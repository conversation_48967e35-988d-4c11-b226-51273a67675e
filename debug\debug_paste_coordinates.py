#!/usr/bin/env python3
"""
Debug de la commande PASTE avec coordonnées multiples
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from command_system.command_executor import CommandExecutor
from command_system.unified_command import UnifiedCommand
import numpy as np

def test_paste_parsing():
    """Test du parsing de PASTE [0,1 1,2]"""
    
    print("🔍 Test du parsing de PASTE [0,1 1,2]...")
    
    command_str = "PASTE [0,1 1,2]"
    cmd = UnifiedCommand.parse(command_str)
    
    print(f"Commande parsée: {cmd}")
    print(f"Action: {cmd.action}")
    print(f"Parameters: {cmd.parameters}")
    print(f"Coordinates: {cmd.coordinates}")
    print(f"Raw command: {getattr(cmd, 'raw_command', 'N/A')}")

def test_paste_behavior():
    """Test du comportement de PASTE avec coordonnées multiples"""
    
    print("\n" + "="*60)
    print("🧪 Test du comportement de PASTE...")
    
    executor = CommandExecutor()
    
    # Créer une grille de test
    executor.grid = np.zeros((9, 9), dtype=int)
    executor.height = 9
    executor.width = 9
    
    # Créer un clipboard de test (résultat de MULTIPLY 4 false)
    executor.clipboard = np.array([
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0]
    ])
    executor.clipboard_mask = np.ones((8, 8), dtype=bool)
    
    print("📋 Clipboard avant PASTE:")
    print(executor.clipboard)
    
    print("\n📋 Grille avant PASTE:")
    print(executor.grid)
    
    # Test PASTE [0,1 1,2]
    print("\n🔄 Exécution de PASTE [0,1 1,2]...")
    
    paste_cmd = UnifiedCommand.parse("PASTE [0,1 1,2]")
    print(f"Coordonnées parsées: {paste_cmd.coordinates}")
    
    success = executor._cmd_paste(paste_cmd)
    print(f"Succès: {success}")
    
    if executor.error:
        print(f"Erreur: {executor.error}")
    
    print("\n📋 Grille après PASTE:")
    print(executor.grid)
    
    return executor.grid

def test_paste_interpretation():
    """Test de différentes interprétations de PASTE [0,1 1,2]"""
    
    print("\n" + "="*60)
    print("🔍 Test de différentes interprétations de PASTE...")
    
    # Interprétation 1: Deux positions séparées [0,1] et [1,2]
    print("\n--- Interprétation 1: Deux positions [0,1] et [1,2] ---")
    
    executor1 = CommandExecutor()
    executor1.grid = np.zeros((9, 9), dtype=int)
    executor1.height = 9
    executor1.width = 9
    
    # Clipboard simple pour test
    executor1.clipboard = np.array([[5, 6], [7, 8]])
    executor1.clipboard_mask = np.ones((2, 2), dtype=bool)
    
    # Simuler PASTE à [0,1] et [1,2]
    positions = [(0, 1), (1, 2)]
    
    for x_dest, y_dest in positions:
        print(f"Collage à position ({x_dest}, {y_dest})")
        clip_height, clip_width = executor1.clipboard.shape
        
        for r in range(clip_height):
            for c in range(clip_width):
                if executor1.clipboard_mask[r, c]:
                    grid_r, grid_c = x_dest + r, y_dest + c
                    if 0 <= grid_r < 9 and 0 <= grid_c < 9:
                        executor1.grid[grid_r, grid_c] = executor1.clipboard[r, c]
    
    print("Résultat:")
    print(executor1.grid[:4, :5])
    
    # Interprétation 2: Rectangle de [0,1] à [1,2]
    print("\n--- Interprétation 2: Rectangle de [0,1] à [1,2] ---")
    
    executor2 = CommandExecutor()
    executor2.grid = np.zeros((9, 9), dtype=int)
    executor2.height = 9
    executor2.width = 9
    
    executor2.clipboard = np.array([[5, 6], [7, 8]])
    executor2.clipboard_mask = np.ones((2, 2), dtype=bool)
    
    # Coller dans le rectangle [0,1] à [1,2]
    x1, y1, x2, y2 = 0, 1, 1, 2
    
    print(f"Collage dans rectangle ({x1},{y1}) à ({x2},{y2})")
    
    # Adapter le clipboard à la taille du rectangle
    rect_height = x2 - x1 + 1  # 2
    rect_width = y2 - y1 + 1   # 2
    
    for r in range(rect_height):
        for c in range(rect_width):
            clip_r = r % executor2.clipboard.shape[0]
            clip_c = c % executor2.clipboard.shape[1]
            executor2.grid[x1 + r, y1 + c] = executor2.clipboard[clip_r, clip_c]
    
    print("Résultat:")
    print(executor2.grid[:4, :5])

def analyze_expected_paste_result():
    """Analyser le résultat attendu pour comprendre le comportement de PASTE"""
    
    print("\n" + "="*60)
    print("🔍 Analyse du résultat attendu de PASTE...")
    
    # Clipboard après MULTIPLY 4 false
    clipboard = np.array([
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0]
    ])
    
    # Résultat attendu
    expected = np.array([
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    
    print("📋 Clipboard (8x8):")
    print(clipboard)
    
    print("\n📋 Résultat attendu (9x9):")
    print(expected)
    
    # Analyser où le clipboard a été collé
    print("\n🔍 Analyse des positions de collage:")
    
    # Chercher les correspondances
    for start_row in range(9):
        for start_col in range(9):
            matches = 0
            total_checked = 0
            
            for r in range(min(8, 9 - start_row)):
                for c in range(min(8, 9 - start_col)):
                    if clipboard[r, c] != 0:  # Seulement les cellules non-zéro
                        total_checked += 1
                        if expected[start_row + r, start_col + c] == clipboard[r, c]:
                            matches += 1
            
            if total_checked > 0 and matches == total_checked:
                print(f"✅ Correspondance parfaite à position ({start_row}, {start_col})")
                print(f"   Cellules vérifiées: {total_checked}, correspondances: {matches}")

if __name__ == "__main__":
    test_paste_parsing()
    test_paste_behavior()
    test_paste_interpretation()
    analyze_expected_paste_result()