/**
 * Index des utilitaires de commandes unifiées
 * Point d'entrée principal pour tous les modules de transition
 */

// Types et interfaces
export * from './commandTypes';

// Génération et parsing de commandes
export {
    parseUnifiedCommand,
    parseCoordinates,
    parseCoordinateBlocks,
    parseUnifiedCommandForPlayback
} from './commandGenerationUtils';

// Imports pour CommandUtils
import {
    parseUnifiedCommand,
    parseCoordinates,
    parseCoordinateBlocks,
    parseUnifiedCommandForPlayback
} from './commandGenerationUtils';

// Moteur d'optimisation
export {
    optimizeCommandList,
    optimizationRules
} from './commandOptimizationEngine';

import {
    optimizeCommandList
} from './commandOptimizationEngine';

// Utilitaires de regroupement
export {
    formatGroupedCommand,
    formatTransfertCommand,
    parseGroupedCommand,
    parseTransfertCommand,
    canGroupCommands,
    groupCommands,
    applyGroupingRule,
    findGroupingRule,
    groupingRules
} from './commandGroupingUtils';

import {
    formatGroupedCommand,
    formatTransfertCommand,
    canGroupCommands,
    groupCommands
} from './commandGroupingUtils';

// Sélections spéciales
export {
    formatSpecialSelection,
    parseSpecialSelection,
    extractSpecialSelection,
    isCompatibleWithSelectInvert,
    isCompatibleWithSelectColor,
    isIncompatibleWithSpecialSelections,
    getAvailableActionsForSelection
} from './specialSelectionUtils';

import {
    formatSpecialSelection,
    extractSpecialSelection,
    isCompatibleWithSelectInvert,
    isCompatibleWithSelectColor,
    isIncompatibleWithSpecialSelections
} from './specialSelectionUtils';

// Fonctions utilitaires principales
export const CommandUtils = {
    // Parsing
    parse: parseUnifiedCommand,
    parseCoords: parseCoordinates,
    parseCoordBlocks: parseCoordinateBlocks,
    parseForPlayback: parseUnifiedCommandForPlayback,
    
    // Grouping
    formatGrouped: formatGroupedCommand,
    formatTransfert: formatTransfertCommand,
    canGroup: canGroupCommands,
    group: groupCommands,
    
    // Optimization
    optimize: optimizeCommandList,
    
    // Special Selections
    formatSpecialSelection,
    extractSpecial: extractSpecialSelection,
    
    // Compatibility
    isCompatibleWithInvert: isCompatibleWithSelectInvert,
    isCompatibleWithColor: isCompatibleWithSelectColor,
    isIncompatible: isIncompatibleWithSpecialSelections
};

// Configuration par défaut
export const DEFAULT_PARSE_CONFIG = {
    strictMode: false,
    allowLegacyFormat: true,
    validateCoordinates: true
};

export const DEFAULT_OPTIMIZATION_CONFIG = {
    enableGrouping: true,
    enableRedundantRemoval: true,
    enableSpecialSelectionIntegration: true,
    maxOptimizationPasses: 3
};

// Fonctions de haut niveau pour l'utilisation courante
export function processCommandList(
    commands: string[],
    options: {
        optimize?: boolean;
        group?: boolean;
        validateCoordinates?: boolean;
    } = {}
): string[] {
    const {
        optimize = true,
        group: _group = true,
        validateCoordinates = true
    } = options;

    let processedCommands = [...commands];

    // Validation optionnelle
    if (validateCoordinates) {
        processedCommands = processedCommands.filter(cmd => {
            try {
                const parsed = parseUnifiedCommand(cmd);
                return parsed.coordinateBlocks.length > 0 || parsed.action === 'END';
            } catch {
                return false;
            }
        });
    }

    // Optimisation
    if (optimize) {
        const optimized = optimizeCommandList(processedCommands);
        processedCommands = optimized.optimizedCommands || processedCommands;
    }

    return processedCommands;
}

export function validateCommandSyntax(command: string): {
    isValid: boolean;
    errors: string[];
    parsed?: any;
} {
    try {
        const parsed = parseUnifiedCommand(command);
        return {
            isValid: true,
            errors: [],
            parsed
        };
    } catch (error) {
        return {
            isValid: false,
            errors: [error instanceof Error ? error.message : 'Unknown parsing error']
        };
    }
}

export async function convertLegacyCommand(legacyCommand: string): Promise<string> {
    try {
        const parsed = await parseUnifiedCommandForPlayback(legacyCommand);
        // Reconstruction simple de la commande
        let command = parsed.action;
        if (parsed.parameters) {
            command += ` ${parsed.parameters}`;
        }
        if (parsed.coordinateBlocks.length > 0) {
            const coordsStr = parsed.coordinateBlocks.map(block => `[${block.join(' ')}]`).join(' ');
            command += ` ${coordsStr}`;
        }
        return command;
    } catch {
        return legacyCommand; // Retourne la commande originale si la conversion échoue
    }
}

export function getCommandMetadata(command: string) {
    const validation = validateCommandSyntax(command);
    if (!validation.isValid) {
        return {
            isValid: false,
            action: null,
            hasCoordinates: false,
            isMotif: false,
            isSpecialSelection: false,
            hasParameters: false
        };
    }

    const parsed = validation.parsed;
    return {
        isValid: true,
        action: parsed.action,
        hasCoordinates: parsed.coordinateBlocks.length > 0,
        isMotif: parsed.isMotif,
        isSpecialSelection: parsed.isSpecialSelection,
        hasParameters: !!parsed.parameters,
        coordinateBlockCount: parsed.coordinateBlocks.length,
        totalCoordinates: parsed.coordinateBlocks.reduce((sum, block) => sum + block.length, 0)
    };
}

// Note: Les anciens modules unifiedCommandUtils et commandOptimizationUtils
// ont été remplacés par commandGenerationUtils et commandOptimizationEngine