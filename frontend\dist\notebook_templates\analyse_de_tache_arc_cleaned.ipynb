{"cells": [{"cell_type": "markdown", "source": ["# Analyse de tâche ARC Cleaned\n", "\n", "Ce notebook permet d'analyser une tâche ARC et d'explorer différentes approches pour la résoudre."], "metadata": {}}, {"cell_type": "code", "metadata": {}, "source": ["# Importer le module arc_utils et les bibliothèques nécessaires\n", "import arc_utils\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import json\n", "\n", "# Vérifier que le module est correctement chargé\n", "print(\"Module arc_utils chargé avec succès!\")\n", "\n", "# Fonction pour afficher une grille\n", "def display_grid(grid, title=None):\n", "    plt.figure(figsize=(5, 5))\n", "    plt.imshow(grid, cmap='tab10', vmin=0, vmax=9)\n", "    plt.grid(True, color='black', linestyle='-', linewidth=1)\n", "    plt.xticks(np.arange(-0.5, len(grid[0]), 1), [])\n", "    plt.yticks(np.arange(-0.5, len(grid), 1), [])\n", "    \n", "    # Ajouter les valeurs dans les cellules\n", "    for i in range(len(grid)):\n", "        for j in range(len(grid[0])):\n", "            plt.text(j, i, str(grid[i][j]), ha='center', va='center', color='white', fontweight='bold')\n", "    \n", "    if title:\n", "        plt.title(title)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Fonction pour envoyer des commandes à l'éditeur d'automatisation\n", "def send_commands(commands):\n", "    try:\n", "        # Importer le module js pour interagir avec JavaScript\n", "        import js\n", "        \n", "        # Convertir les commandes en JSON\n", "        commands_json = json.dumps(commands)\n", "        \n", "        # Envoyer les commandes au frontend\n", "        if hasattr(js.window.parent, 'notebookCommandsCallback'):\n", "            js.window.parent.notebookCommandsCallback(commands_json)\n", "            print('Commandes envoyées au frontend')\n", "        else:\n", "            print('Callback pour les commandes non disponible dans le frontend')\n", "    except Exception as e:\n", "        print(f'Erreur lors de l\\'envoi des commandes au frontend: {e}')"], "outputs": []}, {"cell_type": "code", "metadata": {}, "source": ["# Importer le module arc_utils et les bibliothèques nécessaires", "import arc_utils", "import numpy as np", "import matplotlib.pyplot as plt", "import json", "", "# Vérifier que le module est correctement chargé", "print(\"Module arc_utils chargé avec succès!\")", "", "# Fonction pour afficher une grille", "def display_grid(grid, title=None):", "    plt.figure(figsize=(5, 5))", "    plt.imshow(grid, cmap='tab10', vmin=0, vmax=9)", "    plt.grid(True, color='black', linestyle='-', linewidth=1)", "    plt.xticks(np.arange(-0.5, len(grid[0]), 1), [])", "    plt.yticks(np.arange(-0.5, len(grid), 1), [])", "    ", "    # Ajouter les valeurs dans les cellules", "    for i in range(len(grid)):", "        for j in range(len(grid[0])):", "            plt.text(j, i, str(grid[i][j]), ha='center', va='center', color='white', fontweight='bold')", "    ", "    if title:", "        plt.title(title)", "    plt.tight_layout()", "    plt.show()", "", "# Fonction pour envoyer des commandes à l'éditeur d'automatisation", "def send_commands(commands):", "    try:", "        # Importer le module js pour interagir avec JavaScript", "        import js", "        ", "        # Convertir les commandes en JSON", "        commands_json = json.dumps(commands)", "        ", "        # Envoyer les commandes au frontend", "        if hasattr(js.window.parent, 'notebookCommandsCallback'):", "            js.window.parent.notebookCommandsCallback(commands_json)", "            print('Commandes envoyées au frontend')", "        else:", "            print('Callback pour les commandes non disponible dans le frontend')", "    except Exception as e:", "        print(f'Erreur lors de l\\'envoi des commandes au frontend: {e}')"], "outputs": []}, {"cell_type": "code", "source": ["# Variables pour stocker les données de la tâche", "task_id = None", "train_input = []", "train_output = []", "test_input = []", "train_examples = []", "test_example = None", "", "# Dan<PERSON>, les données de la tâche sont injectées directement par PythonExecutor.ts", "# Vérifier si les variables globales task_id, train_input, train_output et test_input existent déjà", "import sys", "if 'task_id' in globals() and task_id is not None:", "    print(f\"Tâche ARC chargée: {task_id}\")", "    if 'train_input' in globals() and 'train_output' in globals() and len(train_input) == len(train_output):", "        train_examples = [{'input': inp, 'output': out} for inp, out in zip(train_input, train_output)]", "        print(f\"Exemples d'entraînement: {len(train_examples)}\")", "    if 'test_input' in globals() and len(test_input) > 0:", "        test_example = {'input': test_input[0]}", "        print(f\"Exemples de test: {len(test_input)}\")", "else:", "    # Si les données ne sont pas injectées, essayer de les charger depuis arcdata", "    # Utiliser un ID de tâche par défaut pour les tests", "    default_task_id = \"1190e5a7\"", "    print(f\"Tentative de chargement de la tâche {default_task_id} depuis arcdata...\")", "    try:", "        # Vérifier si nous sommes dans Pyodide ou dans un environnement Jupyter normal", "        if 'js' in sys.modules:", "            # Nous sommes dans Pyodide, utiliser la fonction get_current_task", "            import js", "            if hasattr(js.window.parent, 'currentTask'):", "                task_data = js.window.parent.currentTask", "                task_id = js.window.parent.taskName or default_task_id", "                ", "                # Convertir les données JavaScript en Python", "                train_data = []", "                for pair in task_data.train:", "                    train_data.append({", "                        'input': pair.input,", "                        'output': pair.output", "                    })", "                ", "                test_data = []", "                for pair in task_data.test:", "                    test_data.append({", "                        'input': pair.input", "                    })", "                ", "                # Extraire les données d'entrée et de sortie", "                train_input = [pair['input'] for pair in train_data]", "                train_output = [pair['output'] for pair in train_data]", "                test_input = [pair['input'] for pair in test_data]", "                ", "                # <PERSON><PERSON><PERSON> les exemples", "                train_examples = [{'input': inp, 'output': out} for inp, out in zip(train_input, train_output)]", "                if test_input and len(test_input) > 0:", "                    test_example = {'input': test_input[0]}", "                ", "                print(f\"Tâche {task_id} chargée avec succès depuis le frontend\")", "            else:", "                print(\"Aucune tâche disponible dans le frontend\")", "        else:", "            # Nous sommes dans un environnement Jupyter normal, utiliser arc_utils.load_task", "            task_data = arc_utils.load_task(default_task_id)", "            if task_data:", "                task_id = default_task_id", "                train_input = [pair[\"input\"] for pair in task_data[\"train\"]]", "                train_output = [pair[\"output\"] for pair in task_data[\"train\"]]", "                test_input = [pair[\"input\"] for pair in task_data[\"test\"]]", "                train_examples = [{'input': inp, 'output': out} for inp, out in zip(train_input, train_output)]", "                if test_input and len(test_input) > 0:", "                    test_example = {'input': test_input[0]}", "                print(f\"Tâche {task_id} chargée avec succès depuis arcdata\")", "    except Exception as e:", "        print(f\"Erreur lors du chargement de la tâche: {e}\")", "", "# Afficher un résumé", "print(f\"Nombre d'exemples d'entraînement: {len(train_examples)}\")", "print(f\"Exemple de test disponible: {test_example is not None}\")"], "metadata": {}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonctions d'analyse avancées\n", "\nCes fonctions permettent d'analyser en détail les grilles et les transformations."]}, {"cell_type": "code", "metadata": {}, "source": ["# Fonctions d'analyse avancées", "", "def analyze_patterns(grid):", "    \"\"\"Analyse les patterns dans une grille\"\"\"", "    if isinstance(grid, list):", "        grid = np.array(grid)", "", "    # Dimensions de la grille", "    height, width = grid.shape", "    print(f\"Dimensions: {height}x{width}\")", "", "    # Valeurs uniques et leur fréquence", "    values, counts = np.unique(grid, return_counts=True)", "    print(\"\\nValeurs uniques et leur fréquence:\")", "    for val, count in zip(values, counts):", "        print(f\"  Valeur {val}: {count} occurrences ({count/(height*width)*100:.1f}%)\")", "", "    # Recherche de symétries", "    print(\"\\nAnalyse des symétries:\")", "", "    # Symétrie horizontale", "    is_h_symmetric = np.array_equal(grid, np.flipud(grid))", "    print(f\"  Symétrie horizontale: {'Oui' if is_h_symmetric else 'Non'}\")", "", "    # Symétrie verticale", "    is_v_symmetric = np.array_equal(grid, np.fliplr(grid))", "    print(f\"  Symétrie verticale: {'Oui' if is_v_symmetric else 'Non'}\")", "", "    # <PERSON><PERSON><PERSON><PERSON><PERSON> diagonale (si la grille est carrée)", "    if height == width:", "        is_diag_symmetric = np.array_equal(grid, grid.T)", "        print(f\"  Symétrie diagonale: {'Oui' if is_diag_symmetric else 'Non'}\")", "", "    # Recherche de motifs répétitifs", "    print(\"\\nRecherche de motifs répétitifs:\")", "", "    # Motifs 2x2", "    if height >= 2 and width >= 2:", "        patterns_2x2 = {}", "        for y in range(height-1):", "            for x in range(width-1):", "                pattern = tuple(grid[y:y+2, x:x+2].flatten())", "                patterns_2x2[pattern] = patterns_2x2.get(pattern, 0) + 1", "", "        # Afficher les motifs les plus fréquents", "        if patterns_2x2:", "            most_common = sorted(patterns_2x2.items(), key=lambda x: x[1], reverse=True)[:3]", "            print(\"  Motifs 2x2 les plus fréquents:\")", "            for pattern, count in most_common:", "                print(f\"    {pattern}: {count} occurrences\")", "", "    return {", "        \"dimensions\": (height, width),", "        \"unique_values\": list(zip(values.tolist(), counts.tolist())),", "        \"symmetries\": {", "            \"horizontal\": is_h_symmetric,", "            \"vertical\": is_v_symmetric,", "            \"diagonal\": is_diag_symmetric if height == width else None", "        }", "    }", "", "def analyze_transformations(input_grid, output_grid):", "    \"\"\"Analyse les transformations entre deux grilles\"\"\"", "    if isinstance(input_grid, list):", "        input_grid = np.array(input_grid)", "    if isinstance(output_grid, list):", "        output_grid = np.array(output_grid)", "", "    # Vérifier si les dimensions sont les mêmes", "    if input_grid.shape != output_grid.shape:", "        print(f\"Les dimensions sont différentes: {input_grid.shape} -> {output_grid.shape}\")", "        return None", "", "    height, width = input_grid.shape", "", "    # Compter les cellules modifiées", "    diff = (input_grid != output_grid)", "    num_diff = np.sum(diff)", "    print(f\"Nombre de cellules modifiées: {num_diff} sur {input_grid.size} ({num_diff/input_grid.size*100:.1f}%)\")", "", "    # Analyser les valeurs", "    input_values = np.unique(input_grid)", "    output_values = np.unique(output_grid)", "    print(f\"Valeurs dans l'entrée: {input_values}\")", "    print(f\"Valeurs dans la sortie: {output_values}\")", "", "    # Analyser les transformations par valeur", "    print(\"\\nTransformations par valeur:\")", "    transformations = {}", "    for val in input_values:", "        mask = (input_grid == val)", "        if np.sum(mask) > 0:", "            output_vals, counts = np.unique(output_grid[mask], return_counts=True)", "            transformations[int(val)] = {int(o_val): int(count) for o_val, count in zip(output_vals, counts)}", "", "            print(f\"  Valeur {val} -> \", end=\"\")", "            for o_val, count in zip(output_vals, counts):", "                print(f\"{o_val}: {count} ({count/np.sum(mask)*100:.1f}%), \", end=\"\")", "            print()", "", "    # Vérifier les transformations courantes", "    print(\"\\nVérification des transformations courantes:\")", "", "    # Rotation de 90°", "    rot90 = np.rot90(input_grid)", "    is_rot90 = np.array_equal(rot90, output_grid)", "    print(f\"  Rotation 90°: {'Oui' if is_rot90 else 'Non'}\")", "", "    # Rotation de 180°", "    rot180 = np.rot90(input_grid, 2)", "    is_rot180 = np.array_equal(rot180, output_grid)", "    print(f\"  Rotation 180°: {'Oui' if is_rot180 else 'Non'}\")", "", "    # Rotation de 270°", "    rot270 = np.rot90(input_grid, 3)", "    is_rot270 = np.array_equal(rot270, output_grid)", "    print(f\"  Rotation 270°: {'Oui' if is_rot270 else 'Non'}\")", "", "    # Miroir horizontal", "    flip_h = np.flipud(input_grid)", "    is_flip_h = np.array_equal(flip_h, output_grid)", "    print(f\"  Miroir horizontal: {'Oui' if is_flip_h else 'Non'}\")", "", "    # Miroir vertical", "    flip_v = np.fliplr(input_grid)", "    is_flip_v = np.array_equal(flip_v, output_grid)", "    print(f\"  Miroir vertical: {'Oui' if is_flip_v else 'Non'}\")", "", "    # Transposition (si la grille est carrée)", "    if height == width:", "        transpose = input_grid.T", "        is_transpose = np.array_equal(transpose, output_grid)", "        print(f\"  Transposition: {'<PERSON><PERSON>' if is_transpose else 'Non'}\")", "", "    # Afficher une grille des différences", "    print(\"\\nGrille des différences (cellules modifiées):\")", "    diff_grid = np.zeros_like(input_grid)", "    diff_grid[diff] = output_grid[diff]", "    display_grid(diff_grid, \"Différences\")", "", "    return {", "        \"num_diff\": int(num_diff),", "        \"diff_percentage\": float(num_diff/input_grid.size*100),", "        \"transformations\": transformations,", "        \"common_transformations\": {", "            \"rotation_90\": is_rot90,", "            \"rotation_180\": is_rot180,", "            \"rotation_270\": is_rot270,", "            \"flip_horizontal\": is_flip_h,", "            \"flip_vertical\": is_flip_v,", "            \"transpose\": is_transpose if height == width else None", "        }", "    }", "", "def detect_objects(grid, background=0):", "    \"\"\"Détecte les objets dans une grille\"\"\"", "    if isinstance(grid, list):", "        grid = np.array(grid)", "", "    height, width = grid.shape", "", "    # C<PERSON>er une grille de labels pour les objets", "    labels = np.zeros_like(grid)", "    next_label = 1", "", "    # Fonction pour étiqueter un objet de manière récursive", "    def label_object(y, x, label):", "        if y < 0 or y >= height or x < 0 or x >= width:", "            return 0  # Hors limites", "", "        if grid[y, x] == background or labels[y, x] != 0:", "            return 0  # <PERSON><PERSON> ou d<PERSON><PERSON><PERSON>", "", "        # Étiqueter la cellule", "        labels[y, x] = label", "        size = 1", "", "        # Éti<PERSON>er les voisins (4-connexité)", "        size += label_object(y-1, x, label)  # Haut", "        size += label_object(y+1, x, label)  # Bas", "        size += label_object(y, x-1, label)  # Gauche", "        size += label_object(y, x+1, label)  # Droite", "", "        return size", "", "    # Parcourir la grille et étiqueter les objets", "    objects = {}", "    for y in range(height):", "        for x in range(width):", "            if grid[y, x] != background and labels[y, x] == 0:", "                size = label_object(y, x, next_label)", "                objects[next_label] = {", "                    \"value\": int(grid[y, x]),", "                    \"size\": size,", "                    \"label\": next_label", "                }", "                next_label += 1", "", "    print(f\"Nombre d'objets détectés: {len(objects)}\")", "", "    # Afficher les informations sur les objets", "    if objects:", "        print(\"\\nInformations sur les objets:\")", "        for label, obj in objects.items():", "            print(f\"  Objet {label}: valeur {obj['value']}, taille {obj['size']}\")", "", "    # C<PERSON>er une grille colorée pour visualiser les objets", "    if objects:", "        print(\"\\nVisualisation des objets:\")", "        # Utiliser une palette de couleurs différente pour chaque objet", "        object_grid = np.zeros_like(grid)", "        for y in range(height):", "            for x in range(width):", "                if labels[y, x] > 0:", "                    object_grid[y, x] = labels[y, x]", "", "        display_grid(object_grid, \"Objets détectés\")", "", "    return objects"], "outputs": []}, {"cell_type": "code", "source": ["# Exemple d'utilisation des fonctions d'analyse avancées", "if train_examples:", "    example = train_examples[0]", "    ", "    print(\"Analyse des patterns dans la grille d'entrée:\")", "    analyze_patterns(example['input'])", "    ", "    print(\"\\nAnalyse des transformations entre l'entrée et la sortie:\")", "    analyze_transformations(example['input'], example['output'])", "    ", "    print(\"\\nDétection des objets dans la grille d'entrée:\")", "    detect_objects(example['input'])", ""], "metadata": {}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 2. Visualisation des exemples\n", "\n", "Affichons les exemples d'entraînement pour comprendre la tâche."], "metadata": {}}, {"cell_type": "code", "source": ["# Fonction pour afficher une grille", "def display_grid(grid, title=None):", "    plt.figure(figsize=(5, 5))", "    plt.imshow(grid, cmap='tab10', vmin=0, vmax=9)", "    plt.grid(True, color='black', linestyle='-', linewidth=1)", "    plt.xticks(np.arange(-0.5, len(grid[0]), 1), [])", "    plt.yticks(np.arange(-0.5, len(grid), 1), [])", "    ", "    # Ajouter les valeurs dans les cellules", "    for i in range(len(grid)):", "        for j in range(len(grid[0])):", "            plt.text(j, i, str(grid[i][j]), ha='center', va='center', color='white', fontweight='bold')", "    ", "    if title:", "        plt.title(title)", "    plt.tight_layout()", "    plt.show()", "", "# Afficher les exemples d'entraînement", "for i, example in enumerate(train_examples):", "    print(f\"\\nExemple d'entraînement {i+1}:\")", "    print(\"Input:\")", "    display_grid(example[\"input\"], f\"Entrée {i+1}\")", "    print(\"Output:\")", "    display_grid(example[\"output\"], f\"Sortie {i+1}\")"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 3. <PERSON><PERSON><PERSON> des exemples\n", "\n", "Analysons les exemples pour comprendre la transformation."], "metadata": {}}, {"cell_type": "code", "source": ["# Fonction pour analyser les différences entre l'entrée et la sortie", "def analyze_differences(input_grid, output_grid):", "    if isinstance(input_grid, list):", "        input_grid = np.array(input_grid)", "    if isinstance(output_grid, list):", "        output_grid = np.array(output_grid)", "    ", "    # Vérifier si les dimensions sont les mêmes", "    if input_grid.shape != output_grid.shape:", "        print(f\"Les dimensions sont différentes: {input_grid.shape} -> {output_grid.shape}\")", "        return", "    ", "    # Compter les cellules modifiées", "    diff = (input_grid != output_grid)", "    num_diff = np.sum(diff)", "    print(f\"Nombre de cellules modifiées: {num_diff} sur {input_grid.size} ({num_diff/input_grid.size*100:.1f}%)\")", "    ", "    # Analyser les valeurs", "    input_values = np.unique(input_grid)", "    output_values = np.unique(output_grid)", "    print(f\"Valeurs dans l'entrée: {input_values}\")", "    print(f\"Valeurs dans la sortie: {output_values}\")", "    ", "    # Afficher une grille des différences", "    diff_grid = np.zeros_like(input_grid)", "    diff_grid[diff] = output_grid[diff]", "    print(\"\\nGrille des différences (cellules modifiées):\")", "    display_grid(diff_grid, \"Différences\")", "", "# Analyser les différences pour chaque exemple", "for i, example in enumerate(train_examples):", "    print(f\"\\nAnalyse de l'exemple {i+1}:\")", "    analyze_differences(example[\"input\"], example[\"output\"])"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 4. Visualisation de l'exemple de test\n", "\n", "Affichons l'exemple de test."], "metadata": {}}, {"cell_type": "code", "source": ["# Appliquer la solution à l'exemple de test\n", "if test_example:\n", "    # R<PERSON><PERSON><PERSON> la tâche\n", "    test_solution = solve_task(test_example[\"input\"])\n", "    \n", "    print(\"Solution proposée pour l'exemple de test:\")\n", "    display_grid(test_solution, \"Solution proposée\")\n", "    \n", "    # Générer les commandes pour la solution en utilisant arc_utils\n", "    commands = arc_utils.generate_commands_from_grid(test_solution)\n", "    \n", "    print(\"\\nCommandes générées pour la solution:\")\n", "    for cmd in commands:\n", "        print(f\"  {cmd}\")\n", "    \n", "    # Envoyer les commandes à l'éditeur d'automatisation\n", "    send_commands(commands)\n", "    \n", "    # Valider les commandes avec arc_utils\n", "    validation_result = arc_utils.validate_commands(commands)\n", "    if validation_result['valid']:\n", "        print(\"Les commandes sont valides.\")\n", "    else:\n", "        print(f\"Les commandes ne sont pas valides: {validation_result['errors']}\")\n", "else:\n", "    print(\"Aucun exemple de test disponible.\")"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 5. Formulation d'hypothèses\n", "\n", "Formulons des hypothèses sur la transformation à appliquer."], "metadata": {}}, {"cell_type": "code", "source": ["# Espace pour formuler des hypothèses\n", "# Exemple: La transformation consiste à...\n", "\n", "# Hypothèse 1: ...\n", "# Hypothèse 2: ...\n", "# Hypothèse 3: ...\n", "\n", "# Choisir l'hypothèse la plus probable\n", "selected_hypothesis = \"Hypothèse X: ...\"\n", "print(f\"Hypothèse sélectionnée: {selected_hypothesis}\")"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 6. Implémentation de la solution\n", "\n", "Implémentons la solution basée sur l'hypothèse sélectionnée."], "metadata": {}}, {"cell_type": "code", "source": ["# Fonction pour résoudre la tâche", "def solve_task(input_grid):", "    if isinstance(input_grid, list):", "        input_grid = np.array(input_grid)", "    ", "    # TODO: Implémenter la logique de résolution ici", "    # Exemple simple: copier l'entrée", "    output_grid = input_grid.copy()", "    ", "    return output_grid", "", "# Tester la solution sur les exemples d'entraînement", "for i, example in enumerate(train_examples):", "    print(f\"\\nTest de la solution sur l'exemple {i+1}:\")", "    ", "    # Appliquer la solution", "    predicted_output = solve_task(example[\"input\"])", "    ", "    # Afficher la sortie prédite", "    print(\"Sortie prédite:\")", "    display_grid(predicted_output, \"Prédiction\")", "    ", "    # Afficher la sortie attendue", "    print(\"Sortie attendue:\")", "    display_grid(example[\"output\"], \"Attendu\")", "    ", "    # Vérifier si la prédiction est correcte", "    is_correct = np.array_equal(predicted_output, np.array(example[\"output\"]))", "    print(f\"Prédiction correcte: {is_correct}\")"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 7. Application à l'exemple de test\n", "\n", "Appliquons la solution à l'exemple de test."], "metadata": {}}, {"cell_type": "markdown", "metadata": {}, "source": ["# Guide d'utilisation du notebook", "", "Ce notebook vous permet d'analyser et de résoudre des tâches ARC (Abstraction and Reasoning Corpus). Voici comment l'utiliser efficacement :", "", "## Structure du notebook", "", "Le notebook est organisé en sections :", "1. **Configuration et importation** : Importe les bibliothèques nécessaires", "2. **Récupération des données** : Charge les données de la tâche courante", "3. **Visualisation des exemples** : Affiche les grilles d'entrée et de sortie", "4. **Analyse des données** : Fournit des outils pour analyser les patterns et transformations", "5. **Développement de la solution** : Espace pour implémenter votre solution", "6. **Génération de commandes** : Convertit votre solution en commandes pour l'éditeur d'automatisation", "", "## Fonctions disponibles", "", "### Fonctions de base", "- `display_grid(grid, title=None)` : Affiche une grille avec un titre optionnel", "- `arc_utils.load_task(task_id)` : Charge une tâche ARC depuis le répertoire arcdata", "- `arc_utils.validate_commands(commands)` : <PERSON><PERSON> une liste de commandes", "- `arc_utils.execute_commands(commands)` : Exécute une liste de commandes et retourne la grille résultante", "- `arc_utils.generate_commands_from_grid(grid)` : <PERSON>énère des commandes à partir d'une grille", "", "### Fonctions d'analyse avancées", "- `analyze_patterns(grid)` : Analyse les patterns dans une grille (symétries, motifs répétitifs)", "- `analyze_transformations(input_grid, output_grid)` : Analyse les transformations entre deux grilles", "- `detect_objects(grid, background=0)` : Détecte les objets dans une grille", "", "## Commandes d'automatisation", "", "Les commandes d'automatisation permettent de construire une solution. Voici les commandes disponibles :", "", "- `INIT width height` : Initialise une grille vide de dimensions width × height", "- `EDIT x y value` : Définit la valeur de la cellule aux coordonnées (x, y)", "- `COPY x1 y1 x2 y2` : Co<PERSON> la valeur de la cellule (x1, y1) vers la cellule (x2, y2)", "- `FILL x y width height value` : Remplit un rectangle avec une valeur", "- `PROPOSE` : Propose la solution actuelle", "", "## Envoi des commandes à l'éditeur d'automatisation", "", "Pour envoyer des commandes à l'éditeur d'automatisation, utilisez le code suivant :", "", "```python", "# G<PERSON><PERSON><PERSON> les commandes", "commands = arc_utils.generate_commands_from_grid(output_grid)", "", "# Affiche<PERSON> les commandes", "for cmd in commands:", "    print(f\"  {cmd}\")", "", "# Envoyer les commandes à l'éditeur d'automatisation", "# <PERSON><PERSON>, les commandes sont automatiquement envoyées au frontend", "# quand on utilise la fonction send_commands", "import js", "def send_commands(commands):", "    try:", "        # Convertir les commandes en JSON", "        commands_json = json.dumps(commands)", "        ", "        # Envoyer les commandes au frontend", "        if hasattr(js.window.parent, 'notebookCommandsCallback'):", "            js.window.parent.notebookCommandsCallback(commands_json)", "            print('Commandes envoyées au frontend')", "        else:", "            print('Callback pour les commandes non disponible dans le frontend')", "    except Exception as e:", "        print(f'Erreur lors de l\\'envoi des commandes au frontend: {e}')", "", "# A<PERSON>er la fonction", "send_commands(commands)", "```", "", "## Conseils pour résoudre les tâches ARC", "", "1. **Examinez attentivement les exemples** : Comprenez la transformation entre l'entrée et la sortie", "2. **Recherchez des patterns** : Utilisez les fonctions d'analyse pour détecter des patterns", "3. **Testez différentes hypothèses** : Implémentez et testez plusieurs approches", "4. **Validez votre solution** : Vérifiez que votre solution fonctionne sur tous les exemples d'entraînement", "5. **<PERSON><PERSON><PERSON><PERSON> des commandes** : Convertissez votre solution en commandes pour l'éditeur d'automatisation"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "python", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8"}, "kernelspec": {"display_name": "Python (Pyodide)", "language": "python", "name": "python"}}, "nbformat": 4, "nbformat_minor": 4}