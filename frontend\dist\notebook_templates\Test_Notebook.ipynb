{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook de test\n", "\n", "Ce notebook est un test simple pour vérifier que la sortie standard fonctionne correctement."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test simple de print\n", "print(\"Bon<PERSON>r, monde!\")\n", "print(\"Ceci est un test de la sortie standard.\")"]}, {"cell_type": "code", "metadata": {}, "source": ["# Importer le module arc_utils et les bibliothèques nécessaires", "import arc_utils", "import numpy as np", "import matplotlib.pyplot as plt", "import json", "", "# Vérifier que le module est correctement chargé", "print(\"Module arc_utils chargé avec succès!\")"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Récupération des données de la tâche\n", "\nCommençons par récupérer les données de la tâche courante."]}, {"cell_type": "code", "metadata": {}, "source": ["# Variables pour stocker les données de la tâche", "task_id = None", "train_input = []", "train_output = []", "test_input = []", "training_examples = []", "test_example = None", "", "# Essayer de récupérer les données depuis le frontend", "try:", "    display(Javascript(\"\"\"", "    try {", "        // Récupérer les données de la tâche depuis le frontend", "        const taskData = window.parent.currentTask;", "        if (taskData) {", "            // Extraire l'ID de la tâche", "            const taskId = window.parent.taskName || 'unknown';", "            ", "            // Extraire les données d'entrée et de sortie d'entraînement", "            const trainInput = taskData.train.map(pair => pair.input);", "            const trainOutput = taskData.train.map(pair => pair.output);", "            ", "            // Extraire les données d'entrée de test", "            const testInput = taskData.test.map(pair => pair.input);", "            ", "            // Assigner les variables Python", "            IPython.notebook.kernel.execute(`task_id = \"${taskId}\"`);", "            IPython.notebook.kernel.execute(`train_input = ${JSON.stringify(trainInput)}`);", "            IPython.notebook.kernel.execute(`train_output = ${JSON.stringify(trainOutput)}`);", "            IPython.notebook.kernel.execute(`test_input = ${JSON.stringify(testInput)}`);", "            ", "            console.log('Donn<PERSON> de la tâche récupérées avec succès');", "        } else {", "            console.error('Aucune tâche chargée dans le frontend');", "        }", "    } catch (e) {", "        console.error('Erreur lors de la récupération des données de la tâche:', e);", "    }", "    \"\"\"))", "    ", "    # Attendre que les données soient chargées", "    import time", "    time.sleep(1)", "except Exception as e:", "    print(f\"Erreur lors de la récupération des données depuis le frontend: {e}\")", "", "# Si les données n'ont pas été récupérées depuis le frontend, essayer de les charger depuis arcdata", "if not train_input and task_id:", "    print(f\"Tentative de chargement de la tâche {task_id} depuis arcdata...\")", "    try:", "        task_data = arc_utils.load_task(task_id)", "        if task_data:", "            train_input = [pair[\"input\"] for pair in task_data[\"train\"]]", "            train_output = [pair[\"output\"] for pair in task_data[\"train\"]]", "            test_input = [pair[\"input\"] for pair in task_data[\"test\"]]", "            print(f\"Tâche {task_id} chargée avec succès depuis arcdata\")", "    except Exception as e:", "        print(f\"Erreur lors du chargement de la tâche depuis arcdata: {e}\")", "", "# C<PERSON>er les exemples d'entraînement et de test", "if train_input and train_output:", "    training_examples = [{'input': inp, 'output': out} for inp, out in zip(train_input, train_output)]", "    if test_input and len(test_input) > 0:", "        test_example = {'input': test_input[0]}", "", "# Afficher un résumé", "print(f\"Nombre d'exemples d'entraînement: {len(training_examples)}\")", "print(f\"Exemple de test disponible: {test_example is not None}\")"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonctions d'analyse avancées\n", "\nCes fonctions permettent d'analyser en détail les grilles et les transformations."]}, {"cell_type": "code", "metadata": {}, "source": ["# Fonctions d'analyse avancées", "", "def analyze_patterns(grid):", "    \"\"\"Analyse les patterns dans une grille\"\"\"", "    if isinstance(grid, list):", "        grid = np.array(grid)", "", "    # Dimensions de la grille", "    height, width = grid.shape", "    print(f\"Dimensions: {height}x{width}\")", "", "    # Valeurs uniques et leur fréquence", "    values, counts = np.unique(grid, return_counts=True)", "    print(\"\\nValeurs uniques et leur fréquence:\")", "    for val, count in zip(values, counts):", "        print(f\"  Valeur {val}: {count} occurrences ({count/(height*width)*100:.1f}%)\")", "", "    # Recherche de symétries", "    print(\"\\nAnalyse des symétries:\")", "", "    # Symétrie horizontale", "    is_h_symmetric = np.array_equal(grid, np.flipud(grid))", "    print(f\"  Symétrie horizontale: {'Oui' if is_h_symmetric else 'Non'}\")", "", "    # Symétrie verticale", "    is_v_symmetric = np.array_equal(grid, np.fliplr(grid))", "    print(f\"  Symétrie verticale: {'Oui' if is_v_symmetric else 'Non'}\")", "", "    # <PERSON><PERSON><PERSON><PERSON><PERSON> diagonale (si la grille est carrée)", "    if height == width:", "        is_diag_symmetric = np.array_equal(grid, grid.T)", "        print(f\"  Symétrie diagonale: {'Oui' if is_diag_symmetric else 'Non'}\")", "", "    # Recherche de motifs répétitifs", "    print(\"\\nRecherche de motifs répétitifs:\")", "", "    # Motifs 2x2", "    if height >= 2 and width >= 2:", "        patterns_2x2 = {}", "        for y in range(height-1):", "            for x in range(width-1):", "                pattern = tuple(grid[y:y+2, x:x+2].flatten())", "                patterns_2x2[pattern] = patterns_2x2.get(pattern, 0) + 1", "", "        # Afficher les motifs les plus fréquents", "        if patterns_2x2:", "            most_common = sorted(patterns_2x2.items(), key=lambda x: x[1], reverse=True)[:3]", "            print(\"  Motifs 2x2 les plus fréquents:\")", "            for pattern, count in most_common:", "                print(f\"    {pattern}: {count} occurrences\")", "", "    return {", "        \"dimensions\": (height, width),", "        \"unique_values\": list(zip(values.tolist(), counts.tolist())),", "        \"symmetries\": {", "            \"horizontal\": is_h_symmetric,", "            \"vertical\": is_v_symmetric,", "            \"diagonal\": is_diag_symmetric if height == width else None", "        }", "    }", "", "def analyze_transformations(input_grid, output_grid):", "    \"\"\"Analyse les transformations entre deux grilles\"\"\"", "    if isinstance(input_grid, list):", "        input_grid = np.array(input_grid)", "    if isinstance(output_grid, list):", "        output_grid = np.array(output_grid)", "", "    # Vérifier si les dimensions sont les mêmes", "    if input_grid.shape != output_grid.shape:", "        print(f\"Les dimensions sont différentes: {input_grid.shape} -> {output_grid.shape}\")", "        return None", "", "    height, width = input_grid.shape", "", "    # Compter les cellules modifiées", "    diff = (input_grid != output_grid)", "    num_diff = np.sum(diff)", "    print(f\"Nombre de cellules modifiées: {num_diff} sur {input_grid.size} ({num_diff/input_grid.size*100:.1f}%)\")", "", "    # Analyser les valeurs", "    input_values = np.unique(input_grid)", "    output_values = np.unique(output_grid)", "    print(f\"Valeurs dans l'entrée: {input_values}\")", "    print(f\"Valeurs dans la sortie: {output_values}\")", "", "    # Analyser les transformations par valeur", "    print(\"\\nTransformations par valeur:\")", "    transformations = {}", "    for val in input_values:", "        mask = (input_grid == val)", "        if np.sum(mask) > 0:", "            output_vals, counts = np.unique(output_grid[mask], return_counts=True)", "            transformations[int(val)] = {int(o_val): int(count) for o_val, count in zip(output_vals, counts)}", "", "            print(f\"  Valeur {val} -> \", end=\"\")", "            for o_val, count in zip(output_vals, counts):", "                print(f\"{o_val}: {count} ({count/np.sum(mask)*100:.1f}%), \", end=\"\")", "            print()", "", "    # Vérifier les transformations courantes", "    print(\"\\nVérification des transformations courantes:\")", "", "    # Rotation de 90°", "    rot90 = np.rot90(input_grid)", "    is_rot90 = np.array_equal(rot90, output_grid)", "    print(f\"  Rotation 90°: {'Oui' if is_rot90 else 'Non'}\")", "", "    # Rotation de 180°", "    rot180 = np.rot90(input_grid, 2)", "    is_rot180 = np.array_equal(rot180, output_grid)", "    print(f\"  Rotation 180°: {'Oui' if is_rot180 else 'Non'}\")", "", "    # Rotation de 270°", "    rot270 = np.rot90(input_grid, 3)", "    is_rot270 = np.array_equal(rot270, output_grid)", "    print(f\"  Rotation 270°: {'Oui' if is_rot270 else 'Non'}\")", "", "    # Miroir horizontal", "    flip_h = np.flipud(input_grid)", "    is_flip_h = np.array_equal(flip_h, output_grid)", "    print(f\"  Miroir horizontal: {'Oui' if is_flip_h else 'Non'}\")", "", "    # Miroir vertical", "    flip_v = np.fliplr(input_grid)", "    is_flip_v = np.array_equal(flip_v, output_grid)", "    print(f\"  Miroir vertical: {'Oui' if is_flip_v else 'Non'}\")", "", "    # Transposition (si la grille est carrée)", "    if height == width:", "        transpose = input_grid.T", "        is_transpose = np.array_equal(transpose, output_grid)", "        print(f\"  Transposition: {'<PERSON><PERSON>' if is_transpose else 'Non'}\")", "", "    # Afficher une grille des différences", "    print(\"\\nGrille des différences (cellules modifiées):\")", "    diff_grid = np.zeros_like(input_grid)", "    diff_grid[diff] = output_grid[diff]", "    display_grid(diff_grid, \"Différences\")", "", "    return {", "        \"num_diff\": int(num_diff),", "        \"diff_percentage\": float(num_diff/input_grid.size*100),", "        \"transformations\": transformations,", "        \"common_transformations\": {", "            \"rotation_90\": is_rot90,", "            \"rotation_180\": is_rot180,", "            \"rotation_270\": is_rot270,", "            \"flip_horizontal\": is_flip_h,", "            \"flip_vertical\": is_flip_v,", "            \"transpose\": is_transpose if height == width else None", "        }", "    }", "", "def detect_objects(grid, background=0):", "    \"\"\"Détecte les objets dans une grille\"\"\"", "    if isinstance(grid, list):", "        grid = np.array(grid)", "", "    height, width = grid.shape", "", "    # C<PERSON>er une grille de labels pour les objets", "    labels = np.zeros_like(grid)", "    next_label = 1", "", "    # Fonction pour étiqueter un objet de manière récursive", "    def label_object(y, x, label):", "        if y < 0 or y >= height or x < 0 or x >= width:", "            return 0  # Hors limites", "", "        if grid[y, x] == background or labels[y, x] != 0:", "            return 0  # <PERSON><PERSON> ou d<PERSON><PERSON><PERSON>", "", "        # Étiqueter la cellule", "        labels[y, x] = label", "        size = 1", "", "        # Éti<PERSON>er les voisins (4-connexité)", "        size += label_object(y-1, x, label)  # Haut", "        size += label_object(y+1, x, label)  # Bas", "        size += label_object(y, x-1, label)  # Gauche", "        size += label_object(y, x+1, label)  # Droite", "", "        return size", "", "    # Parcourir la grille et étiqueter les objets", "    objects = {}", "    for y in range(height):", "        for x in range(width):", "            if grid[y, x] != background and labels[y, x] == 0:", "                size = label_object(y, x, next_label)", "                objects[next_label] = {", "                    \"value\": int(grid[y, x]),", "                    \"size\": size,", "                    \"label\": next_label", "                }", "                next_label += 1", "", "    print(f\"Nombre d'objets détectés: {len(objects)}\")", "", "    # Afficher les informations sur les objets", "    if objects:", "        print(\"\\nInformations sur les objets:\")", "        for label, obj in objects.items():", "            print(f\"  Objet {label}: valeur {obj['value']}, taille {obj['size']}\")", "", "    # C<PERSON>er une grille colorée pour visualiser les objets", "    if objects:", "        print(\"\\nVisualisation des objets:\")", "        # Utiliser une palette de couleurs différente pour chaque objet", "        object_grid = np.zeros_like(grid)", "        for y in range(height):", "            for x in range(width):", "                if labels[y, x] > 0:", "                    object_grid[y, x] = labels[y, x]", "", "        display_grid(object_grid, \"Objets détectés\")", "", "    return objects"], "outputs": []}, {"cell_type": "code", "metadata": {}, "source": ["# Exemple d'utilisation des fonctions d'analyse avancées\n", "if training_examples:\n", "    example = training_examples[0]\n", "    \n", "    print(\"Analyse des patterns dans la grille d'entrée:\")\n", "    analyze_patterns(example['input'])\n", "    \n", "    print(\"\\nAnalyse des transformations entre l'entrée et la sortie:\")\n", "    analyze_transformations(example['input'], example['output'])\n", "    \n", "    print(\"\\nDétection des objets dans la grille d'entrée:\")\n", "    detect_objects(example['input'])\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test avec une boucle\n", "for i in range(5):\n", "    print(f\"Nombre: {i}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test avec différents types de données\n", "print(\"Chaîne de caractères\")\n", "print(123)\n", "print([1, 2, 3, 4, 5])\n", "print({\"nom\": \"Test\", \"valeur\": 42})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test de l'affichage sans print (dernière expression)\n", "\"<PERSON><PERSON> devrait s'afficher sans print\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test avec matplotlib\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "x = np.linspace(0, 10, 100)\n", "y = np.sin(x)\n", "\n", "plt.figure(figsize=(8, 4))\n", "plt.plot(x, y)\n", "plt.title(\"Test de graphique\")\n", "plt.grid(True)\n", "plt.show()\n", "\n", "print(\"Le graphique devrait s'afficher au-dessus de ce texte.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test de sys.stdout\n", "import sys\n", "\n", "# Vérifier où va la sortie standard\n", "print(\"Sortie standard dirigée vers:\", sys.stdout)\n", "\n", "# Forcer le flush de la sortie standard\n", "print(\"Test avec flush\", flush=True)\n", "\n", "# Écrire directement sur sys.stdout\n", "sys.stdout.write(\"Écrit directement sur sys.stdout\\n\")\n", "sys.stdout.flush()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test avec IPython display\n", "from IPython.display import display, HTML\n", "\n", "print(\"Texte normal avant HTML\")\n", "display(HTML(\"<h3 style='color:red'>Texte HTML en rouge</h3>\"))\n", "print(\"Texte normal après HTML\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Guide d'utilisation du notebook", "", "Ce notebook vous permet d'analyser et de résoudre des tâches ARC (Abstraction and Reasoning Corpus). Voici comment l'utiliser efficacement :", "", "## Structure du notebook", "", "Le notebook est organisé en sections :", "1. **Configuration et importation** : Importe les bibliothèques nécessaires", "2. **Récupération des données** : Charge les données de la tâche courante", "3. **Visualisation des exemples** : Affiche les grilles d'entrée et de sortie", "4. **Analyse des données** : Fournit des outils pour analyser les patterns et transformations", "5. **Développement de la solution** : Espace pour implémenter votre solution", "6. **Génération de commandes** : Convertit votre solution en commandes pour l'éditeur d'automatisation", "", "## Fonctions disponibles", "", "### Fonctions de base", "- `display_grid(grid, title=None)` : Affiche une grille avec un titre optionnel", "- `arc_utils.load_task(task_id)` : Charge une tâche ARC depuis le répertoire arcdata", "- `arc_utils.validate_commands(commands)` : <PERSON><PERSON> une liste de commandes", "- `arc_utils.execute_commands(commands)` : Exécute une liste de commandes et retourne la grille résultante", "- `arc_utils.generate_commands_from_grid(grid)` : <PERSON>énère des commandes à partir d'une grille", "", "### Fonctions d'analyse avancées", "- `analyze_patterns(grid)` : Analyse les patterns dans une grille (symétries, motifs répétitifs)", "- `analyze_transformations(input_grid, output_grid)` : Analyse les transformations entre deux grilles", "- `detect_objects(grid, background=0)` : Détecte les objets dans une grille", "", "## Commandes d'automatisation", "", "Les commandes d'automatisation permettent de construire une solution. Voici les commandes disponibles :", "", "- `INIT width height` : Initialise une grille vide de dimensions width × height", "- `EDIT x y value` : Définit la valeur de la cellule aux coordonnées (x, y)", "- `COPY x1 y1 x2 y2` : Co<PERSON> la valeur de la cellule (x1, y1) vers la cellule (x2, y2)", "- `FILL x y width height value` : Remplit un rectangle avec une valeur", "- `PROPOSE` : Propose la solution actuelle", "", "## Envoi des commandes à l'éditeur d'automatisation", "", "Pour envoyer des commandes à l'éditeur d'automatisation, utilisez le code suivant :", "", "```python", "# G<PERSON><PERSON><PERSON> les commandes", "commands = arc_utils.generate_commands_from_grid(output_grid)", "", "# Affiche<PERSON> les commandes", "for cmd in commands:", "    print(f\"  {cmd}\")", "", "# Envoyer les commandes à l'éditeur d'automatisation", "from IPython.display import display, Javascript", "display(Javascript(\"\"\"", "try {", "    // Récupérer les commandes générées", "    const commands = %s;", "    ", "    // Envoyer les commandes au frontend", "    if (window.parent.notebookCommandsCallback) {", "        window.parent.notebookCommandsCallback(commands);", "        console.log('Commandes envoyées au frontend:', commands);", "    } else {", "        console.error('Callback pour les commandes non disponible dans le frontend');", "    }", "} catch (e) {", "    console.error('<PERSON><PERSON>ur lors de l\\'envoi des commandes au frontend:', e);", "}", "\"\"\" % json.dumps(commands)))", "```", "", "## Conseils pour résoudre les tâches ARC", "", "1. **Examinez attentivement les exemples** : Comprenez la transformation entre l'entrée et la sortie", "2. **Recherchez des patterns** : Utilisez les fonctions d'analyse pour détecter des patterns", "3. **Testez différentes hypothèses** : Implémentez et testez plusieurs approches", "4. **Validez votre solution** : Vérifiez que votre solution fonctionne sur tous les exemples d'entraînement", "5. **<PERSON><PERSON><PERSON><PERSON> des commandes** : Convertissez votre solution en commandes pour l'éditeur d'automatisation"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}, "description": "Notebook de test pour vérifier le fonctionnement de la sortie standard"}, "nbformat": 4, "nbformat_minor": 4}