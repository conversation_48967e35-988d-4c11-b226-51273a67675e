/**
 * Tests pour le système unifié de parsing des coordonnées
 */

import { describe, it, expect } from 'vitest';
import { parseCoordinateBlocks, parseUnifiedCommand } from '../commandGenerationUtils';

describe('Système unifié de parsing des coordonnées', () => {
    
    describe('parseCoordinateBlocks', () => {
        
        it('devrait parser des coordonnées multiples avec parenthèses', () => {
            const result = parseCoordinateBlocks('([0,1 3,1] [0,3 3,5])');
            expect(result).toEqual([
                ['0,1', '3,1'],
                ['0,3', '3,5']
            ]);
        });
        
        it('devrait parser des coordonnées simples avec crochets', () => {
            const result = parseCoordinateBlocks('[1,2 3,4]');
            expect(result).toEqual([
                ['1,2', '3,4']
            ]);
        });
        
        it('devrait parser une cellule simple', () => {
            const result = parseCoordinateBlocks('[2,3]');
            expect(result).toEqual([
                ['2,3']
            ]);
        });
        
    });
    
    describe('parseUnifiedCommand avec coordinateBlocks', () => {
        
        it('devrait parser DELETE avec coordonnées multiples', () => {
            const result = parseUnifiedCommand('DELETE COLUMNS ([0,1 3,1] [0,3 3,5])');
            
            expect(result.action).toBe('DELETE');
            expect(result.parameters).toBe('COLUMNS');
            expect(result.coordinateBlocks).toEqual([
                ['0,1', '3,1'],
                ['0,3', '3,5']
            ]);
        });
        
        it('devrait parser FILL avec coordonnées simples', () => {
            const result = parseUnifiedCommand('FILL 5 [1,2 3,4]');
            
            expect(result.action).toBe('FILL');
            expect(result.parameters).toBe('5');
            expect(result.coordinateBlocks).toEqual([
                ['1,2', '3,4']
            ]);
        });
        
        it('devrait parser EDIT avec cellule simple', () => {
            const result = parseUnifiedCommand('EDIT 7 [2,3]');
            
            expect(result.action).toBe('EDIT');
            expect(result.parameters).toBe('7');
            expect(result.coordinateBlocks).toEqual([
                ['2,3']
            ]);
        });
        
    });
    
});