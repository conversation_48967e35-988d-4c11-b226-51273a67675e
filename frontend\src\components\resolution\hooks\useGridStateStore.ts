// frontend/src/components/resolution/hooks/useGridStateStore.ts
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { Grid } from '../../../lib/grid';
// import { HistoryState } from '../types/resolutionTypes'; // HistoryState n'est plus utilisé directement ici
import { SelectionSnapshot, CommandsSnapshot, HistorySaveMode } from '../types/syncTypes';
import { useGridHistoryStore } from './useGridHistoryStore';
import { useSelection } from './useSelection';
import { useAutomationStore } from './useAutomationStore';
import * as gridUtils from '../utils/gridUtils';

export interface GridStateStoreState {
  grids: (Grid | null)[];
  selectedSymbol: number;
  currentTestIndex: number;

  setGrids: (grids: (Grid | null)[]) => void;
  setGridForTestIndex: (grid: Grid | null, testIndex: number) => void; // Renommé pour clarté
  updateGridAtIndex: (grid: Grid, testIndex: number) => void;
  getCurrentGrid: () => Grid | null;
  setSelectedSymbol: (symbol: number) => void;
  setCurrentTestIndex: (index: number) => void;
  resetStoreState: () => void; // Pour réinitialiser ce store spécifique

  // Actions modifiant la grille et interagissant avec l'historique
  updateCell: (row: number, col: number, value: number) => void;
  resizeGrid: (width: number, height: number) => void;
  resetGrid: (newWidth?: number, newHeight?: number) => void;
  copyFromGrid: (sourceGrid: Grid) => void;
  floodFill: (row: number, col: number, value: number) => void;
  
  // Action pour remplacer la grille actuelle (utilisé par ex. pour coller)
  replaceCurrentGrid: (newGrid: Grid, saveToHistoryMode?: HistorySaveMode) => void;

  // Actions d'undo/redo qui utilisent useGridHistoryStore
  undoLastAction: () => void;
  redoLastAction: () => void;

  // Proposition de grille
  proposeCurrentGrid: () => void;
}

export const useGridStateStore = create<GridStateStoreState>()(
  immer((set, get) => ({
    grids: [],
    selectedSymbol: 0,
    currentTestIndex: 0,

    setGrids: (grids) => {
      set((state) => {
        state.grids = grids.map(g => g ? g.clone() : null);
        // Lors du chargement de nouvelles grilles (ex: chargement de tâche),
        // il faut aussi initialiser/vider l'historique pour chaque grille.
        const historyStore = useGridHistoryStore.getState();
        historyStore.clearAllHistory(); // Ou une boucle pour clearHistoryForTest si on veut être plus fin
        grids.forEach((_, index) => {
            // S'assurer que l'historique est prêt pour cet index
            historyStore.ensureHistoryArraysExist(index);
        });

        // Si currentTestIndex est hors limites, le ramener à 0
        if (state.currentTestIndex >= grids.length && grids.length > 0) {
            state.currentTestIndex = 0;
        } else if (grids.length === 0) {
            state.currentTestIndex = 0; // ou -1 si aucune grille n'est valide
        }
      });
    },

    setGridForTestIndex: (grid, testIndex) => {
      set((state) => {
        const newGrids = [...state.grids];
        while (newGrids.length <= testIndex) {
          newGrids.push(null);
        }
        newGrids[testIndex] = grid ? grid.clone() : null;
        state.grids = newGrids;

        // Réinitialiser l'historique pour ce test spécifique
        useGridHistoryStore.getState().clearHistoryForTest(testIndex);
        // S'assurer que les tableaux d'historique sont prêts pour cet index après clear
        const historyStore = useGridHistoryStore.getState();
        historyStore.ensureHistoryArraysExist(testIndex);
      });
    },
    
    updateGridAtIndex: (grid, testIndex) => {
        console.log(`[GridStateStore] updateGridAtIndex appelé - Index: ${testIndex}, Taille: ${grid.width}x${grid.height}`);
        set(state => {
            if (testIndex >= 0 && testIndex < state.grids.length) {
                state.grids[testIndex] = grid.clone();
                console.log(`[GridStateStore] Grille mise à jour à l'index ${testIndex}`);
            } else {
                // Gérer le cas où l'index est hors limites, peut-être en agrandissant le tableau
                while (state.grids.length <= testIndex) {
                  state.grids.push(null);
                }
                state.grids[testIndex] = grid.clone();
                console.log(`[GridStateStore] Grille créée et mise à jour à l'index ${testIndex}`);
            }
        });
    },

    getCurrentGrid: () => {
      const state = get();
      if (state.currentTestIndex >= 0 && state.currentTestIndex < state.grids.length) {
        return state.grids[state.currentTestIndex];
      }
      return null;
    },

    setSelectedSymbol: (symbol) => {
      set({ selectedSymbol: symbol });
    },

    setCurrentTestIndex: (index) => {
      set((state) => {
        if (index >= 0) { // Permettre index >= state.grids.length pour la création de nouveaux tests
          state.currentTestIndex = index;
          // S'assurer que la grille et l'historique existent pour ce nouvel index
          const historyStore = useGridHistoryStore.getState();
          while (state.grids.length <= index) {
            state.grids.push(null); // Initialiser avec une grille nulle
          }
          historyStore.ensureHistoryArraysExist(index);
        }
      });
    },
    
    resetStoreState: () => {
        set({
            grids: [],
            selectedSymbol: 0,
            currentTestIndex: 0,
        });
        useGridHistoryStore.getState().clearAllHistory();
    },

    updateCell: (row, col, value) => {
      const currentGrid = get().getCurrentGrid();
      if (!currentGrid) return;
      
      const currentValue = currentGrid.getCell(row,col);
      if (currentValue === value) return;

      const historyStore = useGridHistoryStore.getState();
      historyStore.saveGridOnlyHistory(get().currentTestIndex, currentGrid); // Sauvegarde simple pour updateCell

      const newGrid = gridUtils.updateCellLogic(currentGrid, row, col, value);
      get().updateGridAtIndex(newGrid, get().currentTestIndex);
      
      try {
        const automationStore = useAutomationStore.getState();
        if (automationStore.isRecording) {
          const coordinates = [`${row},${col}`];
          automationStore.addUnifiedCommand('EDIT', `${value}`, coordinates);
        }
      } catch (error) {
        console.error('[GridStateStore] Failed to add automation command for EDIT:', error);
      }
    },

    resizeGrid: (width, height) => {
      const currentGrid = get().getCurrentGrid();
      const testIndex = get().currentTestIndex;
      const historyStore = useGridHistoryStore.getState();
      
      // Pour resize, on veut une synchronisation complète
      const selectionSnapshot = useSelection.getState().captureSnapshot();
      const automationStore = useAutomationStore.getState();
      const commands = automationStore.commands[testIndex] || [];
      const commandsSnapshot: CommandsSnapshot = {
        commands: [...commands],
        commandStartIndex: Math.max(0, commands.length - 10), // Exemple, pourrait être ajusté
        commandEndIndex: commands.length,
        timestamp: Date.now(),
        selectionTimestamp: selectionSnapshot.timestamp,
        wasRecording: useSelection.getState().isRecordingCommands,
        currentTestIndex: testIndex,
        version: '2.0',
      };
      
      historyStore.saveWithFullSyncHistory(testIndex, currentGrid, selectionSnapshot, commandsSnapshot);

      const newGrid = gridUtils.resizeGridLogic(currentGrid, width, height);
      get().updateGridAtIndex(newGrid, testIndex);
      
      // La commande RESIZE est généralement ajoutée par l'UI pour avoir le contexte complet.
      // Si on veut l'ajouter ici, il faudrait s'assurer de ne pas la dupliquer.
    },

    resetGrid: (newWidth = 3, newHeight = 3) => {
      const currentGrid = get().getCurrentGrid();
      const testIndex = get().currentTestIndex;
      const historyStore = useGridHistoryStore.getState();

      historyStore.saveGridOnlyHistory(testIndex, currentGrid); // Sauvegarde simple pour reset

      const newGrid = gridUtils.resetGridLogic(newWidth, newHeight);
      get().updateGridAtIndex(newGrid, testIndex);

      try {
        const automationStore = useAutomationStore.getState();
        if (automationStore.isRecording) {
          automationStore.addUnifiedCommand('RESET', `${newWidth}x${newHeight}`);
        }
      } catch (error) {
        console.error('[GridStateStore] Failed to add automation command for RESET:', error);
      }
    },

    copyFromGrid: (sourceGrid) => {
      if (!sourceGrid) return;
      const currentGrid = get().getCurrentGrid();
      const testIndex = get().currentTestIndex;
      const historyStore = useGridHistoryStore.getState();

      historyStore.saveGridOnlyHistory(testIndex, currentGrid);

      const newGrid = gridUtils.copyGridLogic(sourceGrid);
      get().updateGridAtIndex(newGrid, testIndex);

      try {
        const automationStore = useAutomationStore.getState();
        if (automationStore.isRecording) {
          // Remplacer toutes les commandes existantes par INPUT
          automationStore.setCommands(['INPUT']);
        }
      } catch (error) {
        console.error('[GridStateStore] Failed to add automation command for INPUT:', error);
      }
    },

    floodFill: (row, col, value) => {
      const currentGrid = get().getCurrentGrid();
      if (!currentGrid) return;
      
      const currentValue = currentGrid.getCell(row,col);
      if (currentValue === value) return;
      if (row < 0 || row >= currentGrid.height || col < 0 || col >= currentGrid.width) return;

      const historyStore = useGridHistoryStore.getState();
      historyStore.saveGridOnlyHistory(get().currentTestIndex, currentGrid);

      const newGrid = gridUtils.floodFillLogic(currentGrid, row, col, value);
      get().updateGridAtIndex(newGrid, get().currentTestIndex);
      
      try {
        const automationStore = useAutomationStore.getState();
        if (automationStore.isRecording) {
          const coordinates = [`${row},${col}`];
          automationStore.addUnifiedCommand('FLOODFILL', `${value}`, coordinates);
        }
      } catch (error) {
        console.error('[GridStateStore] Failed to add automation command for FLOODFILL:', error);
      }
    },
    
    replaceCurrentGrid: (newGrid, saveToHistoryMode = HistorySaveMode.GRID_ONLY) => {
        console.log(`[GridStateStore] replaceCurrentGrid appelé - Taille: ${newGrid.width}x${newGrid.height}`);
        const currentGrid = get().getCurrentGrid();
        const testIndex = get().currentTestIndex;
        const historyStore = useGridHistoryStore.getState();

        let selectionSnapshot: SelectionSnapshot | null = null;
        let commandsSnapshot: CommandsSnapshot | null = null;

        if (saveToHistoryMode === HistorySaveMode.WITH_SELECTIONS || saveToHistoryMode === HistorySaveMode.FULL_SYNC) {
            try {
                selectionSnapshot = useSelection.getState().captureSnapshot();
            } catch (e) { console.warn("Failed to capture selection snapshot for replaceCurrentGrid", e); }
        }
        if (saveToHistoryMode === HistorySaveMode.FULL_SYNC) {
            try {
                const autoStore = useAutomationStore.getState();
                const cmds = autoStore.commands[testIndex] || [];
                commandsSnapshot = {
                    commands: [...cmds],
                    commandStartIndex: Math.max(0, cmds.length - 10),
                    commandEndIndex: cmds.length,
                    timestamp: Date.now(),
                    selectionTimestamp: selectionSnapshot?.timestamp || Date.now(),
                    wasRecording: useSelection.getState().isRecordingCommands,
                    currentTestIndex: testIndex,
                    version: '2.0',
                };
            } catch (e) { console.warn("Failed to capture commands snapshot for replaceCurrentGrid", e); }
        }
        
        historyStore.saveToHistory(testIndex, currentGrid, saveToHistoryMode, selectionSnapshot, commandsSnapshot);
        get().updateGridAtIndex(newGrid, testIndex);
    },

    undoLastAction: () => {
      const historyStore = useGridHistoryStore.getState();
      const testIndex = get().currentTestIndex;

      if (!historyStore.canUndo(testIndex)) return;

      const currentGrid = get().getCurrentGrid();
      // Capturer l'état actuel pour le passer à `undo` afin qu'il puisse le mettre dans `future`
      const selectionSnapshot = useSelection.getState().captureSnapshot(); // Toujours capturer pour être sûr
      const automationStore = useAutomationStore.getState();
      const commands = automationStore.commands[testIndex] || [];
      const commandsSnapshot: CommandsSnapshot = {
        commands: [...commands],
        commandStartIndex: Math.max(0, commands.length - 10),
        commandEndIndex: commands.length,
        timestamp: Date.now(),
        selectionTimestamp: selectionSnapshot.timestamp,
        wasRecording: useSelection.getState().isRecordingCommands,
        currentTestIndex: testIndex,
        version: '2.0',
      };

      historyStore.undo(
        testIndex,
        currentGrid, // État actuel de la grille
        // _transformationToRestore est préfixé car non utilisé dans cette implémentation
        //,_transformationToRestoreParam
        (gridToRestore, selectionToRestore, commandsToRestore) => {
          if (gridToRestore) {
            get().updateGridAtIndex(gridToRestore, testIndex);
          } else {
             // Gérer le cas où la grille restaurée est nulle, peut-être la définir à null
            const newGrids = [...get().grids];
            newGrids[testIndex] = null;
            set({ grids: newGrids });
          }
          if (selectionToRestore) {
            try {
                const selStore = useSelection.getState();
                const wasRec = selStore.isRecordingCommands;
                if(wasRec) selStore.disableCommandRecording();
                selStore.restoreSnapshot(selectionToRestore);
                if(wasRec) selStore.enableCommandRecording();
            } catch (error) { 
              console.warn("Failed to restore selection snapshot on undo", error); 
            }
          } else {
            // Si pas de snapshot de sélection, vider la sélection pour éviter incohérences
            try { useSelection.getState().clearSelection(); } catch(e) { console.warn("Failed to clearSelection commands snapshot on undo", e); }
          }
          if (commandsToRestore && useAutomationStore.getState().setCommands) {
             try { useAutomationStore.getState().setCommands([...commandsToRestore.commands]); } catch(e) { console.warn("Failed to restore commands snapshot on undo", e); }
          }
          // Gérer transformationToRestore si nécessaire
        },
        selectionSnapshot, // État actuel des sélections
        commandsSnapshot // État actuel des commandes
      );
    },

    redoLastAction: () => {
      const historyStore = useGridHistoryStore.getState();
      const testIndex = get().currentTestIndex;

      if (!historyStore.canRedo(testIndex)) return;

      const currentGrid = get().getCurrentGrid();
      // Capturer l'état actuel pour le passer à `redo`
      const selectionSnapshot = useSelection.getState().captureSnapshot();
      const automationStore = useAutomationStore.getState();
      const commands = automationStore.commands[testIndex] || [];
      const commandsSnapshot: CommandsSnapshot = {
        commands: [...commands],
        commandStartIndex: Math.max(0, commands.length - 10),
        commandEndIndex: commands.length,
        timestamp: Date.now(),
        selectionTimestamp: selectionSnapshot.timestamp,
        wasRecording: useSelection.getState().isRecordingCommands,
        currentTestIndex: testIndex,
        version: '2.0',
      };

      historyStore.redo(
        testIndex,
        currentGrid,
        // _transformationToRestore est préfixé car non utilisé dans cette implémentation
        //,_transformationToRestoreParam
        (gridToRestore, selectionToRestore, commandsToRestore) => {
          if (gridToRestore) {
            get().updateGridAtIndex(gridToRestore, testIndex);
          } else {
            const newGrids = [...get().grids];
            newGrids[testIndex] = null;
            set({ grids: newGrids });
          }
          if (selectionToRestore) {
            try { 
                const selStore = useSelection.getState();
                const wasRec = selStore.isRecordingCommands;
                if(wasRec) selStore.disableCommandRecording();
                selStore.restoreSnapshot(selectionToRestore);
                if(wasRec) selStore.enableCommandRecording();
            } catch (error) { 
              console.warn("Failed to restore selection snapshot on redo", error); 
            }
          } else {
            try { useSelection.getState().clearSelection(); } catch(e) { /* Intentionnellement vide, l'échec du clear est géré au mieux */ }
          }
          if (commandsToRestore && useAutomationStore.getState().setCommands) {
            try { useAutomationStore.getState().setCommands([...commandsToRestore.commands]); } catch(e) { console.warn("Failed to restore commands snapshot on redo", e); }
          }
          // Gérer transformationToRestore si nécessaire
        },
        selectionSnapshot,
        commandsSnapshot
      );
    },
    
    proposeCurrentGrid: () => {
      const currentGrid = get().getCurrentGrid();
      if (!currentGrid) return;
      try {
        const event = new CustomEvent('propose-grid', { detail: { grid: currentGrid } });
        window.dispatchEvent(event);
      } catch (error) {
        console.error(`[GridStateStore] Error proposing grid:`, error);
      }
    },

  }))
);