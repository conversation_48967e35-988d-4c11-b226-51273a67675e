{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#ARC Analyse standardisée\n", "\n", "Ce notebook permet d'analyser une tâche ARC et d'explorer différentes approches pour la résoudre. Il utilise une structure standardisée et des noms de variables cohérents."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importations nécessaires\n", "import arc_utils\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import json\n", "import sys\n", "from IPython.display import display, HTML, Javascript\n", "\n", "# Vérifier que le module arc_utils est correctement chargé\n", "print(\"Module arc_utils chargé avec succès!\")\n", "\n", "# Fonction pour afficher une grille\n", "def display_grid(grid, title=None):\n", "    \"\"\"Affiche une grille ARC avec un titre optionnel.\n", "    \n", "    Args:\n", "        grid (list or numpy.ndarray): La grille à afficher\n", "        title (str, optional): Le titre à afficher au-dessus de la grille\n", "    \"\"\"\n", "    if isinstance(grid, np.ndarray):\n", "        grid = grid.tolist()\n", "        \n", "    plt.figure(figsize=(5, 5))\n", "    plt.imshow(grid, cmap='tab10', vmin=0, vmax=9)\n", "    plt.grid(True, color='black', linestyle='-', linewidth=1)\n", "    plt.xticks(np.arange(-0.5, len(grid[0]), 1), [])\n", "    plt.yticks(np.arange(-0.5, len(grid), 1), [])\n", "    \n", "    # Ajouter les valeurs dans les cellules\n", "    for i in range(len(grid)):\n", "        for j in range(len(grid[0])):\n", "            plt.text(j, i, str(grid[i][j]), ha='center', va='center', color='white', fontweight='bold')\n", "    \n", "    if title:\n", "        plt.title(title)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Fonction pour envoyer des commandes à l'éditeur d'automatisation\n", "def send_commands(commands):\n", "    \"\"\"Envoie des commandes à l'éditeur d'automatisation.\n", "    \n", "    Args:\n", "        commands (list): Liste de commandes à envoyer\n", "    \"\"\"\n", "    try:\n", "        # Importer le module js pour interagir avec JavaScript\n", "        import js\n", "        \n", "        # Convertir les commandes en JSON\n", "        commands_json = json.dumps(commands)\n", "        \n", "        # Envoyer les commandes au frontend\n", "        if hasattr(js.window.parent, 'notebookCommandsCallback'):\n", "            js.window.parent.notebookCommandsCallback(commands_json)\n", "            print('Commandes envoyées au frontend')\n", "        else:\n", "            print('Callback pour les commandes non disponible dans le frontend')\n", "    except Exception as e:\n", "        print(f'Erreur lors de l\\'envoi des commandes au frontend: {e}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Variables pour stocker les données de la tâche\n", "task_id = None\n", "train_input = []\n", "train_output = []\n", "test_input = []\n", "train_examples = []\n", "test_example = None\n", "\n", "# Dan<PERSON>, les données de la tâche sont injectées directement par PythonExecutor.ts\n", "# Vérifier si les variables globales task_id, train_input, train_output et test_input existent déjà\n", "if 'task_id' in globals() and task_id is not None:\n", "    print(f\"Tâche ARC chargée: {task_id}\")\n", "    if 'train_input' in globals() and 'train_output' in globals() and len(train_input) == len(train_output):\n", "        train_examples = [{'input': inp, 'output': out} for inp, out in zip(train_input, train_output)]\n", "        print(f\"Exemples d'entraînement: {len(train_examples)}\")\n", "    if 'test_input' in globals() and len(test_input) > 0:\n", "        test_example = {'input': test_input[0]}\n", "        print(f\"Exemples de test: {len(test_input)}\")\n", "else:\n", "    # Si les données ne sont pas injectées, essayer de les charger depuis arcdata\n", "    # Utiliser un ID de tâche par défaut pour les tests\n", "    default_task_id = \"1190e5a7\"\n", "    print(f\"Tentative de chargement de la tâche {default_task_id} depuis arcdata...\")\n", "    try:\n", "        # Vérifier si nous sommes dans Pyodide ou dans un environnement Jupyter normal\n", "        if 'js' in sys.modules:\n", "            # Nous sommes dans Pyodide, utiliser la fonction get_current_task\n", "            import js\n", "            if hasattr(js.window.parent, 'currentTask'):\n", "                task_data = js.window.parent.currentTask\n", "                task_id = js.window.parent.taskName or default_task_id\n", "                \n", "                # Convertir les données JavaScript en Python\n", "                train_data = []\n", "                for pair in task_data.train:\n", "                    train_data.append({\n", "                        'input': pair.input,\n", "                        'output': pair.output\n", "                    })\n", "                \n", "                test_data = []\n", "                for pair in task_data.test:\n", "                    test_data.append({\n", "                        'input': pair.input\n", "                    })\n", "                \n", "                # Extraire les données d'entrée et de sortie\n", "                train_input = [pair['input'] for pair in train_data]\n", "                train_output = [pair['output'] for pair in train_data]\n", "                test_input = [pair['input'] for pair in test_data]\n", "                \n", "                # <PERSON><PERSON><PERSON> les exemples\n", "                train_examples = [{'input': inp, 'output': out} for inp, out in zip(train_input, train_output)]\n", "                if test_input and len(test_input) > 0:\n", "                    test_example = {'input': test_input[0]}\n", "                \n", "                print(f\"Tâche {task_id} chargée avec succès depuis le frontend\")\n", "            else:\n", "                print(\"Aucune tâche disponible dans le frontend\")\n", "        else:\n", "            # Nous sommes dans un environnement Jupyter normal, utiliser arc_utils.load_task\n", "            task_data = arc_utils.load_task(default_task_id)\n", "            if task_data:\n", "                task_id = default_task_id\n", "                train_input = [pair[\"input\"] for pair in task_data[\"train\"]]\n", "                train_output = [pair[\"output\"] for pair in task_data[\"train\"]]\n", "                test_input = [pair[\"input\"] for pair in task_data[\"test\"]]\n", "                train_examples = [{'input': inp, 'output': out} for inp, out in zip(train_input, train_output)]\n", "                if test_input and len(test_input) > 0:\n", "                    test_example = {'input': test_input[0]}\n", "                print(f\"Tâche {task_id} chargée avec succès depuis arcdata\")\n", "    except Exception as e:\n", "        print(f\"Erreur lors du chargement de la tâche: {e}\")\n", "\n", "# Afficher un résumé\n", "print(f\"Nombre d'exemples d'entraînement: {len(train_examples)}\")\n", "print(f\"Exemple de test disponible: {test_example is not None}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualisation des exemples\n", "\n", "Affichons les exemples d'entraînement pour comprendre la tâche."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Afficher les exemples d'entraînement\n", "for i, example in enumerate(train_examples):\n", "    print(f\"\\nExemple d'entraînement {i+1}:\")\n", "    print(\"Input:\")\n", "    display_grid(example[\"input\"], f\"Entrée {i+1}\")\n", "    print(\"Output:\")\n", "    display_grid(example[\"output\"], f\"Sortie {i+1}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyse des exemples\n", "\n", "Analysons les exemples pour comprendre la transformation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fonction pour analyser les différences entre l'entrée et la sortie\n", "def analyze_differences(input_grid, output_grid):\n", "    \"\"\"Analyse les différences entre une grille d'entrée et une grille de sortie.\n", "    \n", "    Args:\n", "        input_grid (list or numpy.ndarray): La grille d'entrée\n", "        output_grid (list or numpy.ndarray): La grille de sortie\n", "    \"\"\"\n", "    if isinstance(input_grid, list):\n", "        input_grid = np.array(input_grid)\n", "    if isinstance(output_grid, list):\n", "        output_grid = np.array(output_grid)\n", "    \n", "    # Vérifier si les dimensions sont les mêmes\n", "    if input_grid.shape != output_grid.shape:\n", "        print(f\"Les dimensions sont différentes: {input_grid.shape} -> {output_grid.shape}\")\n", "        return\n", "    \n", "    # Compter les cellules modifiées\n", "    diff = (input_grid != output_grid)\n", "    num_diff = np.sum(diff)\n", "    print(f\"Nombre de cellules modifiées: {num_diff} sur {input_grid.size} ({num_diff/input_grid.size*100:.1f}%)\")\n", "    \n", "    # Analyser les valeurs\n", "    input_values = np.unique(input_grid)\n", "    output_values = np.unique(output_grid)\n", "    print(f\"Valeurs dans l'entrée: {input_values}\")\n", "    print(f\"Valeurs dans la sortie: {output_values}\")\n", "    \n", "    # Afficher une grille des différences\n", "    diff_grid = np.zeros_like(input_grid)\n", "    diff_grid[diff] = output_grid[diff]\n", "    print(\"\\nGrille des différences (cellules modifiées):\")\n", "    display_grid(diff_grid, \"Différences\")\n", "\n", "# Analyser les différences pour chaque exemple\n", "for i, example in enumerate(train_examples):\n", "    print(f\"\\nAnalyse de l'exemple {i+1}:\")\n", "    analyze_differences(example[\"input\"], example[\"output\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonctions d'analyse avancées\n", "\n", "Ces fonctions permettent d'analyser en détail les grilles et les transformations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fonctions d'analyse avancées\n", "\n", "def analyze_patterns(grid):\n", "    \"\"\"Analyse les patterns dans une grille\"\"\"\n", "    if isinstance(grid, list):\n", "        grid = np.array(grid)\n", "\n", "    # Dimensions de la grille\n", "    height, width = grid.shape\n", "    print(f\"Dimensions: {height}x{width}\")\n", "\n", "    # Valeurs uniques et leur fréquence\n", "    values, counts = np.unique(grid, return_counts=True)\n", "    print(\"\\nValeurs uniques et leur fréquence:\")\n", "    for val, count in zip(values, counts):\n", "        print(f\"  Valeur {val}: {count} occurrences ({count/(height*width)*100:.1f}%)\")\n", "\n", "    # Recherche de symétries\n", "    print(\"\\nAnalyse des symétries:\")\n", "\n", "    # Symétrie horizontale\n", "    is_h_symmetric = np.array_equal(grid, np.flipud(grid))\n", "    print(f\"  Symétrie horizontale: {'Oui' if is_h_symmetric else 'Non'}\")\n", "\n", "    # Symétrie verticale\n", "    is_v_symmetric = np.array_equal(grid, np.fliplr(grid))\n", "    print(f\"  Symétrie verticale: {'Oui' if is_v_symmetric else 'Non'}\")\n", "\n", "    # <PERSON><PERSON><PERSON><PERSON><PERSON> diagonale (si la grille est carrée)\n", "    if height == width:\n", "        is_diag_symmetric = np.array_equal(grid, grid.T)\n", "        print(f\"  Symétrie diagonale: {'Oui' if is_diag_symmetric else 'Non'}\")\n", "\n", "    # Recherche de motifs répétitifs\n", "    print(\"\\nRecherche de motifs répétitifs:\")\n", "\n", "    # Motifs 2x2\n", "    if height >= 2 and width >= 2:\n", "        patterns_2x2 = {}\n", "        for y in range(height-1):\n", "            for x in range(width-1):\n", "                pattern = tuple(grid[y:y+2, x:x+2].flatten())\n", "                patterns_2x2[pattern] = patterns_2x2.get(pattern, 0) + 1\n", "\n", "        # Afficher les motifs les plus fréquents\n", "        if patterns_2x2:\n", "            most_common = sorted(patterns_2x2.items(), key=lambda x: x[1], reverse=True)[:3]\n", "            print(\"  Motifs 2x2 les plus fréquents:\")\n", "            for pattern, count in most_common:\n", "                print(f\"    {pattern}: {count} occurrences\")\n", "\n", "    return {\n", "        \"dimensions\": (height, width),\n", "        \"unique_values\": list(zip(values.tolist(), counts.tolist())),\n", "        \"symmetries\": {\n", "            \"horizontal\": is_h_symmetric,\n", "            \"vertical\": is_v_symmetric,\n", "            \"diagonal\": is_diag_symmetric if height == width else None\n", "        }\n", "    }\n", "\n", "def analyze_transformations(input_grid, output_grid):\n", "    \"\"\"Analyse les transformations entre deux grilles\"\"\"\n", "    if isinstance(input_grid, list):\n", "        input_grid = np.array(input_grid)\n", "    if isinstance(output_grid, list):\n", "        output_grid = np.array(output_grid)\n", "\n", "    # Vérifier si les dimensions sont les mêmes\n", "    if input_grid.shape != output_grid.shape:\n", "        print(f\"Les dimensions sont différentes: {input_grid.shape} -> {output_grid.shape}\")\n", "        return None\n", "\n", "    height, width = input_grid.shape\n", "\n", "    # Compter les cellules modifiées\n", "    diff = (input_grid != output_grid)\n", "    num_diff = np.sum(diff)\n", "    print(f\"Nombre de cellules modifiées: {num_diff} sur {input_grid.size} ({num_diff/input_grid.size*100:.1f}%)\")\n", "\n", "    # Analyser les valeurs\n", "    input_values = np.unique(input_grid)\n", "    output_values = np.unique(output_grid)\n", "    print(f\"Valeurs dans l'entrée: {input_values}\")\n", "    print(f\"Valeurs dans la sortie: {output_values}\")\n", "\n", "    # Analyser les transformations par valeur\n", "    print(\"\\nTransformations par valeur:\")\n", "    transformations = {}\n", "    for val in input_values:\n", "        mask = (input_grid == val)\n", "        if np.sum(mask) > 0:\n", "            output_vals, counts = np.unique(output_grid[mask], return_counts=True)\n", "            transformations[int(val)] = {int(o_val): int(count) for o_val, count in zip(output_vals, counts)}\n", "\n", "            print(f\"  Valeur {val} -> \", end=\"\")\n", "            for o_val, count in zip(output_vals, counts):\n", "                print(f\"{o_val}: {count} ({count/np.sum(mask)*100:.1f}%), \", end=\"\")\n", "            print()\n", "\n", "    # Vérifier les transformations courantes\n", "    print(\"\\nVérification des transformations courantes:\")\n", "\n", "    # Rotation de 90°\n", "    rot90 = np.rot90(input_grid)\n", "    is_rot90 = np.array_equal(rot90, output_grid)\n", "    print(f\"  Rotation 90°: {'Oui' if is_rot90 else 'Non'}\")\n", "\n", "    # Rotation de 180°\n", "    rot180 = np.rot90(input_grid, 2)\n", "    is_rot180 = np.array_equal(rot180, output_grid)\n", "    print(f\"  Rotation 180°: {'Oui' if is_rot180 else 'Non'}\")\n", "\n", "    # Rotation de 270°\n", "    rot270 = np.rot90(input_grid, 3)\n", "    is_rot270 = np.array_equal(rot270, output_grid)\n", "    print(f\"  Rotation 270°: {'Oui' if is_rot270 else 'Non'}\")\n", "\n", "    # Miroir horizontal\n", "    flip_h = np.flipud(input_grid)\n", "    is_flip_h = np.array_equal(flip_h, output_grid)\n", "    print(f\"  Miroir horizontal: {'Oui' if is_flip_h else 'Non'}\")\n", "\n", "    # Miroir vertical\n", "    flip_v = np.fliplr(input_grid)\n", "    is_flip_v = np.array_equal(flip_v, output_grid)\n", "    print(f\"  Miroir vertical: {'Oui' if is_flip_v else 'Non'}\")\n", "\n", "    # Transposition (si la grille est carrée)\n", "    is_transpose = False\n", "    if height == width:\n", "        transpose = input_grid.T\n", "        is_transpose = np.array_equal(transpose, output_grid)\n", "        print(f\"  Transposition: {'<PERSON><PERSON>' if is_transpose else 'Non'}\")\n", "\n", "    # Afficher une grille des différences\n", "    print(\"\\nGrille des différences (cellules modifiées):\")\n", "    diff_grid = np.zeros_like(input_grid)\n", "    diff_grid[diff] = output_grid[diff]\n", "    display_grid(diff_grid, \"Différences\")\n", "\n", "    return {\n", "        \"num_diff\": int(num_diff),\n", "        \"diff_percentage\": float(num_diff/input_grid.size*100),\n", "        \"transformations\": transformations,\n", "        \"common_transformations\": {\n", "            \"rotation_90\": is_rot90,\n", "            \"rotation_180\": is_rot180,\n", "            \"rotation_270\": is_rot270,\n", "            \"flip_horizontal\": is_flip_h,\n", "            \"flip_vertical\": is_flip_v,\n", "            \"transpose\": is_transpose if height == width else None\n", "        }\n", "    }\n", "\n", "def detect_objects(grid, background=0):\n", "    \"\"\"Détecte les objets dans une grille\"\"\"\n", "    if isinstance(grid, list):\n", "        grid = np.array(grid)\n", "\n", "    height, width = grid.shape\n", "\n", "    # C<PERSON>er une grille de labels pour les objets\n", "    labels = np.zeros_like(grid)\n", "    next_label = 1\n", "\n", "    # Fonction pour étiqueter un objet de manière récursive\n", "    def label_object(y, x, label):\n", "        if y < 0 or y >= height or x < 0 or x >= width:\n", "            return 0  # Hors limites\n", "\n", "        if grid[y, x] == background or labels[y, x] != 0:\n", "            return 0  # <PERSON><PERSON> ou d<PERSON><PERSON><PERSON>\n", "\n", "        # Étiqueter la cellule\n", "        labels[y, x] = label\n", "        size = 1\n", "\n", "        # Éti<PERSON>er les voisins (4-connexité)\n", "        size += label_object(y-1, x, label)  # Haut\n", "        size += label_object(y+1, x, label)  # Bas\n", "        size += label_object(y, x-1, label)  # Gauche\n", "        size += label_object(y, x+1, label)  # Droite\n", "\n", "        return size\n", "\n", "    # Parcourir la grille et étiqueter les objets\n", "    objects = {}\n", "    for y in range(height):\n", "        for x in range(width):\n", "            if grid[y, x] != background and labels[y, x] == 0:\n", "                size = label_object(y, x, next_label)\n", "                objects[next_label] = {\n", "                    \"value\": int(grid[y, x]),\n", "                    \"size\": size,\n", "                    \"label\": next_label\n", "                }\n", "                next_label += 1\n", "\n", "    print(f\"Nombre d'objets détectés: {len(objects)}\")\n", "\n", "    # Afficher les informations sur les objets\n", "    if objects:\n", "        print(\"\\nInformations sur les objets:\")\n", "        for label, obj in objects.items():\n", "            print(f\"  Objet {label}: valeur {obj['value']}, taille {obj['size']}\")\n", "\n", "    # C<PERSON>er une grille colorée pour visualiser les objets\n", "    if objects:\n", "        print(\"\\nVisualisation des objets:\")\n", "        # Utiliser une palette de couleurs différente pour chaque objet\n", "        object_grid = np.zeros_like(grid)\n", "        for y in range(height):\n", "            for x in range(width):\n", "                if labels[y, x] > 0:\n", "                    object_grid[y, x] = labels[y, x]\n", "\n", "        display_grid(object_grid, \"Objets détectés\")\n", "\n", "    return objects"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Exemple d'utilisation des fonctions d'analyse avancées\n", "if train_examples:\n", "    example = train_examples[0]\n", "    \n", "    print(\"Analyse des patterns dans la grille d'entrée:\")\n", "    analyze_patterns(example['input'])\n", "    \n", "    print(\"\\nAnalyse des transformations entre l'entrée et la sortie:\")\n", "    analyze_transformations(example['input'], example['output'])\n", "    \n", "    print(\"\\nDétection des objets dans la grille d'entrée:\")\n", "    detect_objects(example['input'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Génération de commandes\n", "\n", "Cette section permet de générer des commandes pour l'éditeur d'automatisation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fonction pour générer des commandes à partir d'une grille\n", "def generate_commands(input_grid, output_grid):\n", "    \"\"\"Génère des commandes pour transformer la grille d'entrée en grille de sortie.\n", "    \n", "    Args:\n", "        input_grid (list or numpy.ndarray): La grille d'entrée\n", "        output_grid (list or numpy.ndarray): La grille de sortie\n", "        \n", "    Returns:\n", "        list: Liste de commandes\n", "    \"\"\"\n", "    # Utiliser arc_utils si disponible, sinon utiliser l'implémentation de base\n", "    if 'arc_utils' in sys.modules:\n", "        print(\"Utilisation de arc_utils.generate_commands_from_grid\")\n", "        return arc_utils.generate_commands_from_grid(output_grid)\n", "    \n", "    print(\"Utilisation de l'implémentation de base\")\n", "    commands = []\n", "    \n", "    # Initialiser la grille avec les dimensions de la sortie\n", "    height = len(output_grid)\n", "    width = len(output_grid[0])\n", "    commands.append(f\"INIT {width} {height}\")\n", "    \n", "    # Ajouter des commandes EDIT pour chaque cellule non nulle\n", "    for i in range(height):\n", "        for j in range(width):\n", "            if output_grid[i][j] != 0:\n", "                commands.append(f\"EDIT {j} {i} {output_grid[i][j]}\")\n", "    \n", "    # Proposer la solution\n", "    commands.append(\"PROPOSE\")\n", "    \n", "    return commands\n", "\n", "# Générer des commandes pour le premier exemple d'entraînement\n", "if train_examples:\n", "    example = train_examples[0]\n", "    commands = generate_commands(example['input'], example['output'])\n", "    print(\"\\nCommandes générées:\")\n", "    for cmd in commands:\n", "        print(cmd)\n", "    \n", "    # Valider les commandes si arc_utils est disponible\n", "    if 'arc_utils' in sys.modules and 'task_data' in globals():\n", "        print(\"\\nValidation des commandes:\")\n", "        validation = arc_utils.validate_commands(commands, task_data)\n", "        if validation[\"valid\"]:\n", "            print(\"Les commandes sont valides!\")\n", "        else:\n", "            print(\"Les commandes sont invalides:\")\n", "            for error in validation[\"errors\"]:\n", "                print(f\"  Ligne {error['line']}: {error['error']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Envoyer les commandes au frontend\n", "def send_commands_to_frontend(commands):\n", "    \"\"\"Envoie les commandes au frontend pour l'éditeur d'automatisation.\n", "    \n", "    Args:\n", "        commands (list): Liste de commandes à envoyer\n", "    \"\"\"\n", "    try:\n", "        # Vérifier si nous sommes dans Pyodide\n", "        if 'js' in sys.modules:\n", "            # Convertir les commandes en JSON\n", "            commands_json = json.dumps(commands)\n", "            \n", "            # Envoyer les commandes au frontend\n", "            import js\n", "            if hasattr(js.window.parent, 'notebookCommandsCallback'):\n", "                js.window.parent.notebookCommandsCallback(commands_json)\n", "                print('Commandes envoyées au frontend')\n", "            else:\n", "                print('Callback pour les commandes non disponible dans le frontend')\n", "        else:\n", "            # Nous sommes dans un environnement Jupyter normal\n", "            print(\"Commandes à envoyer au frontend:\")\n", "            for cmd in commands:\n", "                print(f\"  {cmd}\")\n", "    except Exception as e:\n", "        print(f\"Erreur lors de l'envoi des commandes au frontend: {e}\")\n", "\n", "# Exemple d'utilisation\n", "if train_examples:\n", "    example = train_examples[0]\n", "    commands = generate_commands(example['input'], example['output'])\n", "    \n", "    # Demander confirmation avant d'envoyer les commandes\n", "    print(\"\\nPour envoyer ces commandes au frontend, exécutez la cellule suivante:\")\n", "    print(\"send_commands_to_frontend(commands)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Guide d'utilisation du notebook\n", "\n", "Ce notebook vous permet d'analyser et de résoudre des tâches ARC (Abstraction and Reasoning Corpus). Voici comment l'utiliser efficacement :\n", "\n", "## Structure du notebook\n", "\n", "Le notebook est organisé en sections :\n", "1. **Configuration et importation** : Importe les bibliothèques nécessaires\n", "2. **Récupération des données** : Charge les données de la tâche courante\n", "3. **Visualisation des exemples** : Affiche les grilles d'entrée et de sortie\n", "4. **Analyse des données** : Fournit des outils pour analyser les patterns et transformations\n", "5. **Développement de la solution** : Espace pour implémenter votre solution\n", "6. **Génération de commandes** : Convertit votre solution en commandes pour l'éditeur d'automatisation\n", "\n", "## Fonctions disponibles\n", "\n", "### Fonctions de base\n", "- `display_grid(grid, title=None)` : Affiche une grille avec un titre optionnel\n", "- `arc_utils.load_task(task_id)` : Charge une tâche ARC depuis le répertoire arcdata\n", "- `arc_utils.validate_commands(commands)` : <PERSON><PERSON> une liste de commandes\n", "- `arc_utils.execute_commands(commands)` : Exécute une liste de commandes et retourne la grille résultante\n", "- `arc_utils.generate_commands_from_grid(grid)` : <PERSON>énère des commandes à partir d'une grille\n", "\n", "### Fonctions d'analyse avancées\n", "- `analyze_patterns(grid)` : Analyse les patterns dans une grille (symétries, motifs répétitifs)\n", "- `analyze_transformations(input_grid, output_grid)` : Analyse les transformations entre deux grilles\n", "- `detect_objects(grid, background=0)` : Détecte les objets dans une grille\n", "\n", "## Commandes d'automatisation\n", "\n", "Les commandes d'automatisation permettent de construire une solution. Voici les commandes disponibles :\n", "\n", "- `INIT width height` : Initialise une grille vide de dimensions width × height\n", "- `EDIT x y value` : Définit la valeur de la cellule aux coordonnées (x, y)\n", "- `COPY x1 y1 x2 y2 dest_x dest_y` : Copie un rectangle de (x1,y1) à (x2,y2) vers la position (dest_x,dest_y)\n", "- `FILL x1 y1 x2 y2 value` : Remplit un rectangle avec une valeur\n", "- `PROPOSE` : Propose la solution actuelle\n", "\n", "## Conseils pour résoudre les tâches ARC\n", "\n", "1. **Examinez attentivement les exemples** : Comprenez la transformation entre l'entrée et la sortie\n", "2. **Recherchez des patterns** : Utilisez les fonctions d'analyse pour détecter des patterns\n", "3. **Testez différentes hypothèses** : Implémentez et testez plusieurs approches\n", "4. **Validez votre solution** : Vérifiez que votre solution fonctionne sur tous les exemples d'entraînement\n", "5. **<PERSON><PERSON><PERSON><PERSON> des commandes** : Convertissez votre solution en commandes pour l'éditeur d'automatisation"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "python", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8"}, "kernelspec": {"display_name": "Python (Pyodide)", "language": "python", "name": "python"}, "description": "Template standardisé pour analyser et résoudre les tâches ARC"}, "nbformat": 4, "nbformat_minor": 4}