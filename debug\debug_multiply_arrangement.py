#!/usr/bin/env python3
"""
Debug de l'arrangement correct pour MULTIPLY 4 false
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import numpy as np

def analyze_expected_multiply_result():
    """Analyser le résultat attendu de MULTIPLY 4 false"""
    
    print("🔍 Analyse du résultat attendu de MULTIPLY 4 false...")
    
    # Motif original
    original = np.array([[0, 0],
                         [3, 0]])
    
    print("📋 Motif original (2x2):")
    print(original)
    
    # Résultat attendu après collage à [0,1]
    expected_final = np.array([
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    
    # Extraire le clipboard attendu (zone collée à partir de [0,1])
    expected_clipboard = expected_final[0:8, 1:9]  # 8x8 à partir de [0,1]
    
    print("\n📋 Clipboard attendu (8x8):")
    print(expected_clipboard)
    
    # Analyser la structure
    print("\n🔍 Analyse de la structure:")
    
    # Diviser en blocs 4x4
    for block_row in range(2):
        for block_col in range(2):
            start_row = block_row * 4
            end_row = start_row + 4
            start_col = block_col * 4
            end_col = start_col + 4
            
            block = expected_clipboard[start_row:end_row, start_col:end_col]
            orig_row, orig_col = block_row, block_col
            orig_value = original[orig_row, orig_col]
            
            print(f"\nBloc [{block_row},{block_col}] (lignes {start_row}-{end_row-1}, cols {start_col}-{end_col-1}):")
            print(f"Valeur originale: {orig_value}")
            print(f"Bloc généré:")
            print(block)
            
            # Vérifier si le bloc est uniforme
            is_uniform = np.all(block == orig_value)
            print(f"Bloc uniforme avec valeur {orig_value}: {is_uniform}")

def test_correct_multiply_implementation():
    """Tester l'implémentation correcte de MULTIPLY"""
    
    print("\n" + "="*60)
    print("🧪 Test de l'implémentation correcte de MULTIPLY...")
    
    # Motif original
    original = np.array([[0, 0],
                         [3, 0]])
    
    factor = 4
    clip_height, clip_width = original.shape
    
    # Créer le clipboard multiplié correct
    new_height = clip_height * factor
    new_width = clip_width * factor
    new_clipboard = np.zeros((new_height, new_width), dtype=int)
    
    print(f"📏 Dimensions: {clip_height}x{clip_width} → {new_height}x{new_width}")
    
    # IMPLÉMENTATION CORRECTE: Arrangement en blocs
    # Chaque cellule du motif original devient un bloc factor x factor
    for orig_row in range(clip_height):
        for orig_col in range(clip_width):
            cell_value = original[orig_row, orig_col]
            
            # Calculer la position du bloc dans le nouveau clipboard
            block_start_row = orig_row * factor
            block_start_col = orig_col * factor
            
            # Remplir le bloc factor x factor avec la valeur de la cellule
            for i in range(factor):
                for j in range(factor):
                    new_row = block_start_row + i
                    new_col = block_start_col + j
                    new_clipboard[new_row, new_col] = cell_value
    
    print("\n📋 Clipboard multiplié correct:")
    print(new_clipboard)
    
    # Tester le collage à [0,1]
    grid = np.zeros((9, 9), dtype=int)
    x_dest, y_dest = 0, 1
    
    for r in range(new_height):
        for c in range(new_width):
            grid_r, grid_c = x_dest + r, y_dest + c
            if 0 <= grid_r < 9 and 0 <= grid_c < 9:
                grid[grid_r, grid_c] = new_clipboard[r, c]
    
    print("\n📋 Grille après collage à [0,1]:")
    print(grid)
    
    # Comparer avec l'attendu
    expected = np.array([
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    
    matches = np.sum(grid == expected)
    total = grid.size
    percentage = (matches / total) * 100
    
    print(f"\n📊 Comparaison avec l'attendu:")
    print(f"Cellules correspondantes: {matches}/{total} ({percentage:.1f}%)")
    
    if matches == total:
        print("✅ SUCCÈS ! L'implémentation est correcte.")
        return True
    else:
        print("\n❌ Différences détectées:")
        diff_positions = np.where(grid != expected)
        for i in range(min(10, len(diff_positions[0]))):
            row, col = diff_positions[0][i], diff_positions[1][i]
            print(f"   Position [{row},{col}]: généré={grid[row,col]}, attendu={expected[row,col]}")
        return False

if __name__ == "__main__":
    analyze_expected_multiply_result()
    success = test_correct_multiply_implementation()
    
    if success:
        print("\n🎉 L'implémentation correcte a été identifiée !")
    else:
        print("\n❌ L'implémentation nécessite encore des ajustements.")