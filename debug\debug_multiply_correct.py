#!/usr/bin/env python3
"""
Implémentation correcte de MULTIPLY basée sur l'analyse de la sortie attendue
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import numpy as np

def correct_multiply_implementation():
    """Implémentation correcte de MULTIPLY 4 false"""
    
    print("🔧 Implémentation correcte de MULTIPLY 4 false...")
    
    # Motif original
    original_clipboard = np.array([[0, 0],
                                   [3, 0]])
    original_mask = np.ones((2, 2), dtype=bool)
    
    print("📋 Clipboard original:")
    print(original_clipboard)
    
    factor = 4
    clip_height, clip_width = original_clipboard.shape
    
    # Créer le nouveau clipboard avec les bonnes dimensions
    new_height = clip_height * factor
    new_width = clip_width * factor
    new_clipboard = np.zeros((new_height, new_width), dtype=int)
    new_mask = np.ones((new_height, new_width), dtype=bool)
    
    print(f"📏 Nouvelles dimensions: {new_height}x{new_width}")
    
    # L'implémentation correcte : chaque cellule du motif original
    # devient un bloc factor x factor avec la même valeur
    for orig_row in range(clip_height):
        for orig_col in range(clip_width):
            if original_mask[orig_row, orig_col]:
                cell_value = original_clipboard[orig_row, orig_col]
                
                # Calculer la position du bloc dans le nouveau clipboard
                block_start_row = orig_row * factor
                block_start_col = orig_col * factor
                
                # Remplir le bloc factor x factor avec la valeur de la cellule
                for i in range(factor):
                    for j in range(factor):
                        new_row = block_start_row + i
                        new_col = block_start_col + j
                        new_clipboard[new_row, new_col] = cell_value
                        new_mask[new_row, new_col] = True
    
    print("\n📋 Clipboard après MULTIPLY 4 false (implémentation correcte):")
    print(new_clipboard)
    
    # Test de collage à [0,1]
    grid = np.zeros((9, 9), dtype=int)
    x_dest, y_dest = 0, 1
    
    for r in range(new_height):
        for c in range(new_width):
            if new_mask[r, c]:
                grid_r, grid_c = x_dest + r, y_dest + c
                if 0 <= grid_r < 9 and 0 <= grid_c < 9:
                    grid[grid_r, grid_c] = new_clipboard[r, c]
    
    print("\n📋 Grille après PASTE [0,1]:")
    print(grid)
    
    # Comparer avec l'attendu
    expected = np.array([
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    
    matches = np.sum(grid == expected)
    total = grid.size
    percentage = (matches / total) * 100
    
    print(f"\n📊 Comparaison avec l'attendu:")
    print(f"Cellules correspondantes: {matches}/{total} ({percentage:.1f}%)")
    
    if matches == total:
        print("✅ SUCCÈS ! Cette implémentation est correcte.")
        return True
    else:
        print("\n❌ Différences détectées:")
        diff_positions = np.where(grid != expected)
        for i in range(min(10, len(diff_positions[0]))):
            row, col = diff_positions[0][i], diff_positions[1][i]
            print(f"   Position [{row},{col}]: généré={grid[row,col]}, attendu={expected[row,col]}")
        return False

def test_with_different_patterns():
    """Test avec différents motifs pour valider l'implémentation"""
    
    print("\n" + "="*60)
    print("🧪 Test avec différents motifs...")
    
    # Test avec un motif plus complexe
    test_patterns = [
        np.array([[1, 2], [3, 4]]),
        np.array([[5]]),
        np.array([[1, 0, 2]])
    ]
    
    for i, pattern in enumerate(test_patterns):
        print(f"\n--- Test {i+1} ---")
        print(f"Motif original:")
        print(pattern)
        
        factor = 3
        clip_height, clip_width = pattern.shape
        
        # Appliquer la logique correcte
        new_height = clip_height * factor
        new_width = clip_width * factor
        new_clipboard = np.zeros((new_height, new_width), dtype=int)
        
        for orig_row in range(clip_height):
            for orig_col in range(clip_width):
                cell_value = pattern[orig_row, orig_col]
                
                block_start_row = orig_row * factor
                block_start_col = orig_col * factor
                
                for r in range(factor):
                    for c in range(factor):
                        new_row = block_start_row + r
                        new_col = block_start_col + c
                        new_clipboard[new_row, new_col] = cell_value
        
        print(f"Résultat MULTIPLY {factor} false:")
        print(new_clipboard)

if __name__ == "__main__":
    success = correct_multiply_implementation()
    
    if success:
        test_with_different_patterns()
    else:
        print("\n❌ L'implémentation n'est pas encore correcte.")