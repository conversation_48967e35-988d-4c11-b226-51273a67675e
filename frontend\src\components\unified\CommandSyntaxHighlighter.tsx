import React from 'react';
import './CommandSyntaxHighlighter.css';

interface CommandSyntaxHighlighterProps {
  command: string;
  className?: string;
}

interface TokenInfo {
  type: 'action' | 'parameter' | 'coordinate' | 'operator' | 'parenthesis' | 'bracket' | 'comma' | 'text';
  value: string;
  start: number;
  end: number;
}

export const CommandSyntaxHighlighter: React.FC<CommandSyntaxHighlighterProps> = ({
  command,
  className = ''
}) => {
  const tokens = tokenizeCommand(command);

  return (
    <span className={`command-syntax ${className}`}>
      {tokens.map((token, index) => (
        <span key={index} className={`token token--${token.type}`}>
          {token.value}
        </span>
      ))}
    </span>
  );
};

/**
 * Tokenise une commande en identifiant les différents éléments syntaxiques
 */
function tokenizeCommand(command: string): TokenInfo[] {
  const tokens: TokenInfo[] = [];
  let position = 0;

  // Expressions régulières pour identifier les différents éléments
  const patterns = [
    // Actions principales
    { type: 'action' as const, regex: /^(INIT|INPUT|FILL|CLEAR|REPLACE|SURROUND|EDIT|INSERT|DELETE|EXTRACT|COPY|CUT|PASTE|FLIP|ROTATE|MULTIPLY|DIVIDE|INPUT|SELECT|MOTIF|FILLS|EDITS|REPLACES|END)(?=\s|$)/ },
    
    // Opérateurs spéciaux (nouveaux)
    { type: 'operator' as const, regex: /^(XOR|AND|OR|INVERT|COLOR)(?=\s|\(|$)/ },
    
    // Paramètres de direction et type
    { type: 'parameter' as const, regex: /^(HORIZONTAL|VERTICAL|LEFT|RIGHT|ABOVE|BELOW|BEFORE|AFTER|ROWS|COLUMNS)(?=\s|$)/ },
    
    // Coordonnées [x,y] ou [x1,y1 x2,y2]
    { type: 'coordinate' as const, regex: /^\[[\d,\s]+\]/ },
    
    // Parenthèses
    { type: 'parenthesis' as const, regex: /^[()]/ },
    
    // Crochets (déjà gérés dans les coordonnées, mais pour les cas isolés)
    { type: 'bracket' as const, regex: /^[\[\]]/ },
    
    // Virgules
    { type: 'comma' as const, regex: /^,/ },
    
    // Nombres (paramètres de couleur, compteurs, etc.)
    { type: 'parameter' as const, regex: /^\d+/ },
    
    // Accolades pour les regroupements
    { type: 'bracket' as const, regex: /^[{}]/ },
    
    // Espaces (préservés pour la mise en forme)
    { type: 'text' as const, regex: /^\s+/ },
    
    // Tout autre caractère
    { type: 'text' as const, regex: /^./ }
  ];

  while (position < command.length) {
    let matched = false;

    for (const pattern of patterns) {
      const match = command.slice(position).match(pattern.regex);
      if (match) {
        tokens.push({
          type: pattern.type,
          value: match[0],
          start: position,
          end: position + match[0].length
        });
        position += match[0].length;
        matched = true;
        break;
      }
    }

    // Si aucun pattern ne correspond, avancer d'un caractère
    if (!matched) {
      tokens.push({
        type: 'text',
        value: command[position],
        start: position,
        end: position + 1
      });
      position++;
    }
  }

  return tokens;
}

/**
 * Composant pour afficher une commande avec coloration syntaxique dans une ligne
 */
export const InlineCommandHighlighter: React.FC<CommandSyntaxHighlighterProps> = ({
  command,
  className = ''
}) => {
  return (
    <code className={`inline-command ${className}`}>
      <CommandSyntaxHighlighter command={command} />
    </code>
  );
};

/**
 * Composant pour afficher plusieurs commandes avec coloration syntaxique
 */
interface MultiCommandHighlighterProps {
  commands: string[];
  className?: string;
  onCommandClick?: (command: string, index: number) => void;
}

export const MultiCommandHighlighter: React.FC<MultiCommandHighlighterProps> = ({
  commands,
  className = '',
  onCommandClick
}) => {
  return (
    <div className={`multi-command-highlighter ${className}`}>
      {commands.map((command, index) => (
        <div 
          key={index}
          className={`command-line ${onCommandClick ? 'clickable' : ''}`}
          onClick={() => onCommandClick?.(command, index)}
        >
          <span className="line-number">{index + 1}</span>
          <CommandSyntaxHighlighter command={command} />
        </div>
      ))}
    </div>
  );
};