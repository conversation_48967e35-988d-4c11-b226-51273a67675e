#!/usr/bin/env python3
"""
Test pour isoler le problème avec MULTIPLY 4 false
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from command_system.command_executor import CommandExecutor
import numpy as np

def test_multiply_issue():
    """Test la séquence exacte qui pose problème"""
    
    print("🧪 Test de la séquence MULTIPLY qui pose problème...")
    
    # Données de test simulées (grille 3x3 -> 9x9)
    task_data = {
        'test': [{
            'input': [[0, 0, 0], [0, 0, 0], [0, 0, 0]],
            'output': [[0] * 9 for _ in range(9)]  # Grille 9x9 de zéros pour simplifier
        }]
    }
    
    # Commandes exactes du scénario problématique
    commands = [
        'INPUT',
        'EDIT 0 [0,1]',
        'EDIT 0 [1,2]', 
        'EDIT 3 [1,1]',
        'RESIZE 9x9',
        'CUT [0,1 1,2]',
        'MULTIPLY 4 false',
        'PASTE [0,1 1,2]',
        'END'
    ]
    
    executor = CommandExecutor(task_data, 0)
    
    print("📋 Commandes à exécuter:")
    for i, cmd in enumerate(commands, 1):
        print(f"  {i}. {cmd}")
    
    print("\n🔄 Exécution étape par étape...")
    
    # Exécuter commande par commande pour identifier où ça échoue
    for i, command in enumerate(commands):
        print(f"\n--- Étape {i+1}: {command} ---")
        
        if not executor._execute_command(command):
            print(f"❌ ÉCHEC à l'étape {i+1}: {command}")
            print(f"   Erreur: {executor.error}")
            break
        else:
            print(f"✅ Succès")
            if executor.grid is not None:
                print(f"   Grille: {executor.grid.shape}")
                if hasattr(executor, 'clipboard') and executor.clipboard is not None:
                    print(f"   Clipboard: {executor.clipboard.shape}")
    
    # Résultat final
    result = executor.execute_commands(commands)
    print(f"\n🎯 Résultat final:")
    print(f"   Succès: {result['success']}")
    if not result['success']:
        print(f"   Erreur: {result['error']}")
    
    return result['success']

if __name__ == "__main__":
    success = test_multiply_issue()
    if success:
        print("\n🎉 Test réussi!")
    else:
        print("\n💥 Test échoué!")