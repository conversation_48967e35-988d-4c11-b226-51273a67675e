import { 
  ParsedCommand, 
  UnifiedSelection, 
  SelectionRectangle, 
  CommandParameters,
  ValidationResult,
  ValidationError,
  ValidationWarning,
  ActionSubtype,
  CommandType
} from '../types/unifiedCommands';

export class UnifiedCommandParser {
  
  /**
   * Parse une commande au format unifié
   * Formats supportés :
   * - CLEAR [1,2 3,4] [+5,6 7,8]
   * - FILL 5 [1,2 3,4]
   * - REPLACE 1,2 3 [1,2 3,4]
   * - INSERT 3 ROWS BELOW [1,2 3,4]
   * - CLEAR INVERSE([1,2 3,4])
   * - REPLACE 1 2 COLOR([1,2 3,4])
   */
  parseCommand(commandString: string): ParsedCommand {
    const trimmed = commandString.trim();
    if (!trimmed) {
      throw new Error('Commande vide');
    }

    // Détecter les commandes avec sélections spéciales (INVERSE, COLOR)
    const specialSelectionMatch = this.parseSpecialSelections(trimmed);
    if (specialSelectionMatch) {
      return specialSelectionMatch;
    }

    // Parser commande standard
    return this.parseStandardCommand(trimmed);
  }
  private parseSpecialSelections(command: string): ParsedCommand | null {
    // Pattern pour INVERSE([...]) avec support des paramètres
    const inverseMatch = command.match(/^(\w+)(?:\s+(.+?))?\s+INVERSE\((.+)\)$/);
    if (inverseMatch) {
      const [, action, paramsStr, selectionsStr] = inverseMatch;
      const selections = this.parseSelections(selectionsStr);
      selections.isInverted = true;
      const parameters = this.parseParameters(action, paramsStr?.trim() || '');
      
      return {
        type: 'ACTION',
        action: action as ActionSubtype,
        selections,
        parameters,
        rawCommand: command
      };
    }

    // Pattern pour COLOR([...])
    const colorMatch = command.match(/^(\w+)\s+(.+?)\s+COLOR\((.+)\)$/);
    if (colorMatch) {
      const [, action, paramsStr, selectionsStr] = colorMatch;
      const selections = this.parseSelections(selectionsStr);
      const parameters = this.parseParameters(action, paramsStr);
      
      // Pour COLOR, on appliquera le filtre dans l'exécuteur
      selections.colorFilter = []; // Sera défini par l'UI
      
      return {
        type: 'ACTION',
        action: action as ActionSubtype,
        selections,
        parameters,
        rawCommand: command
      };
    }

    return null;
  }

  private parseStandardCommand(command: string): ParsedCommand {
    // Cas spécial pour INPUT (commande sans paramètres ni coordonnées)
    if (command.trim() === 'INPUT') {
      return {
        type: 'ACTION',
        action: 'INPUT' as ActionSubtype,
        selections: {
          coordinates: [],
          additionalCoordinates: [],
          isInverted: false,
          colorFilter: []
        },
        parameters: {},
        rawCommand: command
      };
    }

    // Pattern général : ACTION [params] [selections]
    const match = command.match(/^(\w+)(?:\s+([^[]+?))?\s*(\[.+\])$/);
    if (!match) {
      throw new Error(`Format de commande invalide: ${command}`);
    }

    const [, actionStr, paramsStr, selectionsStr] = match;

    const action = actionStr as ActionSubtype;
    const selections = this.parseSelections(selectionsStr);
    const parameters = this.parseParameters(action, paramsStr?.trim() || '');

    return {
      type: this.determineCommandType(action),
      action,
      selections,
      parameters,
      rawCommand: command
    };
  }

  private parseSelections(selectionsString: string): UnifiedSelection {
    // Parse [l1,c1 l2,c2] [+l3,c3 l4,c4] format
    const rectangleMatches = [...selectionsString.matchAll(/\[([^\]]+)\]/g)];
    
    if (rectangleMatches.length === 0) {
      throw new Error('Aucune sélection trouvée');
    }

    const rectangles: SelectionRectangle[] = [];
    
    for (const match of rectangleMatches) {
      const content = match[1].trim();
      const isAdditional = content.startsWith('+');
      const coords = isAdditional ? content.substring(1) : content;
      
      rectangles.push(this.parseRectangle(coords, isAdditional));
    }
    
    return {
      rectangles,
      mode: 'replace'
    };
  }

  private parseRectangle(coordsStr: string, isAdditional: boolean): SelectionRectangle {
    // Format: "1,2 3,4" (ligne,colonne ligne,colonne)
    const coords = coordsStr.trim().split(/\s+/);
    
    if (coords.length === 1) {
      // Format cellule unique: "1,2"
      const [row, col] = coords[0].split(',').map(Number);
      if (isNaN(row) || isNaN(col)) {
        throw new Error(`Coordonnées invalides: ${coordsStr}`);
      }
      return {
        startRow: row,
        startCol: col,
        endRow: row,
        endCol: col,
        isAdditional
      };
    } else if (coords.length === 2) {
      // Format rectangle: "1,2 3,4"
      const [startRow, startCol] = coords[0].split(',').map(Number);
      const [endRow, endCol] = coords[1].split(',').map(Number);
      
      if (isNaN(startRow) || isNaN(startCol) || isNaN(endRow) || isNaN(endCol)) {
        throw new Error(`Coordonnées invalides: ${coordsStr}`);
      }
      
      return {
        startRow: Math.min(startRow, endRow),
        startCol: Math.min(startCol, endCol),
        endRow: Math.max(startRow, endRow),
        endCol: Math.max(startCol, endCol),
        isAdditional
      };
    } else {
      throw new Error(`Format de coordonnées invalide: ${coordsStr}`);
    }
  }

  private parseParameters(action: string, paramsStr: string): CommandParameters {
    if (!paramsStr) return {};

    const params: CommandParameters = {};
    const parts = paramsStr.trim().split(/\s+/);

    switch (action) {
      case 'FILL':
      case 'SURROUND':
        // Format: FILL 5 [...]
        if (parts.length >= 1) {
          params.color = parseInt(parts[0]);
          if (isNaN(params.color)) {
            throw new Error(`Couleur invalide: ${parts[0]}`);
          }
        }
        break;

      case 'REPLACE':
        // Format: REPLACE 1,2,3 5 [...] ou REPLACE 1 5 [...]
        if (parts.length >= 2) {
          if (parts[0].includes(',')) {
            params.sourceColors = parts[0].split(',').map(Number);
          } else {
            params.sourceColors = [parseInt(parts[0])];
          }
          params.targetColor = parseInt(parts[1]);
          
          if (params.sourceColors.some(isNaN) || isNaN(params.targetColor)) {
            throw new Error(`Couleurs invalides dans REPLACE: ${paramsStr}`);
          }
        }
        break;

      case 'INSERT':
        // Format: INSERT 3 ROWS BELOW [...]
        if (parts.length >= 3) {
          params.type = parts[0] as 'ROWS' | 'COLUMNS';
          params.count = parseInt(parts[1]);
          params.direction = parts[2] as any;
          
          if (!['ROWS', 'COLUMNS'].includes(params.type)) {
            throw new Error(`Type INSERT invalide: ${params.type}`);
          }
          if (isNaN(params.count) || params.count <= 0) {
            throw new Error(`Nombre invalide pour INSERT: ${parts[1]}`);
          }
          if (!params.direction || !['before', 'after', 'left', 'right'].includes(params.direction)) {
            throw new Error(`Direction INSERT invalide: ${params.direction}`);
          }
        }
        break;

      case 'DELETE':
        // Format: DELETE ROWS [...] ou DELETE COLUMNS [...]
        if (parts.length >= 1) {
          params.type = parts[0] as 'ROWS' | 'COLUMNS';
          if (!['ROWS', 'COLUMNS'].includes(params.type)) {
            throw new Error(`Type DELETE invalide: ${params.type}`);
          }
        }
        break;

      case 'FLIP':
        // Format: FLIP H [...] ou FLIP V [...]
        if (parts.length >= 1) {
          params.direction = parts[0] as 'H' | 'V';
          if (!params.direction || !['H', 'V'].includes(params.direction)) {
            throw new Error(`Direction FLIP invalide: ${params.direction}`);
          }
        }
        break;

      case 'ROTATE':
        // Format: ROTATE L [...] ou ROTATE R [...]
        if (parts.length >= 1) {
          params.direction = parts[0] as 'L' | 'R';
          if (!params.direction || !['L', 'R'].includes(params.direction)) {
            throw new Error(`Direction ROTATE invalide: ${params.direction}`);
          }
        }
        break;
    }

    return params;
  }

  private determineCommandType(action: string): CommandType {
    const transformations = ['FLIP', 'ROTATE'];
    const selections = ['INVERSE', 'COLOR'];
    
    if (transformations.includes(action)) return 'TRANSFORMATION';
    if (selections.includes(action)) return 'SELECTION';
    return 'ACTION';
  }

  /**
   * Valide une commande parsée
   */
  validateParsedCommand(parsed: ParsedCommand): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Validation basique des sélections
    if (parsed.selections.rectangles.length === 0) {
      errors.push({
        code: 'NO_SELECTION',
        message: 'Aucune sélection spécifiée'
      });
    }

    // Validation des paramètres selon l'action
    switch (parsed.action) {
      case 'FILL':
      case 'SURROUND':
        if (parsed.parameters.color === undefined) {
          errors.push({
            code: 'MISSING_COLOR',
            message: `Couleur requise pour ${parsed.action}`
          });
        } else if (parsed.parameters.color < 0 || parsed.parameters.color > 9) {
          errors.push({
            code: 'INVALID_COLOR',
            message: 'Couleur doit être entre 0 et 9'
          });
        }
        break;

      case 'REPLACE':
        if (!parsed.parameters.sourceColors || parsed.parameters.targetColor === undefined) {
          errors.push({
            code: 'MISSING_COLORS',
            message: 'Couleurs source et cible requises pour REPLACE'
          });
        }
        break;

      case 'INSERT':
        if (!parsed.parameters.type || !parsed.parameters.count || !parsed.parameters.direction) {
          errors.push({
            code: 'MISSING_INSERT_PARAMS',
            message: 'Type, nombre et direction requis pour INSERT'
          });
        } else {
          // Valider les directions selon le type
          if (parsed.parameters.type === 'ROWS') {
            if (!['ABOVE', 'BELOW'].includes(parsed.parameters.direction)) {
              errors.push({
                code: 'INVALID_INSERT_DIRECTION',
                message: `Direction invalide pour INSERT ROWS: ${parsed.parameters.direction}. Utilisez ABOVE ou BELOW`
              });
            }
          } else if (parsed.parameters.type === 'COLUMNS') {
            if (!['BEFORE', 'AFTER'].includes(parsed.parameters.direction)) {
              errors.push({
                code: 'INVALID_INSERT_DIRECTION',
                message: `Direction invalide pour INSERT COLUMNS: ${parsed.parameters.direction}. Utilisez BEFORE ou AFTER`
              });
            }
          }
        }
        break;

      case 'DELETE':
        if (!parsed.parameters.type) {
          errors.push({
            code: 'MISSING_DELETE_TYPE',
            message: 'Type requis pour DELETE (ROWS ou COLUMNS)'
          });
        }
        break;

      case 'FLIP':
      case 'ROTATE':
        if (!parsed.parameters.direction) {
          errors.push({
            code: 'MISSING_DIRECTION',
            message: `Direction requise pour ${parsed.action}`
          });
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}

// Export de l'instance singleton
export const unifiedCommandParser = new UnifiedCommandParser();