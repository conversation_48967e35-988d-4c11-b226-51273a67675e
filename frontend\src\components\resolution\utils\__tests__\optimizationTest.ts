/**
 * Test pour vérifier que l'optimisation ne casse pas les commandes DELETE/INSERT
 */

import { optimizeCommandList } from '../commandOptimizationEngine';

console.log('🧪 Test de l\'optimisation des commandes DELETE/INSERT');

const commands = [
    'INPUT',
    'DELETE COLUMNS [0,1 3,1]',
    'DELETE COLUMNS [0,3 3,5]',
    'DELETE ROWS [1,0 3,4]',
    'END'
];

console.log('\n📝 Commandes originales:');
commands.forEach((cmd, i) => console.log(`${i + 1}. ${cmd}`));

const result = optimizeCommandList(commands);

console.log('\n✅ Commandes optimisées:');
result.optimizedCommands.forEach((cmd, i) => console.log(`${i + 1}. ${cmd}`));

console.log('\n🔍 Règles appliquées:');
result.rulesApplied.forEach(rule => console.log(`- ${rule}`));

// Vérifier que les commandes DELETE ne sont pas groupées
const hasGroupedDelete = result.optimizedCommands.some(cmd => 
    cmd.includes('DELETES {') || cmd.includes('DELETE COLUMNS ([')
);

if (hasGroupedDelete) {
    console.log('\n❌ ERREUR: Les commandes DELETE ont été groupées !');
} else {
    console.log('\n✅ SUCCESS: Les commandes DELETE restent séparées !');
}

console.log('\n🎉 Test terminé !');