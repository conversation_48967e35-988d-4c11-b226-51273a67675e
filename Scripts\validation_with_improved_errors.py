#!/usr/bin/env python3
"""
Exemple d'utilisation du module error_message_formatter dans un script de validation.
Ce script montre comment utiliser les messages d'erreur améliorés dans d'autres contextes.
"""

import sys
import os
from pathlib import Path

# Ajouter le backend au path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'backend'))

from utils.error_message_formatter import (
    ErrorMessageFormatter, 
    ValidationErrorContext, 
    format_validation_error,
    create_error_context
)


def example_validation_with_improved_errors():
    """
    Exemple d'utilisation des messages d'erreur améliorés dans un script de validation.
    """
    print("🧪 Exemple d'utilisation des messages d'erreur améliorés\n")
    
    # Méthode 1: Utilisation directe de ErrorMessageFormatter
    print("1. Utilisation directe de ErrorMessageFormatter:")
    
    # Erreur d'exécution
    error_msg = ErrorMessageFormatter.format_execution_error(
        "Commande inconnue: INVALID_CMD",
        ["INPUT", "RESIZE 9x9", "INVALID_CMD"],
        5
    )
    print(f"   Erreur d'exécution: {error_msg}")
    
    # Erreur de comparaison de grilles
    generated_grid = [[1, 1], [1, 1]]
    expected_grid = [[7, 0, 7], [7, 0, 7], [7, 7, 0]]
    error_msg = ErrorMessageFormatter.format_grid_comparison_error(
        generated_grid, expected_grid, 2, 9
    )
    print(f"   Erreur de grille: {error_msg}")
    
    print()
    
    # Méthode 2: Utilisation avec ValidationErrorContext
    print("2. Utilisation avec ValidationErrorContext:")
    
    context = ValidationErrorContext("007bbfb7", 0, "training")
    
    # Erreur de chargement de tâche
    error_msg = context.format_error("task_loading")
    print(f"   Erreur de chargement: {error_msg}")
    
    # Erreur d'index invalide
    error_msg = context.format_error("invalid_test_index", max_index=2)
    print(f"   Index invalide: {error_msg}")
    
    print()
    
    # Méthode 3: Utilisation de la fonction utilitaire
    print("3. Utilisation de la fonction utilitaire:")
    
    error_msg = format_validation_error(
        "execution",
        "007bbfb7", 
        0,
        error_msg="Syntaxe invalide",
        history=["INPUT", "RESIZE 9x9"],
        total_commands=4
    )
    print(f"   Erreur formatée: {error_msg}")
    
    print()
    
    # Méthode 4: Création d'un contexte réutilisable
    print("4. Contexte réutilisable:")
    
    context = create_error_context("abc123", 1, "evaluation")
    
    errors = [
        context.format_error("missing_output"),
        context.format_error("no_grid_generated", total_commands=3),
        context.format_error("internal", error="Division par zéro")
    ]
    
    for i, error in enumerate(errors, 1):
        print(f"   Erreur {i}: {error}")
    
    print()
    
    # Méthode 5: Résumé de validation
    print("5. Résumé de validation:")
    
    summary = ErrorMessageFormatter.format_validation_summary(
        total_scenarios=10,
        successful=7,
        failed=3,
        success_rate=70.0
    )
    print(f"   {summary}")


def simulate_validation_workflow():
    """
    Simule un workflow de validation complet avec gestion d'erreurs améliorée.
    """
    print("\n" + "="*60)
    print("SIMULATION D'UN WORKFLOW DE VALIDATION")
    print("="*60)
    
    # Simuler différents scénarios d'erreur
    scenarios = [
        {
            "task_id": "007bbfb7",
            "test_index": 0,
            "status": "success",
            "description": "Scénario valide"
        },
        {
            "task_id": "abc123",
            "test_index": 0,
            "status": "task_not_found",
            "description": "Tâche inexistante"
        },
        {
            "task_id": "def456",
            "test_index": 5,
            "status": "invalid_index",
            "max_index": 2,
            "description": "Index de test invalide"
        },
        {
            "task_id": "ghi789",
            "test_index": 0,
            "status": "execution_error",
            "error": "Commande INVALID_ACTION non reconnue",
            "history": ["INPUT", "RESIZE 9x9", "INVALID_ACTION"],
            "total_commands": 5,
            "description": "Erreur d'exécution"
        },
        {
            "task_id": "jkl012",
            "test_index": 0,
            "status": "grid_mismatch",
            "generated_grid": [[1, 1], [1, 1]],
            "expected_grid": [[7, 0, 7], [7, 0, 7], [7, 7, 0]],
            "matching_cells": 0,
            "total_cells": 9,
            "description": "Grille incorrecte"
        }
    ]
    
    successful = 0
    failed = 0
    
    for i, scenario in enumerate(scenarios, 1):
        task_id = scenario["task_id"]
        test_index = scenario["test_index"]
        status = scenario["status"]
        description = scenario["description"]
        
        print(f"\n[{i}/{len(scenarios)}] 🔍 {task_id}_TEST{test_index} - {description}")
        
        if status == "success":
            print("   ✅ VALIDE")
            successful += 1
        else:
            # Utiliser le contexte d'erreur pour formater le message
            context = create_error_context(task_id, test_index)
            
            if status == "task_not_found":
                error_msg = context.format_error("task_loading")
            elif status == "invalid_index":
                error_msg = context.format_error("invalid_test_index", max_index=scenario["max_index"])
            elif status == "execution_error":
                error_msg = context.format_error(
                    "execution",
                    error_msg=scenario["error"],
                    history=scenario["history"],
                    total_commands=scenario["total_commands"]
                )
            elif status == "grid_mismatch":
                error_msg = ErrorMessageFormatter.format_grid_comparison_error(
                    scenario["generated_grid"],
                    scenario["expected_grid"],
                    scenario["matching_cells"],
                    scenario["total_cells"]
                )
            else:
                error_msg = f"Erreur de type inconnu: {status}"
            
            print(f"   ❌ ÉCHEC - {error_msg}")
            failed += 1
    
    # Résumé final
    print("\n" + "="*60)
    print("BILAN FINAL")
    print("="*60)
    
    success_rate = (successful / len(scenarios) * 100) if scenarios else 0
    summary = ErrorMessageFormatter.format_validation_summary(
        len(scenarios), successful, failed, success_rate
    )
    print(summary)


if __name__ == "__main__":
    example_validation_with_improved_errors()
    simulate_validation_workflow()