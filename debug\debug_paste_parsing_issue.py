#!/usr/bin/env python3
"""
Debug du problème de parsing de PASTE [0,1 1,2]
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from command_system.unified_command import UnifiedCommand

def test_paste_parsing():
    """Test du parsing de différentes commandes PASTE"""
    
    test_commands = [
        "PASTE [0,1]",           # Une seule position
        "PASTE [0,1 1,2]",       # Rectangle ou deux positions ?
        "PASTE [0,1] [1,2]",     # Deux positions explicites
        "PASTE ([0,1] [1,2])",   # Deux positions avec parenthèses
    ]
    
    for cmd_str in test_commands:
        print(f"\n--- Test: {cmd_str} ---")
        
        cmd = UnifiedCommand.parse(cmd_str)
        if cmd:
            print(f"Action: {cmd.action}")
            print(f"Parameters: {cmd.parameters}")
            print(f"Coordinates: {cmd.coordinates}")
            print(f"Raw command: {getattr(cmd, 'raw_command', 'N/A')}")
            
            # Analyser l'interprétation
            if len(cmd.coordinates) == 2:
                print(f"Interprétation: Une position ({cmd.coordinates[0]}, {cmd.coordinates[1]})")
            elif len(cmd.coordinates) == 4:
                print(f"Interprétation: Deux positions ({cmd.coordinates[0]}, {cmd.coordinates[1]}) et ({cmd.coordinates[2]}, {cmd.coordinates[3]})")
            else:
                print(f"Interprétation: {len(cmd.coordinates)//2} positions")
        else:
            print("❌ Parsing échoué")

def analyze_expected_behavior():
    """Analyser le comportement attendu selon la documentation"""
    
    print("\n" + "="*60)
    print("🔍 Analyse du comportement attendu...")
    
    print("\nSelon la documentation FONCTIONNEMENT_COMMANDES_SCENARIOS.md:")
    print("- Format des coordonnées: [ligne,colonne] ou [ligne1,colonne1 ligne2,colonne2]")
    print("- [0,1 1,2] devrait être interprété comme un rectangle de (0,1) à (1,2)")
    print("- Mais PASTE colle à des positions spécifiques, pas dans des rectangles")
    
    print("\nProblème identifié:")
    print("- PASTE [0,1 1,2] est parsé comme coordinates = [0, 1, 1, 2]")
    print("- L'implémentation actuelle traite cela comme deux positions: (0,1) et (1,2)")
    print("- Mais le comportement attendu suggère une seule position: (0,1)")
    
    print("\nSolutions possibles:")
    print("1. Modifier le parsing pour traiter [0,1 1,2] comme une seule coordonnée")
    print("2. Modifier PASTE pour utiliser le système de parsing générique")
    print("3. Clarifier la spécification de PASTE dans la documentation")

def test_frontend_behavior():
    """Analyser le comportement du frontend"""
    
    print("\n" + "="*60)
    print("🔍 Analyse du comportement frontend...")
    
    print("D'après le code frontend (useAutomation.ts):")
    print("- executeUnifiedPaste() prend coordinates[0].split(',').map(Number)")
    print("- Il utilise seulement la PREMIÈRE coordonnée")
    print("- Donc PASTE [0,1 1,2] utilise seulement [0,1] dans le frontend")
    
    print("\nIncohérence identifiée:")
    print("- Frontend: utilise seulement la première coordonnée")
    print("- Backend: utilise toutes les coordonnées par paires")
    print("- Résultat: comportements différents entre frontend et backend")

if __name__ == "__main__":
    test_paste_parsing()
    analyze_expected_behavior()
    test_frontend_behavior()