[{"resource": "/c:/Users/<USER>/Documents/Projets/arc-puzzle/debug_init_dimensions.py", "owner": "pylance", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/microsoft/pylance-release/blob/main/docs/diagnostics/reportOptionalMemberAccess.md", "scheme": "https", "authority": "github.com"}}, "severity": 8, "message": "\"action\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 22, "startColumn": 38, "endLineNumber": 22, "endColumn": 44, "origin": "extHost5"}, {"resource": "/c:/Users/<USER>/Documents/Projets/arc-puzzle/debug_init_dimensions.py", "owner": "pylance", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/microsoft/pylance-release/blob/main/docs/diagnostics/reportOptionalMemberAccess.md", "scheme": "https", "authority": "github.com"}}, "severity": 8, "message": "\"parameters\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 22, "startColumn": 68, "endLineNumber": 22, "endColumn": 78, "origin": "extHost5"}, {"resource": "/c:/Users/<USER>/Documents/Projets/arc-puzzle/debug_init_dimensions.py", "owner": "pylance", "code": {"value": "reportArgumentType", "target": {"$mid": 1, "path": "/microsoft/pylance-release/blob/main/docs/diagnostics/reportArgumentType.md", "scheme": "https", "authority": "github.com"}}, "severity": 8, "message": "Argument of type \"UnifiedCommand | None\" cannot be assigned to parameter \"unified_cmd\" of type \"UnifiedCommand\" in function \"_execute_unified_command\"\n  Type \"UnifiedCommand | None\" is not assignable to type \"UnifiedCommand\"\n    \"None\" is not assignable to \"UnifiedCommand\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 24, "startColumn": 42, "endLineNumber": 24, "endColumn": 50, "origin": "extHost5"}, {"resource": "/c:/Users/<USER>/Documents/Projets/arc-puzzle/debug_init_dimensions.py", "owner": "pylance", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/microsoft/pylance-release/blob/main/docs/diagnostics/reportOptionalMemberAccess.md", "scheme": "https", "authority": "github.com"}}, "severity": 8, "message": "\"shape\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 25, "startColumn": 46, "endLineNumber": 25, "endColumn": 51, "origin": "extHost5"}, {"resource": "/c:/Users/<USER>/Documents/Projets/arc-puzzle/debug_init_dimensions.py", "owner": "pylance", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/microsoft/pylance-release/blob/main/docs/diagnostics/reportOptionalMemberAccess.md", "scheme": "https", "authority": "github.com"}}, "severity": 8, "message": "\"action\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 36, "startColumn": 39, "endLineNumber": 36, "endColumn": 45, "origin": "extHost5"}, {"resource": "/c:/Users/<USER>/Documents/Projets/arc-puzzle/debug_init_dimensions.py", "owner": "pylance", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/microsoft/pylance-release/blob/main/docs/diagnostics/reportOptionalMemberAccess.md", "scheme": "https", "authority": "github.com"}}, "severity": 8, "message": "\"parameters\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 36, "startColumn": 70, "endLineNumber": 36, "endColumn": 80, "origin": "extHost5"}, {"resource": "/c:/Users/<USER>/Documents/Projets/arc-puzzle/debug_init_dimensions.py", "owner": "pylance", "code": {"value": "reportArgumentType", "target": {"$mid": 1, "path": "/microsoft/pylance-release/blob/main/docs/diagnostics/reportArgumentType.md", "scheme": "https", "authority": "github.com"}}, "severity": 8, "message": "Argument of type \"UnifiedCommand | None\" cannot be assigned to parameter \"unified_cmd\" of type \"UnifiedCommand\" in function \"_execute_unified_command\"\n  Type \"UnifiedCommand | None\" is not assignable to type \"UnifiedCommand\"\n    \"None\" is not assignable to \"UnifiedCommand\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 38, "startColumn": 43, "endLineNumber": 38, "endColumn": 52, "origin": "extHost5"}, {"resource": "/c:/Users/<USER>/Documents/Projets/arc-puzzle/debug_init_dimensions.py", "owner": "pylance", "code": {"value": "reportOptionalMemberAccess", "target": {"$mid": 1, "path": "/microsoft/pylance-release/blob/main/docs/diagnostics/reportOptionalMemberAccess.md", "scheme": "https", "authority": "github.com"}}, "severity": 8, "message": "\"shape\" is not a known attribute of \"None\"", "source": "<PERSON><PERSON><PERSON>", "startLineNumber": 39, "startColumn": 47, "endLineNumber": 39, "endColumn": 52, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "2307", "severity": 8, "message": "Cannot find module './useProposalContext' or its corresponding type declarations.", "source": "ts", "startLineNumber": 6, "startColumn": 36, "endLineNumber": 6, "endColumn": 58, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "2345", "severity": 8, "message": "Argument of type 'string[][]' is not assignable to parameter of type 'string'.", "source": "ts", "startLineNumber": 379, "startColumn": 33, "endLineNumber": 379, "endColumn": 49, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'row1' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 379, "startColumn": 61, "endLineNumber": 379, "endColumn": 65, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'col1' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 379, "startColumn": 67, "endLineNumber": 379, "endColumn": 71, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'row2' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 379, "startColumn": 73, "endLineNumber": 379, "endColumn": 77, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'col2' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 379, "startColumn": 79, "endLineNumber": 379, "endColumn": 83, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "2304", "severity": 8, "message": "Cannot find name 'additionalCoords'.", "source": "ts", "startLineNumber": 986, "startColumn": 29, "endLineNumber": 986, "endColumn": 45, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "2345", "severity": 8, "message": "Argument of type 'any[][]' is not assignable to parameter of type 'string'.", "source": "ts", "startLineNumber": 998, "startColumn": 33, "endLineNumber": 998, "endColumn": 40, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'row1' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 998, "startColumn": 56, "endLineNumber": 998, "endColumn": 60, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'col1' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 998, "startColumn": 62, "endLineNumber": 998, "endColumn": 66, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'row2' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 998, "startColumn": 68, "endLineNumber": 998, "endColumn": 72, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'col2' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 998, "startColumn": 74, "endLineNumber": 998, "endColumn": 78, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "2345", "severity": 8, "message": "Argument of type 'string[][]' is not assignable to parameter of type 'string'.", "source": "ts", "startLineNumber": 1088, "startColumn": 29, "endLineNumber": 1088, "endColumn": 45, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'row1' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1088, "startColumn": 57, "endLineNumber": 1088, "endColumn": 61, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'col1' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1088, "startColumn": 63, "endLineNumber": 1088, "endColumn": 67, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'row2' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1088, "startColumn": 69, "endLineNumber": 1088, "endColumn": 73, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'col2' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1088, "startColumn": 75, "endLineNumber": 1088, "endColumn": 79, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "2345", "severity": 8, "message": "Argument of type 'string[][]' is not assignable to parameter of type 'string'.", "source": "ts", "startLineNumber": 1140, "startColumn": 29, "endLineNumber": 1140, "endColumn": 45, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'row1' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1140, "startColumn": 61, "endLineNumber": 1140, "endColumn": 65, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'col1' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1140, "startColumn": 67, "endLineNumber": 1140, "endColumn": 71, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'row2' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1140, "startColumn": 73, "endLineNumber": 1140, "endColumn": 77, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'col2' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1140, "startColumn": 79, "endLineNumber": 1140, "endColumn": 83, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "2345", "severity": 8, "message": "Argument of type 'string[][]' is not assignable to parameter of type 'string'.", "source": "ts", "startLineNumber": 1226, "startColumn": 29, "endLineNumber": 1226, "endColumn": 45, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'row1' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1226, "startColumn": 61, "endLineNumber": 1226, "endColumn": 65, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'col1' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1226, "startColumn": 67, "endLineNumber": 1226, "endColumn": 71, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'row2' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1226, "startColumn": 73, "endLineNumber": 1226, "endColumn": 77, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'col2' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1226, "startColumn": 79, "endLineNumber": 1226, "endColumn": 83, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "2345", "severity": 8, "message": "Argument of type 'string[][]' is not assignable to parameter of type 'string'.", "source": "ts", "startLineNumber": 1618, "startColumn": 29, "endLineNumber": 1618, "endColumn": 45, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'row1' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1618, "startColumn": 57, "endLineNumber": 1618, "endColumn": 61, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'col1' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1618, "startColumn": 63, "endLineNumber": 1618, "endColumn": 67, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'row2' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1618, "startColumn": 69, "endLineNumber": 1618, "endColumn": 73, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "7006", "severity": 8, "message": "Parameter 'col2' implicitly has an 'any' type.", "source": "ts", "startLineNumber": 1618, "startColumn": 75, "endLineNumber": 1618, "endColumn": 79, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'coordinates' does not exist on type 'UnifiedCommand'.", "source": "ts", "startLineNumber": 2130, "startColumn": 33, "endLineNumber": 2130, "endColumn": 44, "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "2552", "severity": 8, "message": "Cannot find name 'executeUnifiedFill'. Did you mean 'executeUnifiedFlip'?", "source": "ts", "startLineNumber": 2145, "startColumn": 26, "endLineNumber": 2145, "endColumn": 44, "relatedInformation": [{"startLineNumber": 765, "startColumn": 16, "endLineNumber": 765, "endColumn": 34, "message": "'executeUnifiedFlip' is declared here.", "resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts"}], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'useProposalContext' is declared but its value is never read.", "source": "ts", "startLineNumber": 6, "startColumn": 1, "endLineNumber": 6, "endColumn": 59, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'processCoordinateBlocksSync' is declared but its value is never read.", "source": "ts", "startLineNumber": 205, "startColumn": 10, "endLineNumber": 205, "endColumn": 37, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'executeUnifiedClear' is declared but its value is never read.", "source": "ts", "startLineNumber": 361, "startColumn": 16, "endLineNumber": 361, "endColumn": 35, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'coordinates' is declared but its value is never read.", "source": "ts", "startLineNumber": 361, "startColumn": 36, "endLineNumber": 361, "endColumn": 47, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'additionalCoords' is declared but its value is never read.", "source": "ts", "startLineNumber": 361, "startColumn": 59, "endLineNumber": 361, "endColumn": 75, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'executeUnifiedSelect' is declared but its value is never read.", "source": "ts", "startLineNumber": 406, "startColumn": 16, "endLineNumber": 406, "endColumn": 36, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'grid' is declared but its value is never read.", "source": "ts", "startLineNumber": 432, "startColumn": 89, "endLineNumber": 432, "endColumn": 93, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'setGrid' is declared but its value is never read.", "source": "ts", "startLineNumber": 432, "startColumn": 108, "endLineNumber": 432, "endColumn": 115, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'rawCommand' is declared but its value is never read.", "source": "ts", "startLineNumber": 432, "startColumn": 136, "endLineNumber": 432, "endColumn": 146, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'grid' is declared but its value is never read.", "source": "ts", "startLineNumber": 484, "startColumn": 89, "endLineNumber": 484, "endColumn": 93, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'setGrid' is declared but its value is never read.", "source": "ts", "startLineNumber": 484, "startColumn": 108, "endLineNumber": 484, "endColumn": 115, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'grid' is declared but its value is never read.", "source": "ts", "startLineNumber": 505, "startColumn": 91, "endLineNumber": 505, "endColumn": 95, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'setGrid' is declared but its value is never read.", "source": "ts", "startLineNumber": 505, "startColumn": 110, "endLineNumber": 505, "endColumn": 117, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'grid' is declared but its value is never read.", "source": "ts", "startLineNumber": 529, "startColumn": 91, "endLineNumber": 529, "endColumn": 95, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'setGrid' is declared but its value is never read.", "source": "ts", "startLineNumber": 529, "startColumn": 110, "endLineNumber": 529, "endColumn": 117, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'grid' is declared but its value is never read.", "source": "ts", "startLineNumber": 567, "startColumn": 57, "endLineNumber": 567, "endColumn": 61, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'rawCommand' is declared but its value is never read.", "source": "ts", "startLineNumber": 567, "startColumn": 76, "endLineNumber": 567, "endColumn": 86, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'grid' is declared but its value is never read.", "source": "ts", "startLineNumber": 573, "startColumn": 56, "endLineNumber": 573, "endColumn": 60, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'rawCommand' is declared but its value is never read.", "source": "ts", "startLineNumber": 573, "startColumn": 75, "endLineNumber": 573, "endColumn": 85, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'grid' is declared but its value is never read.", "source": "ts", "startLineNumber": 579, "startColumn": 58, "endLineNumber": 579, "endColumn": 62, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'setGrid' is declared but its value is never read.", "source": "ts", "startLineNumber": 579, "startColumn": 77, "endLineNumber": 579, "endColumn": 84, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'grid' is declared but its value is never read.", "source": "ts", "startLineNumber": 585, "startColumn": 60, "endLineNumber": 585, "endColumn": 64, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'executeUnifiedExtract' is declared but its value is never read.", "source": "ts", "startLineNumber": 627, "startColumn": 16, "endLineNumber": 627, "endColumn": 37, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'executeUnifiedMultiply' is declared but its value is never read.", "source": "ts", "startLineNumber": 654, "startColumn": 16, "endLineNumber": 654, "endColumn": 38, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'executeUnifiedDivide' is declared but its value is never read.", "source": "ts", "startLineNumber": 719, "startColumn": 16, "endLineNumber": 719, "endColumn": 36, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'executeUnifiedReplace' is declared but its value is never read.", "source": "ts", "startLineNumber": 1053, "startColumn": 16, "endLineNumber": 1053, "endColumn": 37, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'additionalCoords' is declared but its value is never read.", "source": "ts", "startLineNumber": 1053, "startColumn": 93, "endLineNumber": 1053, "endColumn": 109, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'executeUnifiedResize' is declared but its value is never read.", "source": "ts", "startLineNumber": 1466, "startColumn": 16, "endLineNumber": 1466, "endColumn": 36, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'executeUnifiedSurround' is declared but its value is never read.", "source": "ts", "startLineNumber": 1600, "startColumn": 16, "endLineNumber": 1600, "endColumn": 38, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'executeUnifiedXor' is declared but its value is never read.", "source": "ts", "startLineNumber": 1653, "startColumn": 16, "endLineNumber": 1653, "endColumn": 33, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'executeUnifiedAnd' is declared but its value is never read.", "source": "ts", "startLineNumber": 1759, "startColumn": 16, "endLineNumber": 1759, "endColumn": 33, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'executeUnifiedOr' is declared but its value is never read.", "source": "ts", "startLineNumber": 1858, "startColumn": 16, "endLineNumber": 1858, "endColumn": 32, "tags": [1], "origin": "extHost5"}, {"resource": "/C:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/resolution/hooks/useAutomation.ts", "owner": "typescript", "code": "6133", "severity": 4, "message": "'executeUnifiedEdit' is declared but its value is never read.", "source": "ts", "startLineNumber": 2273, "startColumn": 16, "endLineNumber": 2273, "endColumn": 34, "tags": [1], "origin": "extHost5"}, {"resource": "/c:/Users/<USER>/Documents/Projets/arc-puzzle/frontend/src/components/unified/UnifiedScenarioEditor.tsx", "owner": "typescript", "code": "6133", "severity": 4, "message": "'handleSelectionChange' is declared but its value is never read.", "source": "ts", "startLineNumber": 103, "startColumn": 9, "endLineNumber": 103, "endColumn": 30, "tags": [1], "origin": "extHost5"}]