// Récupérer les données de la tâche
const trainInputs = getTrainInputData();
const trainOutputs = getTrainOutputData();
const testInput = getTestInputData()[0]; // Premier exemple de test

// Afficher les informations sur la tâche
console.log("Type de tâche:", await getTaskType());
console.log("Nombre d'exemples d'entraînement:", trainInputs.length);

// Analyser les exemples d'entraînement
for (let i = 0; i < trainInputs.length; i++) {
  const input = trainInputs[i];
  const output = trainOutputs[i];
  
  console.log(`\nExemple d'entraînement ${i+1}:`);
  console.log("Dimensions de l'entrée:", input.length, "x", input[0].length);
  console.log("Dimensions de la sortie:", output.length, "x", output[0].length);
  
  // Analyser les couleurs utilisées
  const inputColors = new Set(input.flat());
  const outputColors = new Set(output.flat());
  
  console.log("Couleurs dans l'entrée:", Array.from(inputColors));
  console.log("Couleurs dans la sortie:", Array.from(outputColors));
  
  // Vérifier si la taille a changé
  if (input.length !== output.length || input[0].length !== output[0].length) {
    console.log("⚠️ La taille a changé entre l'entrée et la sortie");
  }
}

// Analyser l'exemple de test
console.log("\nExemple de test:");
console.log("Dimensions:", testInput.length, "x", testInput[0].length);
console.log("Couleurs:", Array.from(new Set(testInput.flat())));

// Générer des commandes d'automatisation basiques
const height = testInput.length;
const width = testInput[0].length;

// Liste des commandes à générer
const commands = [];

// Initialiser la grille
commands.push(`INIT ${width} ${height}`);

// Remplir la grille avec les valeurs de l'entrée de test
for (let y = 0; y < height; y++) {
  for (let x = 0; x < width; x++) {
    const value = testInput[y][x];
    if (value !== 0) { // Supposons que 0 est la valeur par défaut
      commands.push(`EDIT ${x} ${y} ${value}`);
    }
  }
}

// Afficher les commandes générées
console.log("\nCommandes générées:");
commands.forEach(cmd => console.log(cmd));

// Retourner les commandes pour qu'elles puissent être transférées à l'éditeur d'automatisation
return commands;
