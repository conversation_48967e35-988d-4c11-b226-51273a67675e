{"cells": [{"cell_type": "markdown", "source": ["# Analyse de tâche ARC Clean\n", "\n", "Ce notebook permet d'analyser une tâche ARC et d'explorer différentes approches pour la résoudre."], "metadata": {}}, {"cell_type": "code", "source": ["# Importer le module arc_utils et les bibliothèques nécessaires\n", "import arc_utils\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import json\n", "import sys\n", "\n", "# Vérifier que le module est correctement chargé\n", "print(\"Module arc_utils chargé avec succès!\")\n", "\n", "# Fonction pour afficher une grille\n", "def display_grid(grid, title=None):\n", "    \"\"\"Affiche une grille ARC avec un titre optionnel.\n", "    \n", "    Args:\n", "        grid (list or numpy.ndarray): La grille à afficher\n", "        title (str, optional): Le titre à afficher au-dessus de la grille\n", "    \"\"\"\n", "    if isinstance(grid, np.ndarray):\n", "        grid = grid.tolist()\n", "        \n", "    plt.figure(figsize=(5, 5))\n", "    plt.imshow(grid, cmap='tab10', vmin=0, vmax=9)\n", "    plt.grid(True, color='black', linestyle='-', linewidth=1)\n", "    plt.xticks(np.arange(-0.5, len(grid[0]), 1), [])\n", "    plt.yticks(np.arange(-0.5, len(grid), 1), [])\n", "    \n", "    # Ajouter les valeurs dans les cellules\n", "    for i in range(len(grid)):\n", "        for j in range(len(grid[0])):\n", "            plt.text(j, i, str(grid[i][j]), ha='center', va='center', color='white', fontweight='bold')\n", "    \n", "    if title:\n", "        plt.title(title)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Fonction pour envoyer des commandes à l'éditeur d'automatisation\n", "def send_commands(commands):\n", "    try:\n", "        # Vérifier si nous sommes dans Pyodide\n", "        if 'js' in sys.modules:\n", "            # Importer le module js pour interagir avec JavaScript\n", "            import js\n", "            \n", "            # Convertir les commandes en JSON\n", "            commands_json = json.dumps(commands)\n", "            \n", "            # Envoyer les commandes au frontend\n", "            if hasattr(js.window.parent, 'notebookCommandsCallback'):\n", "                js.window.parent.notebookCommandsCallback(commands_json)\n", "                print('Commandes envoyées au frontend')\n", "            else:\n", "                print('Callback pour les commandes non disponible dans le frontend')\n", "        else:\n", "            # Nous ne sommes pas dans Pyodide, afficher simplement les commandes\n", "            print('Commandes générées (non envoyées au frontend):') \n", "            for cmd in commands[:5]:  # <PERSON><PERSON><PERSON><PERSON> les 5 premières commandes\n", "                print(f\"  {cmd}\")\n", "            if len(commands) > 5:\n", "                print(\"  ...\")\n", "                for cmd in commands[-2:]:  # A<PERSON><PERSON><PERSON> les 2 dernières commandes\n", "                    print(f\"  {cmd}\")\n", "    except Exception as e:\n", "        print(f'Erreur lors de l\\'envoi des commandes au frontend: {e}')"], "metadata": {}, "outputs": []}, {"cell_type": "code", "source": ["# Variables pour stocker les données de la tâche\n", "task_id = None\n", "train_input = []\n", "train_output = []\n", "test_input = []\n", "train_examples = []\n", "test_example = None\n", "\n", "# Dan<PERSON>, les données de la tâche sont injectées directement par PythonExecutor.ts\n", "# Vérifier si les variables globales task_id, train_input, train_output et test_input existent déjà\n", "if 'task_id' in globals() and task_id is not None:\n", "    print(f\"Tâche ARC chargée: {task_id}\")\n", "    if 'train_input' in globals() and 'train_output' in globals() and len(train_input) == len(train_output):\n", "        train_examples = [{'input': inp, 'output': out} for inp, out in zip(train_input, train_output)]\n", "        print(f\"Exemples d'entraînement: {len(train_examples)}\")\n", "    if 'test_input' in globals() and len(test_input) > 0:\n", "        test_example = {'input': test_input[0]}\n", "        print(f\"Exemples de test: {len(test_input)}\")\n", "else:\n", "    # Si les données ne sont pas injectées, essayer de les récupérer depuis le frontend\n", "    try:\n", "        # Vérifier si nous sommes dans Pyodide\n", "        if 'js' in sys.modules:\n", "            # Nous sommes dans Pyodide, essayer <PERSON> r<PERSON><PERSON><PERSON><PERSON> la tâche courante\n", "            import js\n", "            \n", "            # Récupérer l'ID de la tâche courante depuis js.window.parent.taskName\n", "            current_task_id = None\n", "            if hasattr(js.window.parent, 'taskName'):\n", "                current_task_id = js.window.parent.taskName\n", "                print(f\"ID de la tâche courante récupéré: {current_task_id}\")\n", "            \n", "            # Récupérer les données de la tâche courante depuis js.window.parent.currentTask\n", "            if hasattr(js.window.parent, 'currentTask'):\n", "                task_data = js.window.parent.currentTask\n", "                task_id = current_task_id or 'unknown'\n", "                \n", "                # Extraire les données d'entrée et de sortie\n", "                train_input = [pair.input for pair in task_data.train]\n", "                train_output = [pair.output for pair in task_data.train]\n", "                test_input = [pair.input for pair in task_data.test]\n", "                \n", "                # <PERSON><PERSON><PERSON> les exemples\n", "                train_examples = [{'input': inp, 'output': out} for inp, out in zip(train_input, train_output)]\n", "                if test_input and len(test_input) > 0:\n", "                    test_example = {'input': test_input[0]}\n", "                \n", "                print(f\"Tâche {task_id} chargée avec succès depuis le frontend\")\n", "            else:\n", "                print(\"Aucune tâche disponible dans le frontend\")\n", "    except Exception as e:\n", "        print(f\"Erreur lors du chargement de la tâche: {e}\")\n", "\n", "# Afficher un résumé\n", "print(f\"Nombre d'exemples d'entraînement: {len(train_examples)}\")\n", "print(f\"Exemple de test disponible: {test_example is not None}\")"], "metadata": {}, "outputs": []}, {"cell_type": "code", "source": ["# Visualiser les exemples d'entraînement\n", "for i, example in enumerate(train_examples):\n", "    print(f\"\\nExemple d'entraînement {i+1}:\")\n", "    \n", "    print(\"Input:\")\n", "    display_grid(example[\"input\"], f\"Entrée {i+1}\")\n", "    \n", "    print(\"Output:\")\n", "    display_grid(example[\"output\"], f\"Sortie {i+1}\")"], "metadata": {}, "outputs": []}, {"cell_type": "code", "source": ["# Visualiser l'exemple de test\n", "if test_example:\n", "    print(\"\\nExemple de test:\")\n", "    print(\"Input:\")\n", "    display_grid(test_example[\"input\"], \"Entrée de test\")\n", "else:\n", "    print(\"\\nAucun exemple de test disponible.\")"], "metadata": {}, "outputs": []}, {"cell_type": "markdown", "source": ["## Analyse des exemples\n", "\n", "Analysons les exemples pour comprendre la transformation entre l'entrée et la sortie."], "metadata": {}}, {"cell_type": "code", "source": ["# Fonctions d'analyse\n", "def analyze_patterns(grid):\n", "    \"\"\"Analyse les patterns dans une grille\"\"\"\n", "    if isinstance(grid, list):\n", "        grid = np.array(grid)\n", "    \n", "    # Dimensions de la grille\n", "    height, width = grid.shape\n", "    print(f\"Dimensions: {height}x{width}\")\n", "    \n", "    # Valeurs uniques et leur fréquence\n", "    values, counts = np.unique(grid, return_counts=True)\n", "    print(\"\\nValeurs uniques et leur fréquence:\")\n", "    for val, count in zip(values, counts):\n", "        print(f\"  Valeur {val}: {count} occurrences ({count/(height*width)*100:.1f}%)\")\n", "    \n", "    # Recherche de symétries\n", "    print(\"\\nAnalyse des symétries:\")\n", "    \n", "    # Symétrie horizontale\n", "    is_h_symmetric = np.array_equal(grid, np.flipud(grid))\n", "    print(f\"  Symétrie horizontale: {'Oui' if is_h_symmetric else 'Non'}\")\n", "    \n", "    # Symétrie verticale\n", "    is_v_symmetric = np.array_equal(grid, np.fliplr(grid))\n", "    print(f\"  Symétrie verticale: {'Oui' if is_v_symmetric else 'Non'}\")\n", "    \n", "    # <PERSON><PERSON><PERSON><PERSON><PERSON> diagonale (si la grille est carrée)\n", "    if height == width:\n", "        is_diag_symmetric = np.array_equal(grid, grid.T)\n", "        print(f\"  Symétrie diagonale: {'Oui' if is_diag_symmetric else 'Non'}\")\n", "\n", "def analyze_transformations(input_grid, output_grid):\n", "    \"\"\"Analyse les transformations entre deux grilles\"\"\"\n", "    if isinstance(input_grid, list):\n", "        input_grid = np.array(input_grid)\n", "    if isinstance(output_grid, list):\n", "        output_grid = np.array(output_grid)\n", "    \n", "    # Vérifier si les dimensions sont les mêmes\n", "    if input_grid.shape != output_grid.shape:\n", "        print(f\"Les dimensions sont différentes: {input_grid.shape} -> {output_grid.shape}\")\n", "        return\n", "    \n", "    # Compter les cellules modifiées\n", "    diff = (input_grid != output_grid)\n", "    num_diff = np.sum(diff)\n", "    print(f\"Nombre de cellules modifiées: {num_diff} sur {input_grid.size} ({num_diff/input_grid.size*100:.1f}%)\")\n", "    \n", "    # Analyser les valeurs\n", "    input_values = np.unique(input_grid)\n", "    output_values = np.unique(output_grid)\n", "    print(f\"Valeurs dans l'entrée: {input_values}\")\n", "    print(f\"Valeurs dans la sortie: {output_values}\")\n", "    \n", "    # Analyser les transformations par valeur\n", "    print(\"\\nTransformations par valeur:\")\n", "    for val in input_values:\n", "        mask = (input_grid == val)\n", "        if np.sum(mask) > 0:\n", "            output_vals, counts = np.unique(output_grid[mask], return_counts=True)\n", "            print(f\"  Valeur {val} -> \", end=\"\")\n", "            for o_val, count in zip(output_vals, counts):\n", "                print(f\"{o_val}: {count} ({count/np.sum(mask)*100:.1f}%), \", end=\"\")\n", "            print()"], "metadata": {}, "outputs": []}, {"cell_type": "code", "source": ["# Analyser le premier exemple\n", "if train_examples:\n", "    example = train_examples[0]\n", "    \n", "    print(\"Analyse de la grille d'entrée:\")\n", "    analyze_patterns(example['input'])\n", "    \n", "    print(\"\\nAnalyse de la grille de sortie:\")\n", "    analyze_patterns(example['output'])\n", "    \n", "    print(\"\\nAnalyse des transformations:\")\n", "    analyze_transformations(example['input'], example['output'])"], "metadata": {}, "outputs": []}, {"cell_type": "markdown", "source": ["## Développement d'une solution\n", "\n", "Développons une solution pour la tâche ARC en fonction de l'analyse des exemples."], "metadata": {}}, {"cell_type": "code", "source": ["# Fonction pour transformer la grille d'entrée en grille de sortie\n", "def transform_grid(input_grid):\n", "    \"\"\"Transforme la grille d'entrée en grille de sortie selon la logique de la tâche.\n", "    \n", "    Args:\n", "        input_grid (list): La grille d'entrée\n", "        \n", "    Returns:\n", "        list: La grille de sortie transformée\n", "    \"\"\"\n", "    # Convertir en numpy array si ce n'est pas déjà le cas\n", "    if isinstance(input_grid, list):\n", "        grid = np.array(input_grid)\n", "    else:\n", "        grid = input_grid.copy()\n", "    \n", "    # TODO: Implémenter la logique de transformation spécifique à la tâche\n", "    # Pour l'instant, retourner simplement une copie de la grille d'entrée\n", "    output_grid = grid.copy()\n", "    \n", "    # Convertir en liste si l'entrée était une liste\n", "    if isinstance(input_grid, list):\n", "        return output_grid.tolist()\n", "    return output_grid"], "metadata": {}, "outputs": []}, {"cell_type": "code", "source": ["# Tester la solution sur les exemples d'entraînement\n", "for i, example in enumerate(train_examples):\n", "    input_grid = example[\"input\"]\n", "    expected_output = example[\"output\"]\n", "    \n", "    # Appliquer notre solution\n", "    predicted_output = transform_grid(input_grid)\n", "    \n", "    print(f\"\\nExemple d'entraînement {i+1}:\")\n", "    print(\"Sortie prédite:\")\n", "    display_grid(predicted_output, f\"Prédiction {i+1}\")\n", "    \n", "    print(\"Sortie attendue:\")\n", "    display_grid(expected_output, f\"Attendu {i+1}\")\n", "    \n", "    # Vérifier si la prédiction est correcte\n", "    is_correct = np.array_equal(np.array(predicted_output), np.array(expected_output))\n", "    print(f\"Prédiction correcte: {is_correct}\")"], "metadata": {}, "outputs": []}, {"cell_type": "code", "source": ["# Appliquer la solution à l'exemple de test\n", "if test_example:\n", "    test_input = test_example[\"input\"]\n", "    test_output = transform_grid(test_input)\n", "    \n", "    print(\"Solution proposée pour l'exemple de test:\")\n", "    display_grid(test_output, \"Solution proposée\")\n", "    \n", "    # <PERSON><PERSON><PERSON><PERSON> et valider les commandes\n", "    commands = arc_utils.generate_commands_from_grid(test_output)\n", "    validation_result = arc_utils.validate_commands(commands)\n", "    \n", "    print(f\"\\nCommandes générées ({len(commands)} commandes):\")\n", "    for cmd in commands[:5]:  # <PERSON><PERSON><PERSON><PERSON> les 5 premières commandes\n", "        print(f\"  {cmd}\")\n", "    if len(commands) > 5:\n", "        print(\"  ...\")\n", "        for cmd in commands[-2:]:  # A<PERSON><PERSON><PERSON> les 2 dernières commandes\n", "            print(f\"  {cmd}\")\n", "    \n", "    print(f\"\\nValidation des commandes: {'valide' if validation_result['valid'] else 'invalide'}\")\n", "    \n", "    # Envoyer les commandes au frontend\n", "    send_commands(commands)\n", "else:\n", "    print(\"Aucun exemple de test disponible.\")"], "metadata": {}, "outputs": []}], "metadata": {"language_info": {"codemirror_mode": {"name": "python", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8"}, "kernelspec": {"display_name": "Python (Pyodide)", "language": "python", "name": "python"}}, "nbformat": 4, "nbformat_minor": 4}