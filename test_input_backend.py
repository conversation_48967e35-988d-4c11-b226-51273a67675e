#!/usr/bin/env python3
"""
Test de la commande INPUT côté backend
"""

import sys
import os

# Ajouter le répertoire backend au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_input_validation():
    """Test de validation de la commande INPUT"""
    print("🧪 Test de validation de la commande INPUT")
    
    try:
        from command_system.command_validator import CommandValidator
        
        validator = CommandValidator()
        
        # Test 1: Commande INPUT simple
        commands = ['INPUT']
        result = validator.validate_scenario(commands)
        
        print(f"Test 1 - INPUT simple:")
        print(f"  Commandes: {commands}")
        print(f"  Valide: {result['is_valid']}")
        if not result['is_valid']:
            print(f"  Erreurs: {result['errors']}")
        
        # Test 2: Scénario avec INPUT + autres commandes
        commands = ['INPUT', 'FILL 5 [1,1]', 'END']
        result = validator.validate_scenario(commands)
        
        print(f"\nTest 2 - Scénario avec INPUT:")
        print(f"  Commandes: {commands}")
        print(f"  Valide: {result['is_valid']}")
        if not result['is_valid']:
            print(f"  Erreurs: {result['errors']}")
        
        # Test 3: INPUT avec paramètres (doit échouer)
        commands = ['INPUT [1,1]']
        result = validator.validate_scenario(commands)
        
        print(f"\nTest 3 - INPUT avec paramètres (doit échouer):")
        print(f"  Commandes: {commands}")
        print(f"  Valide: {result['is_valid']}")
        if not result['is_valid']:
            print(f"  Erreurs: {result['errors']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test de validation: {e}")
        return False

def test_input_execution():
    """Test d'exécution de la commande INPUT"""
    print("\n🧪 Test d'exécution de la commande INPUT")
    
    try:
        import json
        from command_system.command_executor import CommandExecutor
        from command_system.unified_command import UnifiedCommand
        
        # Charger des données de tâche pour le test
        task_file = 'arcdata/training/0e206a2e.json'
        if not os.path.exists(task_file):
            print(f"⚠️ Fichier de tâche non trouvé: {task_file}")
            print("   Test d'exécution ignoré - pas de données de tâche disponibles")
            return True  # Considérer comme réussi si pas de données
        
        with open(task_file, 'r') as f:
            task_data = json.load(f)
        
        # Créer un CommandExecutor avec les données de tâche
        executor = CommandExecutor()
        executor.task_data = task_data
        executor.test_index = 0
        
        # Créer une commande INPUT
        input_command = UnifiedCommand.parse('INPUT')
        
        print(f"Commande parsée:")
        print(f"  Action: {input_command.action}")
        print(f"  Paramètres: {input_command.parameters}")
        print(f"  Coordonnées: {input_command.coordinates}")
        
        # Tester l'exécution
        result = executor._cmd_input(input_command)
        
        print(f"\nRésultat d'exécution:")
        print(f"  Succès: {result}")
        if result:
            print(f"  Grille chargée: {executor.height}x{executor.width}")
        else:
            print(f"  Erreur: {executor.error}")
        
        return result
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'exécution: {e}")
        return False

def test_input_parsing():
    """Test de parsing de la commande INPUT"""
    print("\n🧪 Test de parsing de la commande INPUT")
    
    try:
        from command_system.unified_command import UnifiedCommand
        
        # Test parsing INPUT
        command_str = 'INPUT'
        parsed = UnifiedCommand.parse(command_str)
        
        print(f"Commande: '{command_str}'")
        print(f"Action: {parsed.action}")
        print(f"Paramètres: {parsed.parameters}")
        print(f"Coordonnées: {parsed.coordinates}")
        print(f"Type: {type(parsed).__name__}")
        
        # Vérifier que c'est bien une commande INPUT
        assert parsed.action == 'INPUT'
        assert len(parsed.parameters) == 0
        assert len(parsed.coordinates) == 0
        
        print("✅ Parsing INPUT réussi")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test de parsing: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("=" * 60)
    print("TEST DE LA COMMANDE INPUT - BACKEND")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Parsing
    if test_input_parsing():
        tests_passed += 1
        print("✅ Test parsing: RÉUSSI")
    else:
        print("❌ Test parsing: ÉCHOUÉ")
    
    # Test 2: Validation
    if test_input_validation():
        tests_passed += 1
        print("✅ Test validation: RÉUSSI")
    else:
        print("❌ Test validation: ÉCHOUÉ")
    
    # Test 3: Exécution
    if test_input_execution():
        tests_passed += 1
        print("✅ Test exécution: RÉUSSI")
    else:
        print("❌ Test exécution: ÉCHOUÉ")
    
    print("\n" + "=" * 60)
    print(f"RÉSULTATS: {tests_passed}/{total_tests} tests réussis")
    
    if tests_passed == total_tests:
        print("🎉 TOUS LES TESTS SONT PASSÉS!")
        return True
    else:
        print("⚠️  Certains tests ont échoué")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
