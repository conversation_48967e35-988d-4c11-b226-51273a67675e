#!/usr/bin/env python3
"""
Test de validation de la commande INPUT
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

from command_system.command_validator import CommandValidator

def test_input_validation():
    """Test de validation des commandes avec INPUT"""
    validator = CommandValidator()

    # Test des commandes individuelles et du scénario complet
    test_commands = [
        'INPUT',
        'END'
    ]

    print("=== Test de Validation des Commandes INPUT ===\n")

    try:
        result = validator.validate_commands(test_commands)
        status = "✅ VALIDE" if result['valid'] else "❌ INVALIDE"
        print(f"Scénario complet: {status}")

        if result['valid']:
            print(f"Commandes valides: {len(result['valid_commands'])}/{len(test_commands)}")
            for i, cmd in enumerate(test_commands):
                print(f"  {i+1}. \"{cmd}\" -> ✅")
        else:
            print(f"Erreurs trouvées: {len(result['errors'])}")
            for error in result['errors']:
                print(f"  Ligne {error['line']}: {error['error']}")
                print(f"    Commande: \"{error['command']}\"")

        print()

    except Exception as e:
        print(f"❌ ERREUR: {e}\n")

    # Test de comparaison avec TRANSFERT
    print("=== Comparaison TRANSFERT vs INPUT ===\n")

    old_transfert = 'TRANSFERT {INIT 3x3; EDIT 7 [0,0]; EDIT 7 [0,2]; EDIT 7 [1,1]}'
    new_input = 'INPUT'

    try:
        # Test TRANSFERT (devrait échouer car plus supporté)
        transfert_result = validator.validate_commands([old_transfert])
        transfert_valid = transfert_result['valid']

        # Test INPUT
        input_result = validator.validate_commands([new_input])
        input_valid = input_result['valid']

        print(f"TRANSFERT: {'✅ VALIDE' if transfert_valid else '❌ INVALIDE'}")
        if not transfert_valid:
            print(f"  Erreurs TRANSFERT: {transfert_result.get('errors', [])}")

        print(f"INPUT: {'✅ VALIDE' if input_valid else '❌ INVALIDE'}")
        if not input_valid:
            print(f"  Erreurs INPUT: {input_result.get('errors', [])}")

        print(f"\nLongueur TRANSFERT: {len(old_transfert)} caractères")
        print(f"Longueur INPUT: {len(new_input)} caractères")
        reduction = ((len(old_transfert) - len(new_input)) / len(old_transfert) * 100)
        print(f"Réduction: {reduction:.1f}%")

    except Exception as e:
        print(f"Erreur de comparaison: {e}")

if __name__ == "__main__":
    test_input_validation()
