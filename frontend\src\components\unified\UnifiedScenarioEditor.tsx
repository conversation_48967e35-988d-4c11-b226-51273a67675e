import React, { useState, useCallback } from 'react';
import { Grid } from '../../lib/grid';
import { UnifiedGridComponent } from './UnifiedGridComponent';
import { UnifiedActionToolbar } from './UnifiedActionToolbar';
import { useUnifiedSelection } from '../../hooks/useUnifiedSelection';
import { ScenarioHistory } from './ScenarioHistory';
//import { QuickOperatorPanel } from './QuickOperatorPanel';
import './UnifiedScenarioEditor.css';

interface UnifiedScenarioEditorProps {
  initialGrid: Grid;
  onGridChange?: (grid: Grid) => void;
  onScenarioChange?: (commands: string[]) => void;
}

export const UnifiedScenarioEditor: React.FC<UnifiedScenarioEditorProps> = ({
  initialGrid,
  onGridChange,
  onScenarioChange
}) => {
  const [grid, setGrid] = useState(initialGrid);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  
  const {
    selectionState,
    getUnifiedSelection,
    clearSelection,
    applyInverseSelection,
    applyColorFilter,
    applyXorOperator,
    applyAndOperator,
    applyOrOperator
  } = useUnifiedSelection(grid);

  // === GESTION DES CHANGEMENTS ===

  const handleGridChange = useCallback((newGrid: Grid) => {
    setGrid(newGrid);
    onGridChange?.(newGrid);
  }, [onGridChange]);

  const handleCommandExecuted = useCallback((command: string) => {
    const newHistory = [...commandHistory, command];
    setCommandHistory(newHistory);
    onScenarioChange?.(newHistory);
    
    // Optionnel : effacer la sélection après exécution
    clearSelection();
  }, [commandHistory, onScenarioChange, clearSelection]);

  // === ACTIONS DE L'INTERFACE ===

  const resetGrid = useCallback(() => {
    setGrid(initialGrid.clone());
    setCommandHistory([]);
    clearSelection();
    onGridChange?.(initialGrid.clone());
    onScenarioChange?.([]);
  }, [initialGrid, clearSelection, onGridChange, onScenarioChange]);

  const undoLastCommand = useCallback(() => {
    if (commandHistory.length === 0) return;
    
    // Pour un vrai undo, il faudrait soit :
    // 1. Garder un historique des grilles
    // 2. Ou rejouer depuis le début
    // Pour la démo, on reset et rejoue
    const newHistory = commandHistory.slice(0, -1);
    setCommandHistory(newHistory);
    onScenarioChange?.(newHistory);
    
    // Ici on devrait rejouer tous les commandes sauf la dernière
    console.log('Undo implementation needed - replaying from start would be required');
    
    // Pour l'instant, on reset et informe l'utilisateur
    alert('Pour annuler, utilisez le bouton Reset et rejouez les commandes désirées.');
  }, [commandHistory, onScenarioChange]);

  const handleCommandClick = useCallback((command: string, index: number) => {
    console.log(`Command ${index}: ${command}`);
    // Ici on pourrait afficher les détails de la commande ou permettre sa modification
  }, []);

  const handleCommandDelete = useCallback((index: number) => {
    const newHistory = commandHistory.filter((_, i) => i !== index);
    setCommandHistory(newHistory);
    onScenarioChange?.(newHistory);
    
    // Pour une vraie implementation, il faudrait rejouer depuis le début
    alert('Commande supprimée. Utilisez Reset pour appliquer les changements.');
  }, [commandHistory, onScenarioChange]);

  const showColorFilterDialog = useCallback(() => {
    const colors = prompt('Entrez les couleurs à filtrer (séparées par des virgules):');
    if (colors) {
      const colorArray = colors.split(',').map(c => parseInt(c.trim())).filter(c => !isNaN(c) && c >= 0 && c <= 9);
      if (colorArray.length > 0) {
        applyColorFilter(colorArray);
      }
    }
  }, [applyColorFilter]);

  const handleSelectionChange = useCallback((newSelection: any) => {
    // Cette fonction sera appelée par QuickOperatorPanel
    // pour mettre à jour la sélection avec les nouveaux opérateurs
    console.log('Selection changed:', newSelection);
  }, []);

  // === RENDU ===

  return (
    <div className="unified-scenario-editor">
      <div className="editor-header">
        <h2>Éditeur de Scénario Unifié</h2>
        <div className="header-controls">
          <button 
            className="control-button control-button--danger" 
            onClick={resetGrid} 
            title="Remettre à zéro"
          >
            🔄 Reset
          </button>
          <button 
            className="control-button control-button--secondary"
            onClick={undoLastCommand} 
            disabled={commandHistory.length === 0}
            title="Annuler la dernière commande"
          >
            ↶ Undo
          </button>
          <button 
            className="control-button control-button--secondary"
            onClick={clearSelection}
            title="Effacer la sélection"
          >
            ✖ Clear Selection
          </button>
          <button 
            className={`control-button ${selectionState.isInverted ? 'control-button--active' : 'control-button--secondary'}`}
            onClick={applyInverseSelection}
            title="Inverser la sélection"
          >
            🔄 INVERSE
          </button>
          <button 
            className={`control-button ${selectionState.colorFilter.length > 0 ? 'control-button--active' : 'control-button--secondary'}`}
            onClick={showColorFilterDialog}
            title="Filtrer par couleur"
          >
            🎨 COLOR
          </button>
          
          {/* Séparateur visuel */}
          <div className="control-separator"></div>
          
          {/* Nouveaux boutons pour les opérateurs */}
          <button 
            className="control-button control-button--operator control-button--xor"
            onClick={() => {
              // Exemple rapide avec sélection par défaut
              const exampleSelection = {
                rectangles: [{ startRow: 2, startCol: 2, endRow: 4, endCol: 4, isAdditional: false }],
                mode: 'replace' as const
              };
              applyXorOperator(exampleSelection);
            }}
            title="XOR: Différence symétrique entre sélections"
          >
            ⊕ XOR
          </button>
          <button 
            className="control-button control-button--operator control-button--and"
            onClick={() => {
              const exampleSelection = {
                rectangles: [{ startRow: 1, startCol: 1, endRow: 3, endCol: 3, isAdditional: false }],
                mode: 'replace' as const
              };
              applyAndOperator([exampleSelection]);
            }}
            title="AND: Intersection de sélections"
          >
            ∩ AND
          </button>
          <button 
            className="control-button control-button--operator control-button--or"
            onClick={() => {
              const exampleSelection = {
                rectangles: [{ startRow: 0, startCol: 0, endRow: 1, endCol: 1, isAdditional: false }],
                mode: 'replace' as const
              };
              applyOrOperator([exampleSelection]);
            }}
            title="OR: Union de sélections"
          >
            ∪ OR
          </button>
        </div>
      </div>

      <div className="editor-content">
        <div className="grid-panel">
          <UnifiedGridComponent
            grid={grid}
            onGridChange={handleGridChange}
          />
        </div>

        <div className="actions-panel">
          <UnifiedActionToolbar
            grid={grid}
            selection={getUnifiedSelection()}
            onGridChange={handleGridChange}
            onCommandExecuted={handleCommandExecuted}
          />
          
          {/* Panneau d'opérateurs avancé */}
          {/* <QuickOperatorPanel
            currentSelection={getUnifiedSelection()}
            onSelectionChange={handleSelectionChange}
          /> */}
        </div>
      </div>

      <div className="editor-footer">
        <ScenarioHistory
          commands={commandHistory}
          onCommandClick={handleCommandClick}
          onCommandDelete={handleCommandDelete}
        />
      </div>
    </div>
  );
};
