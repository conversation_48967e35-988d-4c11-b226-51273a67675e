/**
 * Script pour faciliter la communication bidirectionnelle entre l'iframe et l'application parent
 * 
 * Ce script est injecté dans l'iframe pour permettre la communication avec l'application parent
 * et l'exécution de commandes dans le conteneur Docker.
 */

(function() {
  // Vérifier si le script est déjà chargé
  if (window.DockerIframeBridge) {
    console.log('DockerIframeBridge déjà initialisé');
    return;
  }

  // Créer l'objet DockerIframeBridge
  window.DockerIframeBridge = {
    // Propriétés
    containerId: null,
    isConnected: false,
    messageQueue: [],
    messageCallbacks: {},
    messageId: 0,

    // Initialiser le bridge
    init: function(containerId) {
      this.containerId = containerId;
      this.isConnected = true;
      console.log('DockerIframeBridge initialisé avec le conteneur:', containerId);

      // Traiter les messages en attente
      this.processMessageQueue();

      // Envoyer un message de confirmation
      this.sendMessage({
        type: 'docker_bridge_ready',
        containerId: this.containerId
      });

      return this;
    },

    // Envoyer un message à l'application parent
    sendMessage: function(message) {
      if (window.parent && window.parent !== window) {
        try {
          window.parent.postMessage(message, '*');
          return true;
        } catch (error) {
          console.error('Erreur lors de l\'envoi du message:', error);
          return false;
        }
      }
      return false;
    },

    // Exécuter une commande dans le conteneur Docker
    executeCommand: function(command, callback) {
      if (!this.isConnected) {
        console.warn('DockerIframeBridge non connecté');
        if (callback) {
          callback({
            success: false,
            error: 'DockerIframeBridge non connecté'
          });
        }
        return false;
      }

      // Générer un ID unique pour ce message
      const messageId = 'cmd_' + (++this.messageId);

      // Enregistrer le callback
      if (callback) {
        this.messageCallbacks[messageId] = callback;
      }

      // Envoyer la commande
      this.sendMessage({
        type: 'docker_command',
        id: messageId,
        command: command
      });

      return messageId;
    },

    // Traiter les messages en attente
    processMessageQueue: function() {
      if (this.messageQueue.length > 0) {
        console.log('Traitement de', this.messageQueue.length, 'messages en attente');
        
        // Traiter tous les messages en attente
        this.messageQueue.forEach(message => {
          this.handleMessage(message);
        });
        
        // Vider la file d'attente
        this.messageQueue = [];
      }
    },

    // Gérer un message reçu
    handleMessage: function(event) {
      try {
        const message = event.data;
        
        // Ignorer les messages qui ne sont pas des objets
        if (!message || typeof message !== 'object') {
          return;
        }
        
        // Traiter le message selon son type
        if (message.type === 'docker_init') {
          // Initialiser le bridge
          this.init(message.containerId);
        } else if (message.type === 'docker_command_result') {
          // Résultat d'une commande
          const callback = this.messageCallbacks[message.id];
          if (callback) {
            callback(message.result);
            delete this.messageCallbacks[message.id];
          }
        } else if (message.type === 'docker_command_error') {
          // Erreur lors de l'exécution d'une commande
          const callback = this.messageCallbacks[message.id];
          if (callback) {
            callback({
              success: false,
              error: message.error
            });
            delete this.messageCallbacks[message.id];
          }
        }
      } catch (error) {
        console.error('Erreur lors du traitement du message:', error);
      }
    }
  };

  // Écouter les messages de l'application parent
  window.addEventListener('message', function(event) {
    // Vérifier l'origine du message (pour la sécurité)
    // Dans un environnement de production, il faudrait vérifier l'origine
    
    // Si le bridge n'est pas encore initialisé, mettre le message en file d'attente
    if (!window.DockerIframeBridge.isConnected && event.data && event.data.type !== 'docker_init') {
      window.DockerIframeBridge.messageQueue.push(event);
      return;
    }
    
    // Traiter le message
    window.DockerIframeBridge.handleMessage(event);
  });

  // Ajouter des fonctions utilitaires au prototype de Jupyter Notebook
  if (window.Jupyter && window.Jupyter.notebook) {
    // Exécuter une commande shell et afficher le résultat dans une cellule
    window.Jupyter.notebook.execute_shell_command = function(command) {
      const cell = this.insert_cell_below('code');
      cell.set_text('# Exécution de la commande: ' + command);
      this.select_cell(this.find_cell_index(cell));
      this.execute_cell();
      
      window.DockerIframeBridge.executeCommand(command, function(result) {
        const outputCell = window.Jupyter.notebook.insert_cell_below('code');
        if (result.success) {
          outputCell.set_text('"""\n' + result.output + '\n"""');
        } else {
          outputCell.set_text('"""\nErreur: ' + result.error + '\n"""');
        }
        window.Jupyter.notebook.select_cell(window.Jupyter.notebook.find_cell_index(outputCell));
        window.Jupyter.notebook.execute_cell();
      });
    };
    
    console.log('Fonctions utilitaires ajoutées à Jupyter Notebook');
  }

  console.log('DockerIframeBridge chargé et prêt à être initialisé');
})();
