#!/usr/bin/env python3
"""
Script pour supprimer les fonctions dupliquées dans useAutomation.ts
"""

import re

# Lire le fichier
with open('frontend/src/components/resolution/hooks/useAutomation.ts', 'r', encoding='utf-8') as f:
    lines = f.readlines()

# Trouver les lignes des fonctions dupliquées
processCoordinateBlocks_lines = []
executeUnifiedEnd_lines = []

for i, line in enumerate(lines):
    if 'function processCoordinateBlocks(' in line:
        processCoordinateBlocks_lines.append(i)
    elif 'async function executeUnifiedEnd(' in line:
        executeUnifiedEnd_lines.append(i)

print(f"Trouvé processCoordinateBlocks aux lignes: {processCoordinateBlocks_lines}")
print(f"Trouvé executeUnifiedEnd aux lignes: {executeUnifiedEnd_lines}")

# Supprimer la première occurrence de chaque fonction dupliquée
lines_to_remove = set()

# Supprimer la première processCoordinateBlocks (version synchrone)
if len(processCoordinateBlocks_lines) >= 2:
    start_line = processCoordinateBlocks_lines[0] - 5  # Inclure le commentaire
    # Trouver la fin de la fonction (chercher la prochaine fonction)
    end_line = start_line
    brace_count = 0
    found_opening = False
    
    for i in range(start_line, len(lines)):
        if '{' in lines[i]:
            brace_count += lines[i].count('{')
            found_opening = True
        if '}' in lines[i]:
            brace_count -= lines[i].count('}')
        
        if found_opening and brace_count == 0:
            end_line = i + 1
            break
    
    print(f"Suppression de processCoordinateBlocks lignes {start_line} à {end_line}")
    for i in range(start_line, end_line):
        lines_to_remove.add(i)

# Supprimer la première executeUnifiedEnd
if len(executeUnifiedEnd_lines) >= 2:
    start_line = executeUnifiedEnd_lines[0]
    # Trouver la fin de la fonction
    end_line = start_line
    brace_count = 0
    found_opening = False
    
    for i in range(start_line, len(lines)):
        if '{' in lines[i]:
            brace_count += lines[i].count('{')
            found_opening = True
        if '}' in lines[i]:
            brace_count -= lines[i].count('}')
        
        if found_opening and brace_count == 0:
            end_line = i + 1
            break
    
    print(f"Suppression de executeUnifiedEnd lignes {start_line} à {end_line}")
    for i in range(start_line, end_line):
        lines_to_remove.add(i)

# Créer le nouveau contenu sans les lignes supprimées
new_lines = []
for i, line in enumerate(lines):
    if i not in lines_to_remove:
        new_lines.append(line)
    elif i == min(lines_to_remove):
        new_lines.append('// SUPPRIMÉ - Fonctions dupliquées\n')

# Écrire le fichier nettoyé
with open('frontend/src/components/resolution/hooks/useAutomation.ts', 'w', encoding='utf-8') as f:
    f.writelines(new_lines)

print("✅ Suppression des doublons terminée!")