#!/usr/bin/env python3
"""
Script de validation et d'optimisation des scénarios via le backend.
"""

import sys
import os
import django
from pathlib import Path
import json
from datetime import datetime
import argparse
from collections import defaultdict, Counter

# Configuration Django
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import des services
from backend.myapp.services.scenario_backend_validation_service import ScenarioBackendValidationService
from backend.myapp.services.scenario_optimization_service import Sc<PERSON>rioOptimizationService
from backend.myapp.services.scenario_service import ScenarioService

def get_all_training_scenarios(max_scenarios=None, specific_task=None, subset='training'):
    """Récupère tous les fichiers .agi du répertoire spécifié."""
    training_dir = Path(f'arcdata/{subset}')
    if not training_dir.exists():
        print(f"❌ Répertoire {training_dir} non trouvé")
        return []
    
    pattern = f"{specific_task}_*.agi" if specific_task else "*.agi"
    agi_files = list(training_dir.glob(pattern))
    
    if max_scenarios:
        agi_files = agi_files[:max_scenarios]
    
    scenarios = []
    for agi_file in agi_files:
        filename = agi_file.stem
        parts = filename.split('_')
        if len(parts) >= 3 and parts[1].startswith('TEST'):
            try:
                scenarios.append({
                    'file_path': str(agi_file),
                    'task_id': parts[0],
                    'test_index': int(parts[1][4:]),
                    'filename': filename
                })
            except (ValueError, IndexError):
                print(f"⚠️ Impossible d'extraire les informations de {filename}")
    
    return scenarios

def process_scenario(scenario_info, validation_service, optimization_service, scenario_service, args):
    """Traite un seul scénario pour validation et/ou optimisation."""
    try:
        with open(scenario_info['file_path'], 'r', encoding='utf-8') as f:
            original_content = f.read().strip()
        
        original_commands = original_content.splitlines()
        content_to_validate = original_content
        
        optimization_info = {'status': 'not_attempted'}
        was_optimized = False
        optimization_result = optimization_service.optimize_command_list(original_commands)
        optimized_commands = optimization_result['optimized_commands']

        if args.optimize_and_save:
            if optimized_commands != original_commands:
                was_optimized = True
                content_to_validate = "\n".join(optimized_commands)

        validation_result = validation_service.validate_scenario(
            subset=args.subnet,
            task_id=scenario_info['task_id'],
            content=content_to_validate,
            test_index=scenario_info['test_index']
        )
        is_valid = validation_result.get('is_valid_by_backend', False)
        
        # Logique d'affichage
        action_taken = was_optimized or not is_valid
        if action_taken or args.verbose:
             print(f"\n[{args.i}/{args.total_scenarios}] 🔍 {scenario_info['filename']}")
        
        if was_optimized and action_taken:
             print(f"   ✨ Optimisation appliquée: {len(original_commands)} -> {len(optimized_commands)} lignes. Règles: {optimization_result['rules_applied']}")

        if is_valid:
            optimization_info['status'] = 'no_change'
            if args.optimize_and_save and was_optimized:
                command_stats = validation_result.get('command_stats', {})
                expanded_count = command_stats.get('expanded_count', float('inf'))
                
                save_result = scenario_service.save_optimal_scenario(
                    subset=args.subnet,
                    task_id=scenario_info['task_id'],
                    scenario_content=content_to_validate,
                    test_index=scenario_info['test_index'],
                    new_command_count=expanded_count
                )
                optimization_info = save_result
                print(f"   💾 Sauvegarde: {save_result.get('status')} - {save_result.get('message')}")
            elif args.verbose:
                print(f"   ✅ VALIDE")
        else:
            error_msg = validation_result.get('error_message', 'Erreur inconnue')
            print(f"   ❌ ÉCHEC - {error_msg}")

        result_entry = {
            'filename': scenario_info['filename'],
            'task_id': scenario_info['task_id'],
            'status': "SUCCESS" if is_valid else "FAILED",
            'optimization_info': optimization_info
        }
        return True, result_entry

    except Exception as e:
        print(f"Erreur critique lors du traitement {scenario_info.get('filename', 'N/A')}: {e}")
        return False, {'error_message': str(e)}

def main():
    parser = argparse.ArgumentParser(description="Validation et optimisation des scénarios")
    parser.add_argument('--task', help='ID de la tâche à tester')
    parser.add_argument('--max', type=int, help='Nombre maximum de scénarios')
    parser.add_argument('--subnet', default='training', help='Sous-répertoire (training/evaluation)')
    parser.add_argument('--optimize-and-save', action='store_true', help='Active l\'optimisation et la sauvegarde')
    parser.add_argument('--verbose', action='store_true', help='Affichage détaillé')
    
    args = parser.parse_args()
    
    mode = "OPTIMISATION & SAUVEGARDE" if args.optimize_and_save else "VALIDATION SIMPLE"
    print(f"🚀 Démarrage du script en mode: {mode}")
    print(f"📁 Répertoire: arcdata/{args.subnet}")
    
    scenarios = get_all_training_scenarios(args.max, args.task, args.subnet)
    
    if not scenarios:
        print("❌ Aucun scénario trouvé")
        return
    
    print(f"📊 {len(scenarios)} scénario(s) à traiter")
    
    validation_service = ScenarioBackendValidationService(no_grids=not args.verbose)
    optimization_service = ScenarioOptimizationService()
    scenario_service = ScenarioService()
    
    results = []
    optimization_stats = Counter()
    
    print("\n" + "="*60 + "\nDÉBUT DU TRAITEMENT\n" + "="*60)
    
    for i, scenario_info in enumerate(scenarios, 1):
        args.i = i
        args.total_scenarios = len(scenarios)
        success, result_entry = process_scenario(scenario_info, validation_service, optimization_service, scenario_service, args)
        
        results.append(result_entry)
        if success and result_entry.get('status') == "SUCCESS":
            if args.optimize_and_save:
                opt_status = result_entry.get('optimization_info', {}).get('status')
                if opt_status:
                    optimization_stats[opt_status] += 1

    total_scenarios = len(results)
    successful_validations = sum(1 for r in results if r.get('status') == "SUCCESS")
    failed_validations = total_scenarios - successful_validations
    success_rate = (successful_validations / total_scenarios * 100) if total_scenarios > 0 else 0
    
    print("\n" + "="*60 + "\nBILAN FINAL\n" + "="*60)
    print(f"📊 Total des scénarios traités: {total_scenarios}")
    print(f"✅ Validations réussies: {successful_validations}")
    print(f"❌ Validations échouées: {failed_validations}")
    print(f"📈 Taux de succès: {success_rate:.1f}%")
    
    if args.optimize_and_save:
        print("\n" + "-"*20 + " Bilan Optimisation " + "-"*20)
        print(f"💾 Nouveaux scénarios optimaux sauvegardés: {optimization_stats.get('saved_new_optimal', 0)}")
        print(f"👍 Scénario optimal déjà existant: {optimization_stats.get('not_saved_optimal_exists', 0)}")
        print(f"👌 Aucune optimisation trouvée/appliquée: {optimization_stats.get('no_change', 0)}")
        print(f"❓ Scénarios avec erreur de sauvegarde: {optimization_stats.get('error', 0)}")
    
    print(f"\n⏱️ Traitement terminé")

if __name__ == "__main__":
    main()