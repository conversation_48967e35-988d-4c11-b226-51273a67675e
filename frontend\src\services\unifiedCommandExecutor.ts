import { 
  ParsedCommand, 
  ExecutionResult, 
  UnifiedSelection
} from '../types/unifiedCommands';
import { Grid } from '../lib/grid';
import { selectCellsByColorPalette } from '../lib/colorDetectionUtils';

export class UnifiedCommandExecutor {
  
  async executeCommand(
    command: ParsedCommand, 
    grid: Grid,
    selectionStore?: any
  ): Promise<ExecutionResult> {
    
    try {
      console.log(`[UnifiedExecutor] Executing: ${command.rawCommand}`);
      
      // 1. Appliquer la sélection sur le store de sélection
      if (selectionStore) {
        await this.applySelectionsToStore(command.selections, selectionStore, grid);
      }
      
      // 2. Traitement spécial pour INVERSE et COLOR
      if (command.selections.isInverted) {
        await this.applyInversion(command.selections, grid, selectionStore);
      }
      
      if (command.selections.colorFilter && command.selections.colorFilter.length > 0) {
        await this.applyColorFilter(command.parameters, selectionStore, grid);
      }
      
      // 3. Exécuter l'action principale
      return await this.executeMainAction(command, grid, selectionStore);
      
    } catch (error) {
      console.error(`[UnifiedExecutor] Error executing command:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
  private async applySelectionsToStore(
    selections: UnifiedSelection, 
    selectionStore: any, 
    _grid: Grid
  ): Promise<void> {
    
    // Vider la sélection courante si mode replace
    if (selections.mode === 'replace') {
      selectionStore.clearSelection();
      selectionStore.clearRectangles();
    }
    
    // Appliquer chaque rectangle de sélection
    for (const rect of selections.rectangles) {
      const mode = rect.isAdditional ? 'add' : selections.mode;
      
      if (rect.startRow === rect.endRow && rect.startCol === rect.endCol) {
        // Sélection de cellule unique
        selectionStore.selectCell(rect.startRow, rect.startCol, mode);
      } else {
        // Sélection de rectangle
        selectionStore.selectRect(
          rect.startRow, rect.startCol, 
          rect.endRow, rect.endCol, 
          mode
        );
      }
    }
  }
  private async applyInversion(
    _selections: UnifiedSelection, 
    grid: Grid, 
    selectionStore: any
  ): Promise<void> {
    
    if (!selectionStore) return;
    
    // Récupérer toutes les cellules de la grille
    const allCells = new Set<string>();
    for (let row = 0; row < grid.height; row++) {
      for (let col = 0; col < grid.width; col++) {
        allCells.add(`${row},${col}`);
      }
    }
    
    // Récupérer les cellules actuellement sélectionnées
    const currentSelection = selectionStore.selectedCells;
    
    // Calculer l'inversion
    const invertedCells = new Set<string>();
    allCells.forEach(cell => {
      if (!currentSelection.has(cell)) {
        invertedCells.add(cell);
      }
    });
    
    // Remplacer la sélection par l'inversion
    selectionStore.clearSelection();
    invertedCells.forEach(cellKey => {
      const [row, col] = cellKey.split(',').map(Number);
      selectionStore.selectCell(row, col, 'add');
    });
    
    console.log(`[UnifiedExecutor] Applied INVERSE: ${invertedCells.size} cells selected`);
  }  
  
  private async applyColorFilter(
    parameters: any, 
    selectionStore: any, 
    grid: Grid
  ): Promise<void> {
    
    if (!selectionStore || !parameters.colors) return;
    
    // Récupérer les cellules actuellement sélectionnées avec type assertion
    const currentSelection: Set<string> = selectionStore.selectedCells;
    
    // Appliquer le filtre de couleur avec type assertion
    const colorPalette: Set<number> = new Set(parameters.colors as number[]);
    const filteredCells = selectCellsByColorPalette(currentSelection, colorPalette, grid);
    
    // Remplacer la sélection par les cellules filtrées
    selectionStore.clearSelection();
    filteredCells.forEach(cellKey => {
      const [row, col] = cellKey.split(',').map(Number);
      selectionStore.selectCell(row, col, 'add');
    });
    
    console.log(`[UnifiedExecutor] Applied COLOR filter: ${filteredCells.size} cells selected`);
  }

  private async executeMainAction(
    command: ParsedCommand, 
    grid: Grid,
    selectionStore?: any
  ): Promise<ExecutionResult> {
    
    // Récupérer les cellules sélectionnées
    const selectedCells = selectionStore ? selectionStore.selectedCells : this.getSelectedCellsFromCommand(command.selections, grid);
    
    switch (command.action) {
      case 'CLEAR':
        return this.executeClear(selectedCells, grid);
        
      case 'FILL':
        return this.executeFill(command.parameters.color!, selectedCells, grid);
        
      case 'SURROUND':
        return this.executeSurround(command.parameters.color!, selectedCells, grid);
        
      case 'REPLACE':
        return this.executeReplace(
          command.parameters.sourceColors!, 
          command.parameters.targetColor!,
          selectedCells, 
          grid
        );
        
      case 'INSERT':
        return this.executeInsert(command.parameters, command.selections, grid);
        
      case 'DELETE':
        return this.executeDelete(command.parameters, command.selections, grid);
        
      case 'FLIP':
        return this.executeFlip(command.parameters.direction as 'H' | 'V', selectedCells, grid);
        
      case 'ROTATE':
        return this.executeRotate(command.parameters.direction as 'L' | 'R', selectedCells, grid);

      case 'INPUT':
        return this.executeInput(grid);

      default:
        throw new Error(`Action non supportée: ${command.action}`);
    }
  }

  private getSelectedCellsFromCommand(selections: UnifiedSelection, grid: Grid): Set<string> {
    const cells = new Set<string>();
    
    for (const rect of selections.rectangles) {
      for (let row = rect.startRow; row <= rect.endRow; row++) {
        for (let col = rect.startCol; col <= rect.endCol; col++) {
          if (row >= 0 && row < grid.height && col >= 0 && col < grid.width) {
            cells.add(`${row},${col}`);
          }
        }
      }
    }
    
    return cells;
  }

  // === ACTIONS SIMPLES ===
  private executeClear(selectedCells: Set<string>, grid: Grid): ExecutionResult {
    const newGrid = grid.clone();
    const modifiedCells: Array<{row: number, col: number, oldValue: number, newValue: number}> = [];
    
    selectedCells.forEach(cellKey => {
      const [row, col] = cellKey.split(',').map(Number);
      const oldValue = newGrid.getCell(row, col);
      if (oldValue !== undefined) {
        newGrid.setCell(row, col, 0);
        modifiedCells.push({ row, col, oldValue, newValue: 0 });
      }
    });
    
    return {
      success: true,
      newGrid,
      modifiedCells
    };
  }
  private executeFill(color: number, selectedCells: Set<string>, grid: Grid): ExecutionResult {
    const newGrid = grid.clone();
    const modifiedCells: Array<{row: number, col: number, oldValue: number, newValue: number}> = [];
    
    selectedCells.forEach(cellKey => {
      const [row, col] = cellKey.split(',').map(Number);
      const oldValue = newGrid.getCell(row, col);
      if (oldValue !== undefined) {
        newGrid.setCell(row, col, color);
        modifiedCells.push({ row, col, oldValue, newValue: color });
      }
    });
    
    return {
      success: true,
      newGrid,
      modifiedCells
    };
  }
  private executeSurround(color: number, selectedCells: Set<string>, grid: Grid): ExecutionResult {
    const newGrid = grid.clone();
    const modifiedCells: Array<{row: number, col: number, oldValue: number, newValue: number}> = [];
    
    // Calculer les limites de la sélection
    let minRow = grid.height, maxRow = -1;
    let minCol = grid.width, maxCol = -1;
    
    selectedCells.forEach(cellKey => {
      const [row, col] = cellKey.split(',').map(Number);
      minRow = Math.min(minRow, row);
      maxRow = Math.max(maxRow, row);
      minCol = Math.min(minCol, col);
      maxCol = Math.max(maxCol, col);
    });
    
    // Dessiner le contour
    for (let row = minRow - 1; row <= maxRow + 1; row++) {
      for (let col = minCol - 1; col <= maxCol + 1; col++) {
        if (row >= 0 && row < grid.height && col >= 0 && col < grid.width) {
          // Cellule de bordure
          if (row === minRow - 1 || row === maxRow + 1 || 
              col === minCol - 1 || col === maxCol + 1) {
            const oldValue = newGrid.getCell(row, col);
            if (oldValue !== undefined) {
              newGrid.setCell(row, col, color);
              modifiedCells.push({ row, col, oldValue, newValue: color });
            }
          }
        }
      }
    }
    
    return {
      success: true,
      newGrid,
      modifiedCells
    };
  }
  private executeReplace(
    sourceColors: number[], 
    targetColor: number, 
    selectedCells: Set<string>, 
    grid: Grid
  ): ExecutionResult {
    const newGrid = grid.clone();
    const modifiedCells: Array<{row: number, col: number, oldValue: number, newValue: number}> = [];
    const sourceSet = new Set(sourceColors);
    
    selectedCells.forEach(cellKey => {
      const [row, col] = cellKey.split(',').map(Number);
      const currentValue = newGrid.getCell(row, col);
      
      if (currentValue !== undefined && sourceSet.has(currentValue)) {
        newGrid.setCell(row, col, targetColor);
        modifiedCells.push({ row, col, oldValue: currentValue, newValue: targetColor });
      }
    });
    
    return {
      success: true,
      newGrid,
      modifiedCells
    };
  }

  // === TRANSFORMATIONS ===
  private executeFlip(direction: 'H' | 'V', selectedCells: Set<string>, grid: Grid): ExecutionResult {
    const newGrid = grid.clone();
    const modifiedCells: Array<{row: number, col: number, oldValue: number, newValue: number}> = [];
    
    // Calculer les limites de la sélection
    let minRow = grid.height, maxRow = -1;
    let minCol = grid.width, maxCol = -1;
    
    selectedCells.forEach(cellKey => {
      const [row, col] = cellKey.split(',').map(Number);
      minRow = Math.min(minRow, row);
      maxRow = Math.max(maxRow, row);
      minCol = Math.min(minCol, col);
      maxCol = Math.max(maxCol, col);
    });
    
    // Extraire les valeurs de la zone sélectionnée
    const values = new Map<string, number>();
    selectedCells.forEach(cellKey => {
      const [row, col] = cellKey.split(',').map(Number);
      const value = grid.getCell(row, col);
      if (value !== undefined) {
        values.set(cellKey, value);
      }
    });
    
    // Appliquer le retournement
    selectedCells.forEach(cellKey => {
      const [row, col] = cellKey.split(',').map(Number);
      let newRow, newCol;
      
      if (direction === 'H') {
        // Retournement horizontal
        newRow = row;
        newCol = maxCol - (col - minCol);
      } else {
        // Retournement vertical
        newRow = maxRow - (row - minRow);
        newCol = col;
      }
      
      const sourceKey = `${newRow},${newCol}`;
      const newValue = values.get(sourceKey) || 0;
      const oldValue = newGrid.getCell(row, col);
      
      if (oldValue !== undefined) {
        newGrid.setCell(row, col, newValue);
        modifiedCells.push({ row, col, oldValue, newValue });
      }
    });
    
    return {
      success: true,
      newGrid,
      modifiedCells
    };
  }

  private executeRotate(direction: 'L' | 'R', selectedCells: Set<string>, grid: Grid): ExecutionResult {
    const newGrid = grid.clone();
    const modifiedCells = [];
    
    // Calculer les limites et vérifier que c'est un carré
    let minRow = grid.height, maxRow = -1;
    let minCol = grid.width, maxCol = -1;
    
    selectedCells.forEach(cellKey => {
      const [row, col] = cellKey.split(',').map(Number);
      minRow = Math.min(minRow, row);
      maxRow = Math.max(maxRow, row);
      minCol = Math.min(minCol, col);
      maxCol = Math.max(maxCol, col);
    });
    
    const height = maxRow - minRow + 1;
    const width = maxCol - minCol + 1;
    
    // Pour la rotation, on travaille avec toute la zone rectangulaire
    const values: number[][] = [];
    for (let r = 0; r < height; r++) {
      values[r] = [];
      for (let c = 0; c < width; c++) {
        const gridRow = minRow + r;
        const gridCol = minCol + c;
        values[r][c] = grid.getCell(gridRow, gridCol) || 0;
      }
    }
    
    // Appliquer la rotation
    const rotated: number[][] = [];
    
    if (direction === 'R') {
      // Rotation droite (90° horaire)
      for (let c = 0; c < width; c++) {
        rotated[c] = [];
        for (let r = 0; r < height; r++) {
          rotated[c][height - 1 - r] = values[r][c];
        }
      }
    } else {
      // Rotation gauche (90° antihoraire)
      for (let c = 0; c < width; c++) {
        rotated[width - 1 - c] = [];
        for (let r = 0; r < height; r++) {
          rotated[width - 1 - c][r] = values[r][c];
        }
      }
    }
    
    // Appliquer les valeurs rotées
    const rotatedHeight = rotated.length;
    const rotatedWidth = rotated[0]?.length || 0;
    
    for (let r = 0; r < rotatedHeight && r < height; r++) {
      for (let c = 0; c < rotatedWidth && c < width; c++) {
        const gridRow = minRow + r;
        const gridCol = minCol + c;
        
        if (gridRow < grid.height && gridCol < grid.width) {
          const oldValue = newGrid.getCell(gridRow, gridCol);
          const newValue = rotated[r][c];
          if (oldValue !== undefined) {
            newGrid.setCell(gridRow, gridCol, newValue);
            modifiedCells.push({ row: gridRow, col: gridCol, oldValue, newValue });
          }
        }
      }
    }
    
    return {
      success: true,
      newGrid,
      modifiedCells
    };
  }

  // === MODIFICATIONS DE GRILLE ===

  private executeInsert(
    parameters: any, 
    selections: UnifiedSelection, 
    grid: Grid
  ): ExecutionResult {
    const { type, count, direction } = parameters;
      // Calculer les positions d'insertion
    const insertPositions = this.calculateInsertPositions(selections, direction, type);
    
    if (type === 'ROWS') {
      return this.insertRows(insertPositions, count, grid);
    } else {
      return this.insertColumns(insertPositions, count, grid);
    }
  }
  private calculateInsertPositions(
    selections: UnifiedSelection, 
    direction: string,
    type: string
  ): number[] {
    const positions: number[] = [];
    
    for (const rect of selections.rectangles) {
      if (type === 'ROWS') {
        if (direction === 'before') {
          positions.push(rect.startRow);
        } else if (direction === 'after') {
          positions.push(rect.endRow + 1);
        }
      } else { // COLUMNS
        if (direction === 'left') {
          positions.push(rect.startCol);
        } else if (direction === 'right') {
          positions.push(rect.endCol + 1);
        }
      }
    }
    
    // Dédupliquer et trier
    const uniquePositions = [...new Set(positions)];
    return uniquePositions.sort((a, b) => direction === 'before' || direction === 'left' ? a - b : b - a);
  }

  private insertRows(positions: number[], count: number, grid: Grid): ExecutionResult {
    const newHeight = grid.height + (positions.length * count);
    const newGrid = new Grid(newHeight, grid.width);
    
    let sourceRow = 0;
    let targetRow = 0;
    
    // Copier en insérant les nouvelles lignes
    while (sourceRow < grid.height) {
      // Vérifier si on doit insérer des lignes à cette position
      if (positions.includes(sourceRow)) {
        // Insérer 'count' lignes vides
        for (let i = 0; i < count; i++) {
          for (let col = 0; col < grid.width; col++) {
            newGrid.setCell(targetRow, col, 0);
          }
          targetRow++;
        }
      }
      
      // Copier la ligne source
      for (let col = 0; col < grid.width; col++) {
        const value = grid.getCell(sourceRow, col) || 0;
        newGrid.setCell(targetRow, col, value);
      }
      sourceRow++;
      targetRow++;
    }
    
    return {
      success: true,
      newGrid,
      modifiedCells: [] // Trop complexe à calculer pour les insertions
    };
  }

  private insertColumns(positions: number[], count: number, grid: Grid): ExecutionResult {
    const newWidth = grid.width + (positions.length * count);
    const newGrid = new Grid(grid.height, newWidth);
    
    for (let row = 0; row < grid.height; row++) {
      let sourceCol = 0;
      let targetCol = 0;
      
      while (sourceCol < grid.width) {
        // Vérifier si on doit insérer des colonnes à cette position
        if (positions.includes(sourceCol)) {
          // Insérer 'count' colonnes vides
          for (let i = 0; i < count; i++) {
            newGrid.setCell(row, targetCol, 0);
            targetCol++;
          }
        }
        
        // Copier la colonne source
        const value = grid.getCell(row, sourceCol) || 0;
        newGrid.setCell(row, targetCol, value);
        sourceCol++;
        targetCol++;
      }
    }
    
    return {
      success: true,
      newGrid,
      modifiedCells: []
    };
  }

  private executeDelete(
    parameters: any, 
    selections: UnifiedSelection, 
    grid: Grid
  ): ExecutionResult {
    const { type } = parameters;
    
    // Calculer les positions à supprimer
    const deletePositions = this.calculateDeletePositions(selections, type);
    
    if (type === 'ROWS') {
      return this.deleteRows(deletePositions, grid);
    } else {
      return this.deleteColumns(deletePositions, grid);
    }
  }

  private calculateDeletePositions(selections: UnifiedSelection, type: string): number[] {
    const positions = new Set<number>();
    
    for (const rect of selections.rectangles) {
      if (type === 'ROWS') {
        for (let row = rect.startRow; row <= rect.endRow; row++) {
          positions.add(row);
        }
      } else { // COLUMNS
        for (let col = rect.startCol; col <= rect.endCol; col++) {
          positions.add(col);
        }
      }
    }
    
    return Array.from(positions).sort((a, b) => b - a); // Tri décroissant pour supprimer de la fin
  }

  private deleteRows(positions: number[], grid: Grid): ExecutionResult {
    const newHeight = grid.height - positions.length;
    if (newHeight <= 0) {
      throw new Error('Impossible de supprimer toutes les lignes');
    }
    
    const newGrid = new Grid(newHeight, grid.width);
    const positionsSet = new Set(positions);
    let targetRow = 0;
    
    for (let sourceRow = 0; sourceRow < grid.height; sourceRow++) {
      if (!positionsSet.has(sourceRow)) {
        for (let col = 0; col < grid.width; col++) {
          const value = grid.getCell(sourceRow, col) || 0;
          newGrid.setCell(targetRow, col, value);
        }
        targetRow++;
      }
    }
    
    return {
      success: true,
      newGrid,
      modifiedCells: []
    };
  }

  private deleteColumns(positions: number[], grid: Grid): ExecutionResult {
    const newWidth = grid.width - positions.length;
    if (newWidth <= 0) {
      throw new Error('Impossible de supprimer toutes les colonnes');
    }
    
    const newGrid = new Grid(grid.height, newWidth);
    const positionsSet = new Set(positions);
    
    for (let row = 0; row < grid.height; row++) {
      let targetCol = 0;
      for (let sourceCol = 0; sourceCol < grid.width; sourceCol++) {
        if (!positionsSet.has(sourceCol)) {
          const value = grid.getCell(row, sourceCol) || 0;
          newGrid.setCell(row, targetCol, value);
          targetCol++;
        }
      }
    }
    
    return {
      success: true,
      newGrid,
      modifiedCells: []
    };
  }

  private executeInput(grid: Grid): ExecutionResult {
    // Pour la commande INPUT, nous devons accéder à la grille d'input du test actuel
    // Cependant, l'exécuteur de commandes n'a pas accès direct aux contextes React
    // Nous devons donc passer cette information via les paramètres ou un autre mécanisme

    // Pour l'instant, nous retournons une erreur explicite qui sera gérée au niveau supérieur
    throw new Error('INPUT command requires access to test input grid - must be handled at component level');
  }
}

// Export de l'instance singleton
export const unifiedCommandExecutor = new UnifiedCommandExecutor();
