#!/usr/bin/env python3
"""
Debug du parsing des coordonnées rectangulaires dans PASTE
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from command_system.unified_command import UnifiedCommand
import numpy as np

def test_paste_rectangle_parsing():
    """Test du parsing de PASTE [0,1 1,2] comme zone rectangulaire"""
    
    print("🔍 Test du parsing de PASTE [0,1 1,2] comme zone rectangulaire...")
    
    command_str = "PASTE [0,1 1,2]"
    cmd = UnifiedCommand.parse(command_str)
    
    print(f"Commande parsée: {cmd}")
    print(f"Action: {cmd.action}")
    print(f"Parameters: {cmd.parameters}")
    print(f"Coordinates: {cmd.coordinates}")
    print(f"Raw command: {getattr(cmd, 'raw_command', 'N/A')}")
    
    # D'après la doc, [0,1 1,2] devrait être interprété comme :
    # Zone rectangulaire de (0,1) à (1,2)
    print(f"\n📋 Interprétation attendue selon la doc:")
    print(f"Zone rectangulaire: de (0,1) à (1,2)")
    print(f"Cela couvre les positions: (0,1), (0,2), (1,1), (1,2)")
    
    # Vérifier si le parsing est correct
    if len(cmd.coordinates) == 4:
        x1, y1, x2, y2 = cmd.coordinates
        print(f"\n✅ Parsing correct: rectangle de ({x1},{y1}) à ({x2},{y2})")
        
        # Calculer toutes les positions dans le rectangle
        positions = []
        for x in range(x1, x2 + 1):
            for y in range(y1, y2 + 1):
                positions.append((x, y))
        
        print(f"Positions couvertes: {positions}")
        return True, (x1, y1, x2, y2)
    else:
        print(f"\n❌ Parsing incorrect: {len(cmd.coordinates)} coordonnées au lieu de 4")
        return False, None

def test_current_paste_implementation():
    """Test de l'implémentation actuelle de PASTE avec zone rectangulaire"""
    
    print("\n" + "="*60)
    print("🧪 Test de l'implémentation actuelle de PASTE...")
    
    from command_system.command_executor import CommandExecutor
    
    executor = CommandExecutor()
    
    # Créer une grille de test
    executor.grid = np.zeros((9, 9), dtype=int)
    executor.height = 9
    executor.width = 9
    
    # Créer un clipboard de test (résultat de MULTIPLY 4 false)
    executor.clipboard = np.array([
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0]
    ])
    executor.clipboard_mask = np.ones((8, 8), dtype=bool)
    
    print("📋 Clipboard (8x8):")
    print(executor.clipboard)
    
    # Test PASTE [0,1 1,2] - implémentation actuelle
    print("\n🔄 Test PASTE [0,1 1,2] - implémentation actuelle...")
    
    paste_cmd = UnifiedCommand.parse("PASTE [0,1 1,2]")
    print(f"Coordonnées parsées: {paste_cmd.coordinates}")
    
    success = executor._cmd_paste(paste_cmd)
    print(f"Succès: {success}")
    
    if executor.error:
        print(f"Erreur: {executor.error}")
    
    print("\n📋 Grille après PASTE (implémentation actuelle):")
    print(executor.grid)
    
    return executor.grid

def test_correct_paste_rectangle():
    """Test de l'implémentation correcte de PASTE avec zone rectangulaire"""
    
    print("\n" + "="*60)
    print("🔧 Test de l'implémentation correcte de PASTE...")
    
    from command_system.command_executor import CommandExecutor
    
    executor = CommandExecutor()
    
    # Créer une grille de test
    executor.grid = np.zeros((9, 9), dtype=int)
    executor.height = 9
    executor.width = 9
    
    # Créer un clipboard de test
    executor.clipboard = np.array([
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0]
    ])
    executor.clipboard_mask = np.ones((8, 8), dtype=bool)
    
    # Implémentation correcte : PASTE [0,1 1,2] = coller à la position (0,1)
    # D'après la doc et l'analyse, cela devrait coller le clipboard à (0,1)
    
    print("🔧 Implémentation correcte: coller à position (0,1)...")
    
    x_dest, y_dest = 0, 1  # Position de collage
    clip_height, clip_width = executor.clipboard.shape
    
    for r in range(clip_height):
        for c in range(clip_width):
            if executor.clipboard_mask[r, c]:
                grid_r, grid_c = x_dest + r, y_dest + c
                if 0 <= grid_r < 9 and 0 <= grid_c < 9:
                    executor.grid[grid_r, grid_c] = executor.clipboard[r, c]
    
    print("\n📋 Grille après PASTE correct:")
    print(executor.grid)
    
    # Comparer avec l'attendu
    expected = np.array([
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    
    matches = np.sum(executor.grid == expected)
    total = executor.grid.size
    percentage = (matches / total) * 100
    
    print(f"\n📊 Comparaison avec l'attendu:")
    print(f"Cellules correspondantes: {matches}/{total} ({percentage:.1f}%)")
    
    if matches == total:
        print("✅ SUCCÈS ! L'implémentation correcte fonctionne.")
        return True
    else:
        print("\n❌ Différences détectées:")
        diff_positions = np.where(executor.grid != expected)
        for i in range(min(10, len(diff_positions[0]))):
            row, col = diff_positions[0][i], diff_positions[1][i]
            print(f"   Position [{row},{col}]: généré={executor.grid[row,col]}, attendu={expected[row,col]}")
        return False

if __name__ == "__main__":
    # Test du parsing
    success, rect = test_paste_rectangle_parsing()
    
    # Test de l'implémentation actuelle
    current_grid = test_current_paste_implementation()
    
    # Test de l'implémentation correcte
    correct_success = test_correct_paste_rectangle()