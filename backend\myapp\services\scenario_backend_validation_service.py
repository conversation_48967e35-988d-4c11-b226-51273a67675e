"""
Service de validation backend des scénarios.
Ce service implémente la logique de validation côté serveur pour les scénarios de résolution.
"""
import json
import logging # Ajout du logger
import re
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from django.conf import settings
from command_system.command_executor import CommandExecutor
from ai.command_decompressor import CommandDecompressor
from utils.error_message_formatter import ValidationErrorContext, ErrorMessageFormatter

logger = logging.getLogger(__name__) # Initialisation du logger

class ScenarioBackendValidationService:
    """Service pour valider les scénarios côté backend"""
    
    def __init__(self, no_grids=False):
        self.command_executor = CommandExecutor()
        self.command_decompressor = CommandDecompressor()
        self.no_grids = no_grids  # Option pour désactiver l'affichage des grilles
    
    def validate_scenario(self, subset: str, task_id: str, content: str, test_index: int) -> Dict[str, Any]:
        """
        Valide un scénario en l'exécutant et en comparant le résultat avec la sortie attendue.
        
        Args:
            subset: Le sous-ensemble de la tâche ('training', 'evaluation', etc.)
            task_id: L'identifiant de la tâche
            content: Le contenu du scénario sous forme de chaîne
            test_index: L'index du test à valider
            
        Returns:
            Dict contenant:
            - is_valid_by_backend: bool - Si le scénario est valide
            - command_count: int - Nombre de commandes dans le scénario
            - error_message: str - Message d'erreur si applicable
            - execution_result: dict - Résultat de l'exécution des commandes
            - comparison_details: dict - Détails de la comparaison des grilles
        """
        validation_output = {
            'is_valid_by_backend': False,
            'command_count': 0,
            'error_message': 'Erreur inconnue',
            'execution_result': None,
            'comparison_details': None
        }
        try:
            # 1. Parser le contenu pour extraire les commandes
            commands = self._parse_scenario_content(content)
            validation_output['command_count'] = len(commands) # Mettre à jour le compte tôt
            if not commands:
                validation_output['error_message'] = ErrorMessageFormatter.format_empty_scenario_error()
                return validation_output
            
            # 2. Récupérer les données de la tâche
            task_data = self._get_task_data(subset, task_id)
            if not task_data:
                validation_output['error_message'] = ErrorMessageFormatter.format_task_loading_error(task_id, subset)
                return validation_output
            
            # 3. Vérifier que l'index de test est valide
            if 'test' not in task_data or test_index >= len(task_data['test']):
                max_index = len(task_data.get('test', [])) - 1 if 'test' in task_data else -1
                validation_output['error_message'] = ErrorMessageFormatter.format_invalid_test_index_error(
                    test_index, task_id, max_index
                )
                return validation_output
            
            # 4. Récupérer les données du test
            test_data = task_data['test'][test_index]
            input_grid = test_data.get('input', []) # Non utilisé directement ici, mais pourrait l'être
            expected_output = test_data.get('output', [])
            
            if not expected_output:
                validation_output['error_message'] = ErrorMessageFormatter.format_missing_output_error(test_index, task_id)
                return validation_output
            
            # 5. Préparer les commandes (maintenant, ne fait que retourner les commandes telles quelles)
            commands_to_execute = self._prepare_commands_with_input(commands, input_grid)
            
            # 6. Configurer le CommandExecutor avec les données de tâche et l'index de test
            self.command_executor.task_data = task_data
            self.command_executor.test_index = test_index
            
            # 7. Exécuter les commandes et valider le résultat
            execution_result = self.command_executor.execute_commands(commands_to_execute)
            validation_output['execution_result'] = execution_result
            
            # 8. Vérifier si l'exécution a réussi
            if not execution_result.get('success', False):
                validation_output['is_valid_by_backend'] = False
                error_msg = execution_result.get('error', 'Erreur d\'exécution des commandes')
                history = execution_result.get('history', [])
                
                validation_output['error_message'] = ErrorMessageFormatter.format_execution_error(
                    error_msg, history, len(commands_to_execute)
                )
            else:
                # 9. Comparer la grille résultante avec la sortie attendue
                result_grid = execution_result.get('grid') # Utiliser 'grid' comme retourné par CommandExecutor
                
                if result_grid is None:
                    validation_output['is_valid_by_backend'] = False
                    validation_output['error_message'] = ErrorMessageFormatter.format_no_grid_generated_error(
                        len(commands_to_execute)
                    )
                else:
                    comparison_result = self._compare_grids(result_grid, expected_output)
                    validation_output['comparison_details'] = comparison_result
                    validation_output['is_valid_by_backend'] = comparison_result['is_identical']
                    
                    if not comparison_result['is_identical']:
                        validation_output['error_message'] = ErrorMessageFormatter.format_grid_comparison_error(
                            result_grid,
                            expected_output,
                            comparison_result['matching_cells'],
                            comparison_result['total_cells_expected']
                        )
                        # Log des grilles en cas de différence (seulement si no_grids=False)
                        if not self.no_grids:
                            logger.warning(
                                f" Validation échouée pour tâche {task_id}, test {test_index}."
                                f"Grille générée: {json.dumps(result_grid)}. "
                                f"Grille attendue: {json.dumps(expected_output)}."
                            )
                        else:
                            logger.warning(
                                f"\r Validation échouée pour tâche {task_id}, test {test_index}"
                                #f"Affichage des grilles désactivé (option --no-grids)."
                            )
                    else:
                         validation_output['error_message'] = None # Pas d'erreur si valide
            
            return validation_output
            
        except Exception as e:
            logger.error(f"Exception lors de la validation du scénario {task_id}, test {test_index}: {e}", exc_info=True)
            validation_output['error_message'] = ErrorMessageFormatter.format_internal_error(task_id, test_index, str(e))
            return validation_output
    
    def _parse_scenario_content(self, content: str) -> List[str]:
        """
        Parse le contenu du scénario pour extraire la liste des commandes.
        Décompresse les commandes groupées (SELECTS, EDITS, etc.) en commandes individuelles.
        La commande END est incluse et traitée par l'exécuteur.
        
        Args:
            content: Le contenu brut du scénario
            
        Returns:
            Liste des commandes extraites et décompressées (incluant END si présente)
        """
        if not content:
            return []
        
        # Diviser le contenu en lignes et nettoyer
        lines = content.strip().split('\n')
        raw_commands = []
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):  # Ignorer les lignes vides et les commentaires
                raw_commands.append(line)
                if line.upper() == 'END': # Si END est rencontré, on l'ajoute et on arrête de parser les suivantes
                    break
        
        # Décompresser les commandes groupées (SELECTS, EDITS, etc.)
        try:
            decompressed_commands = self.command_decompressor.decompress_commands(raw_commands)
            logger.info(f"Décompression: {len(raw_commands)} commandes -> {len(decompressed_commands)} commandes")
            return decompressed_commands
        except Exception as e:
            logger.error(f"Erreur lors de la décompression des commandes: {e}")
            # En cas d'erreur, retourner les commandes brutes
            return raw_commands
    
    def _get_task_data(self, subset: str, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Récupère les données d'une tâche depuis le système de fichiers.
        
        Args:
            subset: Le sous-ensemble de la tâche
            task_id: L'identifiant de la tâche
            
        Returns:
            Les données de la tâche ou None si non trouvée
        """
        try:
            # Construire le chemin vers le fichier de tâche
            arcdata_path = Path(settings.BASE_DIR).parent / 'arcdata'
            task_file = arcdata_path / subset / f"{task_id}.json"
            
            if not task_file.exists():
                logger.error(f"Fichier de tâche non trouvé: {task_file}")
                return None
            
            # Lire et parser le fichier JSON
            with open(task_file, 'r') as f:
                task_data = json.load(f)
            
            return task_data
            
        except Exception as e:
            logger.error(f"Erreur lors du chargement de la tâche {task_id} depuis {task_file}: {e}", exc_info=True)
            return None
    
    def _prepare_commands_with_input(self, commands: List[str], input_grid: List[List[int]]) -> List[str]:
        """
        Prépare les commandes en ajoutant l'initialisation de la grille d'input si nécessaire.
        
        Args:
            commands: Liste des commandes du scénario
            input_grid: Grille d'entrée de la tâche
            
        Returns:
            Liste des commandes avec initialisation
        """
        # Conformément aux retours, le backend ne doit pas modifier
        # la séquence de commandes reçue du frontend.
        # Le scénario doit être exécuté tel quel.
        # La responsabilité de fournir un scénario initialisant correctement la grille
        # (si nécessaire à partir de l'input de la tâche) incombe au frontend.
        return list(commands)
    
    def _generate_input_copy_commands(self, input_grid: List[List[int]]) -> List[str]:
        """
        Génère les commandes pour copier une grille d'input.
        
        Args:
            input_grid: La grille d'entrée à copier
            
        Returns:
            Liste des commandes EDIT pour copier la grille
        """
        commands = []
        height = len(input_grid)
        width = len(input_grid[0]) if height > 0 else 0
        
        for row in range(height):
            for col in range(width):
                value = input_grid[row][col]
                if value != 0:  # Ne copier que les cellules non-nulles
                    commands.append(f"EDIT {col},{row} {value}") # Note: CommandExecutor attend Y X
        
        return commands
    
    def get_command_count_from_content(self, content: str) -> int:
        """
        Compte le nombre de commandes dans un contenu de scénario.
        
        Args:
            content: Le contenu du scénario
            
        Returns:
            Le nombre de commandes
        """
        commands = self._parse_scenario_content(content)
        return len(commands)
    
    def _compare_grids(self, generated_grid: List[List[int]], expected_grid: List[List[int]]) -> Dict[str, Any]:
        """
        Compare deux grilles et retourne des détails sur la comparaison.
        
        Args:
            generated_grid: Grille générée par l'exécution du scénario.
            expected_grid: Grille de sortie attendue pour le test.
            
        Returns:
            Un dictionnaire avec les détails de la comparaison:
            - 'is_identical': bool
            - 'matching_cells': int
            - 'total_cells_expected': int
            - 'total_cells_generated': int
            - 'diff_points': List[Dict] - Liste des points de différence (optionnel, pour logs détaillés)
        """
        details = {
            'is_identical': False,
            'matching_cells': 0,
            'total_cells_expected': 0,
            'total_cells_generated': 0,
            'diff_points': [] # Pourrait être utilisé pour loguer les différences exactes
        }

        # Calculer le nombre total de cellules pour la grille générée
        if generated_grid:
            details['total_cells_generated'] = sum(len(row) for row in generated_grid)
        
        # Calculer le nombre total de cellules pour la grille attendue
        if expected_grid:
            details['total_cells_expected'] = sum(len(row) for row in expected_grid)

        # Vérification initiale des dimensions
        if not generated_grid and not expected_grid:
            details['is_identical'] = True
            return details
        if not generated_grid or not expected_grid: # L'une est vide, l'autre non
            return details
        
        gen_h, exp_h = len(generated_grid), len(expected_grid)
        if gen_h == 0 and exp_h == 0: # Les deux sont des listes vides de lignes
             details['is_identical'] = True
             return details
        if gen_h == 0 or exp_h == 0: # L'une a des lignes, l'autre non
            return details

        gen_w, exp_w = len(generated_grid[0]) if gen_h > 0 else 0, len(expected_grid[0]) if exp_h > 0 else 0
        
        if gen_h != exp_h or gen_w != exp_w:
            # Les dimensions ne correspondent pas, compter les correspondances sur la zone commune
            # Pour simplifier, si les dimensions diffèrent, on considère qu'elles ne sont pas identiques
            # et le comptage des cellules correspondantes se fera sur la plus petite intersection.
            # Cependant, la définition stricte de "is_identical" requiert des dimensions identiques.
            # On peut affiner le comptage des matching_cells si nécessaire.
            # Pour l'instant, si les dimensions diffèrent, is_identical reste False.
            
            # Comptons les cellules correspondantes dans la zone d'intersection
            min_h = min(gen_h, exp_h)
            min_w = min(gen_w, exp_w)
            matching_count = 0
            for r in range(min_h):
                for c in range(min_w):
                    if generated_grid[r][c] == expected_grid[r][c]:
                        matching_count += 1
            details['matching_cells'] = matching_count
            return details # is_identical reste False

        # Les dimensions sont identiques, comparer cellule par cellule
        matching_count = 0
        is_fully_identical = True
        for r in range(exp_h):
            for c in range(exp_w):
                if generated_grid[r][c] == expected_grid[r][c]:
                    matching_count += 1
                else:
                    is_fully_identical = False
                    # Optionnel: enregistrer le point de différence
                    # details['diff_points'].append({'r': r, 'c': c, 'gen': generated_grid[r][c], 'exp': expected_grid[r][c]})
        
        details['matching_cells'] = matching_count
        details['is_identical'] = is_fully_identical
        
        return details
    
    def _looks_like_command(self, line: str) -> bool:
        """Mise à jour des commandes valides selon nouveau format"""
        unified_commands = [
            # Actions de base
            'CLEAR', 'FILL', 'SURROUND', 'REPLACE', 'EDIT',
            # Modifications structurelles
            'INSERT', 'DELETE', 'EXTRACT',
            # Transformations (motifs)
            'FLIP', 'ROTATE', 'COPY', 'CUT', 'PASTE',
            # Multiplication/Division
            'MULTIPLY', 'DIVIDE',
            # Sélections spéciales
            'SELECT_RELEASE',
            # Système
            'TRANSFERT', 'END'
        ]
        
        # Supprimer complètement les anciens formats
        obsolete_commands = [
            'PROPOSE', 'VALIDATE', 'ROTATE_R', 'ROTATE_L',
            'FLIP_H', 'FLIP_V', 'INSERT_R', 'DELETE_R'
        ]
        
        first_word = line.split()[0].upper() if line.split() else ''
        
        # Vérifier format unifié
        if first_word in unified_commands:
            return self._validate_unified_syntax(line)
          # Rejeter les commandes obsolètes
        if first_word in obsolete_commands:
            return False
            
        return False

    def _validate_unified_syntax(self, line: str) -> bool:
        """Valide la syntaxe unifiée: ACTION paramètres [coordonnées]"""
        # Regex pour format unifié standard
        unified_pattern = r'^([A-Z_]+)(\s+[^[\]]+)?(\s*\[[^\]]+\])*$'
        
        # Regex pour motifs simples: COPY([...]), PASTE([...])
        motif_simple_pattern = r'^([A-Z_]+)\s*\([^)]+\)$'
        
        # Regex pour motifs avec direction: FLIP HORIZONTAL([...]), ROTATE LEFT([...])
        motif_direction_pattern = r'^(FLIP|ROTATE)\s+(HORIZONTAL|VERTICAL|LEFT|RIGHT|CLOCKWISE|COUNTERCLOCKWISE)\s*[^)]+$'
       
        return (re.match(unified_pattern, line) or
                re.match(motif_simple_pattern, line) or
                re.match(motif_direction_pattern, line))