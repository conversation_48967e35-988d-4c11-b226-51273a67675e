// src/components/resolution/hooks/useClipboard.ts
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { Grid } from '../../../lib/grid';
import { type ClipboardState } from '../types/clipboardTypes'; // GridContent supprimé car non utilisé
import { useAutomationStore } from './useAutomationStore';
import { useSelection } from './useSelection';
// formatUnifiedCommand supprimé - utilisation via automationStore
import {
  rotateClipboardRight,
  rotateClipboardLeft,
  flipClipboardHorizontal,
  flipClipboardVertical,
  moveClipboardUp,
  moveClipboardDown,
  moveClipboardLeft,
  moveClipboardRight,
  extractClipboardDataFromGrid, // Ajout
  applyClipboardContentToGrid // Ajout
} from '../utils/clipboardTransformUtils';

/**
 * Store Zustand pour le presse-papier
 */
export const useClipboard = create<ClipboardState>()(
  immer((set, get) => ({
    // État initial
    content: null,
    currentPosition: null,

    // REFONTE SYSTÈME SÉLECTION - État de transfert depuis sélection standard
    hasTransferredSelection: false, // Indique si le clipboard contient une sélection transférée

    // Copier une sélection
    copy: (grid: Grid, startRow: number, startCol: number, endRow: number, endCol: number) =>
      set((state) => {
        // Normaliser les coordonnées
        const minRow = Math.min(startRow, endRow);
        const maxRow = Math.max(startRow, endRow);
        const minCol = Math.min(startCol, endCol);
        const maxCol = Math.max(startCol, endCol);

        const { selectedCells } = useSelection.getState();
        const clipboardContentData = extractClipboardDataFromGrid(
          grid,
          { minRow, maxRow, minCol, maxCol },
          selectedCells
        );

        // Mettre à jour le contenu du presse-papier
        state.content = {
          data: clipboardContentData.data,
          mask: clipboardContentData.mask,
          width: clipboardContentData.width,
          height: clipboardContentData.height,
          sourceRow: minRow,
          sourceCol: minCol,
          isCut: false, // Indique que le contenu a été copié, pas coupé
          timestamp: Date.now() // Ajouter un timestamp pour forcer la détection du changement
        };

        // Initialiser la position actuelle
        state.currentPosition = {
          row: minRow,
          col: minCol
        };

        // REFONTE SYSTÈME SÉLECTION - Marquer le transfert de sélection
        state.hasTransferredSelection = true;

        // Ajouter une commande d'automatisation
        // try {
        //   const automationStore = useAutomationStore.getState();
        //   if (automationStore.isRecording) {
        //     const coordinates = [`${minRow},${minCol}`, `${maxRow},${maxCol}`];
        //     // Utiliser formatUnifiedCommand avec isMotif=true pour COPY
        //     const command = automationStore.formatUnifiedCommand('COPY', '', coordinates, undefined);
        //     automationStore.addCommand(command);
        //   }
        // } catch (error) {
        //   console.error('[Clipboard] Failed to add automation command:', error);
        // }
      }),

    // Couper une sélection
    cut: (grid: Grid, startRow: number, startCol: number, endRow: number, endCol: number) => {
      // Normaliser les coordonnées
      const minRow = Math.min(startRow, endRow);
      const maxRow = Math.max(startRow, endRow);
      const minCol = Math.min(startCol, endCol);
      const maxCol = Math.max(startCol, endCol);

      // Copier la sélection SANS enregistrer de commande COPY (pour éviter le doublon)
      set((state) => {
        const { selectedCells } = useSelection.getState();
        const clipboardContentData = extractClipboardDataFromGrid(
          grid,
          { minRow, maxRow, minCol, maxCol },
          selectedCells
        );
        
        // Mettre à jour le contenu du presse-papier
        state.content = {
          data: clipboardContentData.data,
          mask: clipboardContentData.mask,
          width: clipboardContentData.width,
          height: clipboardContentData.height,
          sourceRow: minRow,
          sourceCol: minCol,
          isCut: true, // Marquer comme coupé
          timestamp: Date.now()
        };

        // Initialiser la position actuelle
        state.currentPosition = {
          row: minRow,
          col: minCol
        };

        // REFONTE SYSTÈME SÉLECTION - Marquer le transfert de sélection pour cut aussi
        state.hasTransferredSelection = true;
      });

      // Enregistrer SEULEMENT la commande CUT (pas COPY)
      try {
        const automationStore = useAutomationStore.getState();
        if (automationStore.isRecording) {
          const coordinates = [`${minRow},${minCol}`, `${maxRow},${maxCol}`];
          // Utiliser formatUnifiedCommand avec isMotif=true pour CUT
          const command = automationStore.formatUnifiedCommand('CUT', '', coordinates, undefined);
          automationStore.addCommand(command);
        }
      } catch (error) {
        console.error('[Clipboard] Failed to add automation command:', error);
      }



      // Retourner l'état actuel
      return get();
    },

    // Coller le contenu du presse-papier
    paste: (grid: Grid, targetRow: number = 0, targetCol: number = 0) => {
      const content = get().content;

      if (!content) {
        return grid;
      }

      // Utiliser la position spécifiée ou la position par défaut (0, 0)
      // Nous n'utilisons plus currentPosition ici car elle est déjà gérée dans le composant ResolutionGrid
      const pasteRow = targetRow;
      const pasteCol = targetCol;

      let newGrid = grid.clone();
      newGrid = applyClipboardContentToGrid(newGrid, content, pasteRow, pasteCol);

      // Nous n'effaçons plus les cellules d'origine lors du collage
      // car elles sont déjà effacées dans ClipboardToolbar.tsx
      // Nous gardons ce commentaire pour référence

      // Ajouter une commande d'automatisation
      try {
        const automationStore = useAutomationStore.getState();
        if (automationStore.isRecording) {
          const coordinates = [`${pasteRow},${pasteCol}`];
          // Utiliser formatUnifiedCommand avec isMotif=true pour PASTE
          const command = automationStore.formatUnifiedCommand('PASTE', '', coordinates, undefined);
          automationStore.addCommand(command);
        }
      } catch (error) {
        console.error('[Clipboard] Failed to add automation command:', error);
      }



      // Ne pas effacer la position actuelle après le collage
      // pour permettre de coller plusieurs fois au même endroit
      // et pour que le bouton "Coller" fonctionne correctement
      // set((state) => {
      //   state.currentPosition = null;
      // });

      return newGrid;
    },

    // Effacer le contenu du presse-papier
    clear: () =>
      set((state) => {
        state.content = null;
        state.currentPosition = null;
      }),

    // Déplacer le motif vers le haut
    moveUp: () => {
      console.log('[useClipboard] moveUp called');
      const currentState = get();
      console.log('[useClipboard] Current position before moveUp:', currentState.currentPosition);
      moveClipboardUp(set, get, useSelection.getState(), useAutomationStore.getState());
      const newState = get();
      console.log('[useClipboard] New position after moveUp:', newState.currentPosition);
    },

    // Déplacer le motif vers le bas
    moveDown: () => {
      console.log('[useClipboard] moveDown called');
      const currentState = get();
      console.log('[useClipboard] Current position before moveDown:', currentState.currentPosition);
      moveClipboardDown(set, get, useSelection.getState(), useAutomationStore.getState());
      const newState = get();
      console.log('[useClipboard] New position after moveDown:', newState.currentPosition);
    },

    // Déplacer le motif vers la gauche
    moveLeft: () => {
      console.log('[useClipboard] moveLeft called');
      const currentState = get();
      console.log('[useClipboard] Current position before moveLeft:', currentState.currentPosition);
      moveClipboardLeft(set, get, useSelection.getState(), useAutomationStore.getState());
      const newState = get();
      console.log('[useClipboard] New position after moveLeft:', newState.currentPosition);
    },

    // Déplacer le motif vers la droite
    moveRight: () => {
      console.log('[useClipboard] moveRight called');
      const currentState = get();
      console.log('[useClipboard] Current position before moveRight:', currentState.currentPosition);
      moveClipboardRight(set, get, useSelection.getState(), useAutomationStore.getState());
      const newState = get();
      console.log('[useClipboard] New position after moveRight:', newState.currentPosition);
    },
// Rotation à droite (90° dans le sens horaire)
rotateRight: () => {
  rotateClipboardRight(set, get, useSelection, useAutomationStore.getState());
},

// Rotation à gauche (90° dans le sens anti-horaire)
rotateLeft: () => {
  rotateClipboardLeft(set, get, useSelection, useAutomationStore.getState());
},

// Retournement horizontal
flipHorizontal: () => {
  flipClipboardHorizontal(set, get, useSelection, useAutomationStore.getState());
},

// Retournement vertical
flipVertical: () => {
  flipClipboardVertical(set, get, useSelection, useAutomationStore.getState());
},
    // Vérifier si le presse-papier contient quelque chose
    hasContent: () => {
      return get().content !== null;
    },



    // Obtenir un aperçu du contenu du presse-papier
    getContentPreview: () => {
      return get().content;
    },

    // Restaurer le contenu coupé si l'utilisateur annule l'opération
    restoreCutContent: (grid: Grid) => {
      const content = get().content;
      
      // Vérifier si le contenu existe et a été coupé
      if (!content || !content.isCut) {
        return grid;
      }

      let newGrid = grid.clone();
      newGrid = applyClipboardContentToGrid(newGrid, content, content.sourceRow, content.sourceCol);

      return newGrid;
    },

    // NOUVELLES MÉTHODES POUR REFONTE SYSTÈME SÉLECTION

    // Accepter le transfert d'une sélection depuis le système de sélection standard
    acceptSelectionTransfer: (selectionData: any) => {
      set((state) => {
        state.hasTransferredSelection = true;
        console.log('[Clipboard] Accepted selection transfer:', selectionData);
      });
    },

    // Effacer toutes les sélections (clipboard + sélection standard via useSelection)
    clearAllSelections: () => {
      set((state) => {
        // Vider le clipboard
        state.content = null;
        state.currentPosition = null;
        state.hasTransferredSelection = false;
        

      });

      // Déclencher aussi le nettoyage côté sélection standard
      try {
        const selectionStore = useSelection.getState();
        selectionStore.clearAllSelections();
      } catch (error) {
        console.error('[Clipboard] Failed to clear selections on selection side:', error);
      }
    },
  }))
);
