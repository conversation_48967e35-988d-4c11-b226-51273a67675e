{"cells": [{"cell_type": "markdown", "source": ["# Analyse de tâche ARC\n", "\n", "Ce notebook permet d'analyser une tâche ARC et d'explorer différentes approches pour la résoudre."], "metadata": {}}, {"cell_type": "code", "source": ["# Importer le module arc_utils et les bibliothèques nécessaires\n", "import arc_utils\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import json\n", "\n", "# Vérifier que le module est correctement chargé\n", "print(\"Module arc_utils chargé avec succès!\")\n", "\n", "# Fonction pour afficher une grille\n", "def display_grid(grid, title=None):\n", "    plt.figure(figsize=(5, 5))\n", "    plt.imshow(grid, cmap='tab10', vmin=0, vmax=9)\n", "    plt.grid(True, color='black', linestyle='-', linewidth=1)\n", "    plt.xticks(np.arange(-0.5, len(grid[0]), 1), [])\n", "    plt.yticks(np.arange(-0.5, len(grid), 1), [])\n", "    \n", "    # Ajouter les valeurs dans les cellules\n", "    for i in range(len(grid)):\n", "        for j in range(len(grid[0])):\n", "            plt.text(j, i, str(grid[i][j]), ha='center', va='center', color='white', fontweight='bold')\n", "    \n", "    if title:\n", "        plt.title(title)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Fonction pour envoyer des commandes à l'éditeur d'automatisation\n", "def send_commands(commands):\n", "    try:\n", "        # Importer le module js pour interagir avec JavaScript\n", "        import js\n", "        \n", "        # Convertir les commandes en JSON\n", "        commands_json = json.dumps(commands)\n", "        \n", "        # Envoyer les commandes au frontend\n", "        if hasattr(js.window.parent, 'notebookCommandsCallback'):\n", "            js.window.parent.notebookCommandsCallback(commands_json)\n", "            print('Commandes envoyées au frontend')\n", "        else:\n", "            print('Callback pour les commandes non disponible dans le frontend')\n", "    except Exception as e:\n", "        print(f'Erreur lors de l\\'envoi des commandes au frontend: {e}')"], "metadata": {}, "outputs": []}, {"cell_type": "code", "source": ["# Variables pour stocker les données de la tâche\n", "task_id = None\n", "train_input = []\n", "train_output = []\n", "test_input = []\n", "train_examples = []\n", "test_example = None\n", "\n", "# Dan<PERSON>, les données de la tâche sont injectées directement par PythonExecutor.ts\n", "# Vérifier si les variables globales task_id, train_input, train_output et test_input existent déjà\n", "import sys\n", "if 'task_id' in globals() and task_id is not None:\n", "    print(f\"Tâche ARC chargée: {task_id}\")\n", "    if 'train_input' in globals() and 'train_output' in globals() and len(train_input) == len(train_output):\n", "        train_examples = [{'input': inp, 'output': out} for inp, out in zip(train_input, train_output)]\n", "        print(f\"Exemples d'entraînement: {len(train_examples)}\")\n", "    if 'test_input' in globals() and len(test_input) > 0:\n", "        test_example = {'input': test_input[0]}\n", "        print(f\"Exemples de test: {len(test_input)}\")\n", "else:\n", "    # Si les données ne sont pas injectées, essayer de les charger depuis arcdata\n", "    # Utiliser un ID de tâche par défaut pour les tests\n", "    default_task_id = \"1190e5a7\"\n", "    print(f\"Tentative de chargement de la tâche {default_task_id} depuis arcdata...\")\n", "    try:\n", "        # Vérifier si nous sommes dans Pyodide ou dans un environnement Jupyter normal\n", "        if 'js' in sys.modules:\n", "            # Nous sommes dans Pyodide, utiliser la fonction get_current_task\n", "            import js\n", "            if hasattr(js.window.parent, 'currentTask'):\n", "                task_data = js.window.parent.currentTask\n", "                task_id = js.window.parent.taskName or default_task_id\n", "                \n", "                # Convertir les données JavaScript en Python\n", "                train_data = []\n", "                for pair in task_data.train:\n", "                    train_data.append({\n", "                        'input': pair.input,\n", "                        'output': pair.output\n", "                    })\n", "                \n", "                test_data = []\n", "                for pair in task_data.test:\n", "                    test_data.append({\n", "                        'input': pair.input\n", "                    })\n", "                \n", "                # Extraire les données d'entrée et de sortie\n", "                train_input = [pair['input'] for pair in train_data]\n", "                train_output = [pair['output'] for pair in train_data]\n", "                test_input = [pair['input'] for pair in test_data]\n", "                \n", "                # <PERSON><PERSON><PERSON> les exemples\n", "                train_examples = [{'input': inp, 'output': out} for inp, out in zip(train_input, train_output)]\n", "                if test_input and len(test_input) > 0:\n", "                    test_example = {'input': test_input[0]}\n", "                \n", "                print(f\"Tâche {task_id} chargée avec succès depuis le frontend\")\n", "            else:\n", "                print(\"Aucune tâche disponible dans le frontend\")\n", "        else:\n", "            # Nous sommes dans un environnement Jupyter normal, utiliser arc_utils.load_task\n", "            task_data = arc_utils.load_task(default_task_id)\n", "            if task_data:\n", "                task_id = default_task_id\n", "                train_input = [pair[\"input\"] for pair in task_data[\"train\"]]\n", "                train_output = [pair[\"output\"] for pair in task_data[\"train\"]]\n", "                test_input = [pair[\"input\"] for pair in task_data[\"test\"]]\n", "                train_examples = [{'input': inp, 'output': out} for inp, out in zip(train_input, train_output)]\n", "                if test_input and len(test_input) > 0:\n", "                    test_example = {'input': test_input[0]}\n", "                print(f\"Tâche {task_id} chargée avec succès depuis arcdata\")\n", "    except Exception as e:\n", "        print(f\"Erreur lors du chargement de la tâche: {e}\")\n", "\n", "# Afficher un résumé\n", "print(f\"Nombre d'exemples d'entraînement: {len(train_examples)}\")\n", "print(f\"Exemple de test disponible: {test_example is not None}\")"], "metadata": {}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Visualisation des exemples\n", "\n", "Affichons les exemples d'entraînement pour comprendre la tâche."], "metadata": {}}, {"cell_type": "code", "source": ["# Afficher les exemples d'entraînement\n", "for i, example in enumerate(train_examples):\n", "    print(f\"\\nExemple d'entraînement {i+1}:\")\n", "    print(\"Input:\")\n", "    display_grid(example[\"input\"], f\"Entrée {i+1}\")\n", "    print(\"Output:\")\n", "    display_grid(example[\"output\"], f\"Sortie {i+1}\")"], "metadata": {}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Analyse des exemples\n", "\n", "Analysons les exemples pour comprendre la transformation."], "metadata": {}}, {"cell_type": "code", "source": ["# Fonction pour analyser les différences entre l'entrée et la sortie\n", "def analyze_differences(input_grid, output_grid):\n", "    if isinstance(input_grid, list):\n", "        input_grid = np.array(input_grid)\n", "    if isinstance(output_grid, list):\n", "        output_grid = np.array(output_grid)\n", "    \n", "    # Vérifier si les dimensions sont les mêmes\n", "    if input_grid.shape != output_grid.shape:\n", "        print(f\"Les dimensions sont différentes: {input_grid.shape} -> {output_grid.shape}\")\n", "        return\n", "    \n", "    # Compter les cellules modifiées\n", "    diff = (input_grid != output_grid)\n", "    num_diff = np.sum(diff)\n", "    print(f\"Nombre de cellules modifiées: {num_diff} sur {input_grid.size} ({num_diff/input_grid.size*100:.1f}%)\")\n", "    \n", "    # Analyser les valeurs\n", "    input_values = np.unique(input_grid)\n", "    output_values = np.unique(output_grid)\n", "    print(f\"Valeurs dans l'entrée: {input_values}\")\n", "    print(f\"Valeurs dans la sortie: {output_values}\")\n", "    \n", "    # Afficher une grille des différences\n", "    diff_grid = np.zeros_like(input_grid)\n", "    diff_grid[diff] = output_grid[diff]\n", "    print(\"\\nGrille des différences (cellules modifiées):\")\n", "    display_grid(diff_grid, \"Différences\")\n", "\n", "# Analyser les différences pour chaque exemple\n", "for i, example in enumerate(train_examples):\n", "    print(f\"\\nAnalyse de l'exemple {i+1}:\")\n", "    analyze_differences(example[\"input\"], example[\"output\"])"], "metadata": {}, "execution_count": null, "outputs": []}], "metadata": {"language_info": {"codemirror_mode": {"name": "python", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8"}, "kernelspec": {"display_name": "Python (Pyodide)", "language": "python", "name": "python"}}, "nbformat": 4, "nbformat_minor": 4}