"""
Module utilitaire pour formater les messages d'erreur de validation des scénarios ARC.
Ce module centralise la logique de formatage des messages d'erreur pour une réutilisation
dans différents scripts de validation.
"""

from typing import Dict, List, Any, Optional


class ErrorMessageFormatter:
    """
    Classe utilitaire pour formater les messages d'erreur de validation des scénarios.
    
    Cette classe fournit des méthodes statiques pour générer des messages d'erreur
    informatifs et cohérents à travers tous les scripts de validation.
    """
    
    @staticmethod
    def format_execution_error(error_msg: str, history: List[str], total_commands: int) -> str:
        """
        Formate un message d'erreur d'exécution de commandes.
        
        Args:
            error_msg: Message d'erreur original
            history: Historique des commandes exécutées
            total_commands: Nombre total de commandes à exécuter
            
        Returns:
            Message d'erreur formaté avec contexte
        """
        if history:
            last_command = history[-1] if history else "inconnue"
            return (
                f"Échec à l'étape {len(history)}/{total_commands}: {error_msg}. "
                f"Dernière commande exécutée: '{last_command}'"
            )
        else:
            return f"Échec dès la première commande: {error_msg}"
    
    @staticmethod
    def format_empty_scenario_error() -> str:
        """
        Formate un message d'erreur pour un scénario vide.
        
        Returns:
            Message d'erreur formaté
        """
        return (
            "Aucune commande valide trouvée dans le scénario. "
            "Vérifiez que le fichier contient des commandes AGI valides et n'est pas vide."
        )
    
    @staticmethod
    def format_task_loading_error(task_id: str, subset: str) -> str:
        """
        Formate un message d'erreur pour le chargement d'une tâche.
        
        Args:
            task_id: Identifiant de la tâche
            subset: Sous-ensemble (training, evaluation, etc.)
            
        Returns:
            Message d'erreur formaté
        """
        return (
            f"Impossible de charger les données de la tâche {task_id} depuis arcdata/{subset}/. "
            f"Vérifiez que le fichier {task_id}.json existe et est valide."
        )
    
    @staticmethod
    def format_invalid_test_index_error(test_index: int, task_id: str, max_index: int) -> str:
        """
        Formate un message d'erreur pour un index de test invalide.
        
        Args:
            test_index: Index de test demandé
            task_id: Identifiant de la tâche
            max_index: Index maximum valide
            
        Returns:
            Message d'erreur formaté
        """
        return (
            f"Index de test {test_index} invalide pour la tâche {task_id}. "
            f"Index valides: 0 à {max_index} ({max_index + 1} tests disponibles)."
        )
    
    @staticmethod
    def format_missing_output_error(test_index: int, task_id: str) -> str:
        """
        Formate un message d'erreur pour une sortie attendue manquante.
        
        Args:
            test_index: Index du test
            task_id: Identifiant de la tâche
            
        Returns:
            Message d'erreur formaté
        """
        return (
            f"Pas de sortie attendue (output) définie pour le test {test_index} de la tâche {task_id}. "
            f"Vérifiez l'intégrité du fichier de données de la tâche."
        )
    
    @staticmethod
    def format_no_grid_generated_error(total_commands: int) -> str:
        """
        Formate un message d'erreur quand aucune grille n'est générée.
        
        Args:
            total_commands: Nombre total de commandes exécutées
            
        Returns:
            Message d'erreur formaté
        """
        return (
            f"Aucune grille générée après l'exécution de {total_commands} commandes. "
            f"Vérifiez que le scénario contient une commande d'initialisation (INPUT ou INIT)."
        )
    
    @staticmethod
    def format_grid_comparison_error(
        generated_grid: List[List[int]], 
        expected_grid: List[List[int]], 
        matching_cells: int, 
        total_cells: int
    ) -> str:
        """
        Formate un message d'erreur pour une comparaison de grilles échouée.
        
        Args:
            generated_grid: Grille générée
            expected_grid: Grille attendue
            matching_cells: Nombre de cellules correspondantes
            total_cells: Nombre total de cellules attendues
            
        Returns:
            Message d'erreur formaté
        """
        # Calculer le pourcentage de correspondance
        match_percentage = (matching_cells / total_cells * 100) if total_cells > 0 else 0
        
        # Calculer les dimensions
        gen_dims = f"{len(generated_grid)}x{len(generated_grid[0]) if generated_grid else 0}"
        exp_dims = f"{len(expected_grid)}x{len(expected_grid[0]) if expected_grid else 0}"
        
        if gen_dims != exp_dims:
            return (
                f"Dimensions incorrectes: grille générée {gen_dims}, attendue {exp_dims}. "
                f"Correspondance partielle: {matching_cells}/{total_cells} cellules ({match_percentage:.1f}%)"
            )
        else:
            return (
                f"Contenu incorrect: {matching_cells}/{total_cells} cellules correctes ({match_percentage:.1f}%). "
                f"Vérifiez les transformations appliquées dans le scénario."
            )
    
    @staticmethod
    def format_internal_error(task_id: str, test_index: int, error: str) -> str:
        """
        Formate un message d'erreur interne.
        
        Args:
            task_id: Identifiant de la tâche
            test_index: Index du test
            error: Message d'erreur original
            
        Returns:
            Message d'erreur formaté
        """
        return (
            f"Erreur interne lors de la validation de {task_id}_TEST{test_index}: {error}. "
            f"Consultez les logs pour plus de détails."
        )
    
    @staticmethod
    def format_command_parsing_error(command: str, line_number: int) -> str:
        """
        Formate un message d'erreur pour le parsing d'une commande.
        
        Args:
            command: Commande qui a échoué
            line_number: Numéro de ligne dans le scénario
            
        Returns:
            Message d'erreur formaté
        """
        return (
            f"Erreur de parsing à la ligne {line_number}: '{command}'. "
            f"Vérifiez la syntaxe de la commande selon le format AGI unifié."
        )
    
    @staticmethod
    def format_decompression_error(error: str, command_count: int) -> str:
        """
        Formate un message d'erreur pour la décompression des commandes.
        
        Args:
            error: Message d'erreur de décompression
            command_count: Nombre de commandes avant décompression
            
        Returns:
            Message d'erreur formaté
        """
        return (
            f"Erreur lors de la décompression des {command_count} commandes: {error}. "
            f"Vérifiez que les commandes groupées (MOTIF, TRANSFERT, etc.) sont correctement formatées."
        )
    
    @staticmethod
    def format_validation_summary(
        total_scenarios: int, 
        successful: int, 
        failed: int, 
        success_rate: float
    ) -> str:
        """
        Formate un résumé de validation.
        
        Args:
            total_scenarios: Nombre total de scénarios traités
            successful: Nombre de validations réussies
            failed: Nombre de validations échouées
            success_rate: Taux de succès en pourcentage
            
        Returns:
            Résumé formaté
        """
        return (
            f"📊 Résumé: {total_scenarios} scénarios traités, "
            f"{successful} réussites, {failed} échecs "
            f"(taux de succès: {success_rate:.1f}%)"
        )


class ValidationErrorContext:
    """
    Classe pour encapsuler le contexte d'une erreur de validation.
    Facilite la création de messages d'erreur cohérents.
    """
    
    def __init__(self, task_id: str, test_index: int, subset: str = "training"):
        self.task_id = task_id
        self.test_index = test_index
        self.subset = subset
        self.scenario_name = f"{task_id}_TEST{test_index}"
    
    def format_error(self, error_type: str, **kwargs) -> str:
        """
        Formate une erreur selon son type avec le contexte approprié.
        
        Args:
            error_type: Type d'erreur (execution, grid_comparison, etc.)
            **kwargs: Arguments spécifiques au type d'erreur
            
        Returns:
            Message d'erreur formaté
        """
        formatter = ErrorMessageFormatter()
        
        if error_type == "execution":
            return formatter.format_execution_error(
                kwargs.get("error_msg", ""),
                kwargs.get("history", []),
                kwargs.get("total_commands", 0)
            )
        elif error_type == "grid_comparison":
            return formatter.format_grid_comparison_error(
                kwargs.get("generated_grid", []),
                kwargs.get("expected_grid", []),
                kwargs.get("matching_cells", 0),
                kwargs.get("total_cells", 0)
            )
        elif error_type == "task_loading":
            return formatter.format_task_loading_error(self.task_id, self.subset)
        elif error_type == "invalid_test_index":
            return formatter.format_invalid_test_index_error(
                self.test_index, 
                self.task_id, 
                kwargs.get("max_index", -1)
            )
        elif error_type == "missing_output":
            return formatter.format_missing_output_error(self.test_index, self.task_id)
        elif error_type == "no_grid_generated":
            return formatter.format_no_grid_generated_error(kwargs.get("total_commands", 0))
        elif error_type == "internal":
            return formatter.format_internal_error(
                self.task_id, 
                self.test_index, 
                kwargs.get("error", "")
            )
        else:
            return f"Erreur de type inconnu: {error_type}"


# Fonctions utilitaires pour une utilisation simple
def format_validation_error(error_type: str, task_id: str, test_index: int, **kwargs) -> str:
    """
    Fonction utilitaire pour formater rapidement une erreur de validation.
    
    Args:
        error_type: Type d'erreur
        task_id: Identifiant de la tâche
        test_index: Index du test
        **kwargs: Arguments spécifiques au type d'erreur
        
    Returns:
        Message d'erreur formaté
    """
    context = ValidationErrorContext(task_id, test_index, kwargs.get("subset", "training"))
    return context.format_error(error_type, **kwargs)


def create_error_context(task_id: str, test_index: int, subset: str = "training") -> ValidationErrorContext:
    """
    Crée un contexte d'erreur pour une tâche et un test donnés.
    
    Args:
        task_id: Identifiant de la tâche
        test_index: Index du test
        subset: Sous-ensemble (training, evaluation, etc.)
        
    Returns:
        Contexte d'erreur configuré
    """
    return ValidationErrorContext(task_id, test_index, subset)