#!/usr/bin/env python3
"""
Test de la commande INPUT nouvellement implémentée
"""
import sys
import os
import json

# Ajouter le chemin du backend pour les imports Django
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
import django
django.setup()

from command_system.command_executor import CommandExecutor
from command_system.command_validator import CommandValidator

def test_input_command():
    """Test de la commande INPUT"""
    print("=== Test de la commande INPUT ===")
    
    # Charger une tâche de test
    task_file = 'arcdata/training/0e206a2e.json'
    if not os.path.exists(task_file):
        print(f"❌ Fichier de tâche non trouvé: {task_file}")
        return False
    
    with open(task_file, 'r') as f:
        task_data = json.load(f)
    
    print(f"✅ Tâche chargée: {len(task_data['test'])} tests disponibles")
    
    # Test 1: Commande INPUT seule
    print("\n--- Test 1: Commande INPUT seule ---")
    commands = ["INPUT"]
    
    executor = CommandExecutor(task_data, 0)  # Test index 0
    result = executor.execute_commands(commands)
    
    print(f"Succès: {result['success']}")
    if result['success']:
        print(f"Grille chargée: {result['height']}x{result['width']}")
        print(f"Historique: {result['history']}")
        print("✅ Test 1 réussi")
    else:
        print(f"Erreur: {result['error']}")
        print("❌ Test 1 échoué")
        return False
    
    # Test 2: Scénario complet avec INPUT
    print("\n--- Test 2: Scénario avec INPUT + EDIT + END ---")
    commands = [
        "INPUT",
        "EDIT 5 [1,1]",
        "END"
    ]
    
    executor = CommandExecutor(task_data, 0)
    result = executor.execute_commands(commands)
    
    print(f"Succès: {result['success']}")
    if result['success']:
        print(f"Grille finale: {result['height']}x{result['width']}")
        print(f"Historique: {result['history']}")
        print("✅ Test 2 réussi")
    else:
        print(f"Erreur: {result['error']}")
        print("❌ Test 2 échoué")
        return False
    
    # Test 3: Validation avec CommandValidator
    print("\n--- Test 3: Validation avec CommandValidator ---")
    validator = CommandValidator()
    validation_result = validator.validate_commands(commands, task_data, 0)
    
    print(f"Validation réussie: {validation_result['valid']}")
    if validation_result['valid']:
        print(f"Commandes valides: {len(validation_result['valid_commands'])}")
        print(f"Exécution réussie: {validation_result['execution_result']['success']}")
        print("✅ Test 3 réussi")
    else:
        print(f"Erreurs: {validation_result['errors']}")
        print("❌ Test 3 échoué")
        return False
    
    # Test 4: Test avec index différent
    print("\n--- Test 4: INPUT avec test_index=1 ---")
    if len(task_data['test']) > 1:
        commands = ["INPUT"]
        executor = CommandExecutor(task_data, 1)  # Test index 1
        result = executor.execute_commands(commands)
        
        print(f"Succès: {result['success']}")
        if result['success']:
            print(f"Grille test 1 chargée: {result['height']}x{result['width']}")
            print("✅ Test 4 réussi")
        else:
            print(f"Erreur: {result['error']}")
            print("❌ Test 4 échoué")
            return False
    else:
        print("⚠️ Test 4 ignoré: pas assez de tests dans la tâche")
    
    print("\n🎉 Tous les tests INPUT réussis!")
    return True

if __name__ == "__main__":
    test_input_command()
