<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Commande INPUT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Test de la Commande INPUT</h1>
    
    <div class="test-section info">
        <h2>Objectif</h2>
        <p>Tester que la commande INPUT remplace correctement TRANSFERT pour charger la grille d'input du test.</p>
    </div>

    <div class="test-section">
        <h2>Test 1: Parser la commande INPUT</h2>
        <button onclick="testParseInput()">Tester le parsing</button>
        <div id="parse-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Validation de la commande INPUT</h2>
        <button onclick="testValidateInput()">Tester la validation</button>
        <div id="validate-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Coloration syntaxique</h2>
        <button onclick="testSyntaxHighlighting()">Tester la coloration</button>
        <div id="syntax-result"></div>
    </div>

    <div class="test-section">
        <h2>Instructions pour test manuel</h2>
        <ol>
            <li>Ouvrir l'application ARC Puzzle</li>
            <li>Charger une tâche avec des tests</li>
            <li>Aller dans l'onglet Résolution</li>
            <li>Cliquer sur "Transférer" depuis un test</li>
            <li>Vérifier que la commande générée est "INPUT" au lieu de "TRANSFERT {...}"</li>
            <li>Tester le replay de la commande INPUT</li>
        </ol>
    </div>

    <script>
        function testParseInput() {
            const resultDiv = document.getElementById('parse-result');
            
            try {
                // Simuler le test de parsing (normalement fait par unifiedCommandParser)
                const command = 'INPUT';
                
                // Test basique de format
                if (command.trim() === 'INPUT') {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Test de parsing réussi</h3>
                            <pre>Commande: "${command}"
Format: Valide (commande simple sans paramètres)
Type: ACTION
Action: INPUT</pre>
                        </div>
                    `;
                } else {
                    throw new Error('Format invalide');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Test de parsing échoué</h3>
                        <pre>Erreur: ${error.message}</pre>
                    </div>
                `;
            }
        }

        function testValidateInput() {
            const resultDiv = document.getElementById('validate-result');
            
            try {
                // Simuler la validation
                const command = 'INPUT';
                const isValid = command === 'INPUT' && !command.includes('[') && !command.includes('(');
                
                if (isValid) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Test de validation réussi</h3>
                            <pre>Commande: "${command}"
Validation: Passée
- Pas de paramètres requis ✓
- Pas de coordonnées requises ✓
- Format simple valide ✓</pre>
                        </div>
                    `;
                } else {
                    throw new Error('Validation échouée');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Test de validation échoué</h3>
                        <pre>Erreur: ${error.message}</pre>
                    </div>
                `;
            }
        }

        function testSyntaxHighlighting() {
            const resultDiv = document.getElementById('syntax-result');
            
            try {
                // Simuler le test de coloration syntaxique
                const inputRegex = /^(CLEAR|FILL|FLOODFILL|SURROUND|REPLACE|EDIT|EXTRACT|RESIZE|SELECT|END|INIT|INPUT|MULTIPLY|DIVIDE)\b/;
                const command = 'INPUT';
                
                if (inputRegex.test(command)) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Test de coloration syntaxique réussi</h3>
                            <pre>Commande: "${command}"
Regex: ${inputRegex.source}
Match: ✓ INPUT est reconnu comme mot-clé</pre>
                        </div>
                    `;
                } else {
                    throw new Error('INPUT non reconnu par la regex');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Test de coloration syntaxique échoué</h3>
                        <pre>Erreur: ${error.message}</pre>
                    </div>
                `;
            }
        }

        // Exécuter tous les tests au chargement
        window.onload = function() {
            console.log('Tests de la commande INPUT chargés');
        };
    </script>
</body>
</html>
