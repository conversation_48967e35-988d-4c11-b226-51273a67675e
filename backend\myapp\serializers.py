from rest_framework import serializers
from ai.models import AIModel, PromptExecution, ARCTask, AutomationScenario, NotebookTemplate, UserNotebook
from myapp.models import CommandCategory, Command

class AIModelSerializer(serializers.ModelSerializer):
    """Sérialiseur pour les modèles d'IA"""

    class Meta:
        model = AIModel
        fields = ['id', 'name', 'description', 'type', 'provider']


class PromptExecutionSerializer(serializers.ModelSerializer):
    """Sérialiseur pour les exécutions de prompts"""

    commands = serializers.SerializerMethodField()

    class Meta:
        model = PromptExecution
        fields = ['id', 'output', 'commands', 'status', 'error']

    def get_commands(self, obj):
        """Convertit les commandes en liste"""
        if not obj.commands:
            return []
        return obj.commands.split('\n')


class PromptExecutionRequestSerializer(serializers.Serializer):
    """Sérialiseur pour les requêtes d'exécution de prompts"""

    prompt = serializers.CharField(required=True)
    taskId = serializers.CharField(required=True)
    modelId = serializers.CharField(required=True)
    contextData = serializers.JSONField(required=False)


class ARCTaskSerializer(serializers.ModelSerializer):
    """Sérialiseur pour les tâches ARC"""

    class Meta:
        model = ARCTask
        fields = ['task_id', 'subset', 'name', 'description', 'difficulty', 'created_at', 'updated_at']


class ARCTaskDetailSerializer(serializers.Serializer):
    """Sérialiseur pour les détails d'une tâche ARC"""

    id = serializers.CharField()
    train = serializers.ListField(child=serializers.JSONField())
    test = serializers.ListField(child=serializers.JSONField())


class AutomationScenarioSerializer(serializers.ModelSerializer):
    """Sérialiseur pour les scénarios d'automatisation"""

    commands_list = serializers.SerializerMethodField()

    class Meta:
        model = AutomationScenario
        fields = ['id', 'task', 'name', 'description', 'commands', 'commands_list', 'status', 'created_by_ai', 'created_at', 'updated_at']

    def get_commands_list(self, obj):
        """Convertit les commandes en liste"""
        if not obj.commands:
            return []
        return obj.commands.split('\n')


class CreateScenarioSerializer(serializers.Serializer):
    """Sérialiseur pour la création d'un scénario d'automatisation"""

    task_id = serializers.CharField(required=True)
    name = serializers.CharField(required=True)
    commands = serializers.CharField(required=True)
    description = serializers.CharField(required=False, default="")
    created_by_ai = serializers.BooleanField(required=False, default=False)


class TestCommandsSerializer(serializers.Serializer):
    """Sérialiseur pour tester des commandes d'automatisation"""

    task_id = serializers.CharField(required=True)
    commands = serializers.CharField(required=True)
    test_index = serializers.IntegerField(required=False, default=0)


class AuditLogSerializer(serializers.Serializer):
    """Sérialiseur pour les journaux d'audit"""

    id = serializers.UUIDField(read_only=True)
    user = serializers.SerializerMethodField()
    action_type = serializers.CharField(read_only=True)
    resource_type = serializers.CharField(read_only=True)
    resource_id = serializers.CharField(read_only=True)
    details = serializers.JSONField(read_only=True)
    ip_address = serializers.IPAddressField(read_only=True)
    user_agent = serializers.CharField(read_only=True)
    created_at = serializers.DateTimeField(read_only=True)

    def get_user(self, obj):
        """Récupère les informations de l'utilisateur"""
        if obj.user:
            return {
                'id': obj.user.id,
                'username': obj.user.username,
                'is_staff': obj.user.is_staff
            }
        return None


class NotebookTemplateSerializer(serializers.ModelSerializer):
    """Sérialiseur pour les templates de notebooks"""

    class Meta:
        model = NotebookTemplate
        fields = ['id', 'name', 'file_path', 'description', 'metadata', 'created_at', 'updated_at']


class UserNotebookSerializer(serializers.ModelSerializer):
    """Sérialiseur pour les notebooks utilisateur"""

    template_name = serializers.SerializerMethodField()
    task_id = serializers.SerializerMethodField()

    class Meta:
        model = UserNotebook
        fields = ['id', 'name', 'file_path', 'template', 'template_name', 'task', 'task_id', 'metadata', 'created_at', 'updated_at']

    def get_template_name(self, obj):
        """Récupère le nom du template"""
        if obj.template:
            return obj.template.name
        return None

    def get_task_id(self, obj):
        """Récupère l'ID de la tâche"""
        return obj.task.task_id


class CreateNotebookSerializer(serializers.Serializer):
    """Sérialiseur pour la création d'un notebook"""

    template_id = serializers.CharField(required=True)
    task_id = serializers.CharField(required=True)
    name = serializers.CharField(required=False)


class JupyterServerInfoSerializer(serializers.Serializer):
    """Sérialiseur pour les informations du serveur Jupyter"""

    url = serializers.URLField()
    token = serializers.CharField()
    notebook_dir = serializers.CharField()
    port = serializers.IntegerField(required=False)
    pid = serializers.IntegerField(required=False)
    status = serializers.CharField(required=False, default="running")


class CommandCategorySerializer(serializers.ModelSerializer):
    """Sérialiseur pour les catégories de commandes"""

    class Meta:
        model = CommandCategory
        fields = ['id', 'name', 'description', 'order']


class CommandSerializer(serializers.ModelSerializer):
    """Sérialiseur pour les commandes"""

    category = CommandCategorySerializer(read_only=True)

    class Meta:
        model = Command
        fields = [
            'id', 'name', 'description', 'syntax', 'example',
            'category', 'is_frontend_only', 'is_backend_only',
            'is_active', 'order'
        ]