#!/usr/bin/env python3
"""
Test avec les vraies données de la tâche 4522001f
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from command_system.command_executor import CommandExecutor
import numpy as np

def test_with_real_task_data():
    """Test avec les vraies données de la tâche 4522001f"""
    
    print("🧪 Test avec les vraies données de la tâche 4522001f...")
    
    # Vraies données de la tâche
    task_data = {
        'test': [{
            'input': [[0, 3, 3], [0, 2, 3], [0, 0, 0]],  # VRAIE grille d'entrée
            'output': [
                [0, 0, 0, 0, 0, 3, 3, 3, 3],
                [0, 0, 0, 0, 0, 3, 3, 3, 3],
                [0, 0, 0, 0, 0, 3, 3, 3, 3],
                [0, 0, 0, 0, 0, 3, 3, 3, 3],
                [0, 3, 3, 3, 3, 0, 0, 0, 0],
                [0, 3, 3, 3, 3, 0, 0, 0, 0],
                [0, 3, 3, 3, 3, 0, 0, 0, 0],
                [0, 3, 3, 3, 3, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0, 0, 0, 0]
            ]
        }]
    }
    
    # Commandes du scénario (simplifiées)
    commands = [
        'INPUT',
        'EDIT 0 [0,1]',
        'EDIT 0 [1,2]', 
        'EDIT 3 [1,1]',
        'RESIZE 9x9',
        'CUT [0,1 1,2]',
        'MULTIPLY 4 false',
        'PASTE [0,1 1,2]',
        'END'
    ]
    
    executor = CommandExecutor(task_data, 0)
    
    print("📋 Exécution étape par étape avec vraies données...")
    
    for i, command in enumerate(commands):
        print(f"\n--- Étape {i+1}: {command} ---")
        
        success = executor._execute_command(command)
        print(f"Succès: {success}")
        
        if executor.error:
            print(f"Erreur: {executor.error}")
            break
            
        if executor.grid is not None:
            print(f"Grille actuelle ({executor.grid.shape}):")
            print(executor.grid)
            
        if command.startswith('CUT'):
            print("📋 Contenu du clipboard après CUT:")
            if executor.clipboard is not None:
                print(f"Clipboard shape: {executor.clipboard.shape}")
                print(executor.clipboard)
            else:
                print("Clipboard vide!")
                
        elif command.startswith('MULTIPLY'):
            print("📋 Contenu du clipboard après MULTIPLY:")
            if executor.clipboard is not None:
                print(f"Clipboard shape: {executor.clipboard.shape}")
                print(executor.clipboard)
            else:
                print("Clipboard vide!")
    
    # Comparer avec la sortie attendue
    if executor.grid is not None:
        expected = np.array(task_data['test'][0]['output'])
        generated = executor.grid
        
        print(f"\n📊 Comparaison finale:")
        print(f"Grille générée shape: {generated.shape}")
        print(f"Grille attendue shape: {expected.shape}")
        
        if generated.shape == expected.shape:
            matches = np.sum(generated == expected)
            total = generated.size
            percentage = (matches / total) * 100
            
            print(f"Cellules correspondantes: {matches}/{total} ({percentage:.1f}%)")
            
            if matches == total:
                print("✅ SUCCÈS COMPLET !")
            else:
                print("\n❌ Différences détectées:")
                diff_positions = np.where(generated != expected)
                for i in range(min(10, len(diff_positions[0]))):
                    row, col = diff_positions[0][i], diff_positions[1][i]
                    print(f"   Position [{row},{col}]: généré={generated[row,col]}, attendu={expected[row,col]}")
        else:
            print("❌ Les dimensions ne correspondent pas!")
    
    return executor

def analyze_input_transformation():
    """Analyser la transformation de l'entrée"""
    
    print("\n" + "="*60)
    print("🔍 Analyse de la transformation de l'entrée...")
    
    # Grille d'entrée réelle
    input_grid = np.array([[0, 3, 3], [0, 2, 3], [0, 0, 0]])
    print("📋 Grille d'entrée réelle:")
    print(input_grid)
    
    # Après les EDIT
    after_edits = input_grid.copy()
    after_edits[0, 1] = 0  # EDIT 0 [0,1]
    after_edits[1, 2] = 0  # EDIT 0 [1,2]
    after_edits[1, 1] = 3  # EDIT 3 [1,1]
    
    print("\n📋 Grille après EDIT:")
    print(after_edits)
    
    # Zone coupée [0,1 1,2]
    cut_region = after_edits[0:2, 1:3]
    print("\n📋 Zone coupée [0,1 1,2]:")
    print(cut_region)
    
    # Analyser ce qui devrait se passer avec MULTIPLY 4 false
    print("\n🔍 Analyse de MULTIPLY 4 false sur cette zone:")
    print("Chaque cellule devient un bloc 4x4:")
    print(f"  [0,0] = {cut_region[0,0]} → bloc 4x4 de {cut_region[0,0]}")
    print(f"  [0,1] = {cut_region[0,1]} → bloc 4x4 de {cut_region[0,1]}")
    print(f"  [1,0] = {cut_region[1,0]} → bloc 4x4 de {cut_region[1,0]}")
    print(f"  [1,1] = {cut_region[1,1]} → bloc 4x4 de {cut_region[1,1]}")

if __name__ == "__main__":
    analyze_input_transformation()
    executor = test_with_real_task_data()