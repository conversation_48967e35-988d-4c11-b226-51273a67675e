{"version": "3.2.0", "results": [[":test-transformations-motifs-selections.test.ts", {"duration": 10.147899999999936, "failed": false}], [":src/components/resolution/hooks/__tests__/useScenarioSync.test.ts", {"duration": 36.75769999999966, "failed": true}], [":src/lib/__tests__/colorDetectionUtils.test.ts", {"duration": 20.638800000000174, "failed": false}], [":src/components/resolution/Dialogs/__tests__/ColorSelectionDialog.test.tsx", {"duration": 556.7616000000003, "failed": true}], [":src/components/resolution/__tests__/synchronized-history.test.ts", {"duration": 8.317000000000007, "failed": true}], [":src/services/__tests__/ErrorService.test.ts", {"duration": 19.27290000000039, "failed": false}], [":src/components/resolution/hooks/useSelection.test.ts", {"duration": 59.354899999999816, "failed": false}], [":src/components/common/GridEdit.test.tsx", {"duration": 63.58130000000028, "failed": false}], [":src/components/resolution/__tests__/undo-redo-fix.test.ts", {"duration": 0, "failed": true}], [":src/components/resolution/__tests__/unified-undo-redo.test.ts", {"duration": 23.040399999999863, "failed": false}], [":test-correction-paste-duplication.test.ts", {"duration": 6.660799999999881, "failed": false}], [":test-correction-flip-commands.test.ts", {"duration": 4.144600000000082, "failed": false}], [":test-correction-flip-rerendu.test.ts", {"duration": 3.5757999999998447, "failed": false}], [":test-correction-rotate-commands.test.ts", {"duration": 4.490500000000111, "failed": false}], [":test-correction-grid-version.test.ts", {"duration": 5.607600000000048, "failed": false}], [":test-correction-paste-clear-selection.test.ts", {"duration": 4.130900000000111, "failed": false}], [":test-correction-copy-cut-clear-selection.test.ts", {"duration": 6.5797999999999774, "failed": false}], [":test-debug-transform-toolbar.test.ts", {"duration": 4.4809999999999945, "failed": false}], [":test-real-problem-transform-disabled.test.ts", {"duration": 5.4846, "failed": false}], [":test-real-problem-react-hooks.test.ts", {"duration": 6.196100000000001, "failed": false}], [":test-correction-transform-works-after-copy.test.ts", {"duration": 5.36850000000004, "failed": false}], [":test_correction_synchronisation.test.ts", {"duration": 74.51299999999992, "failed": false}], [":src/components/resolution/utils/__tests__/transformationCoordinator.test.ts", {"duration": 50.08179999999993, "failed": false}], [":src/components/resolution/utils/__tests__/transformationIntegration.test.ts", {"duration": 0, "failed": true}], [":src/components/resolution/ResolutionGrid/__tests__/ResolutionGrid.reactivity.test.ts", {"duration": 3.4104999999999563, "failed": false}], [":src/components/resolution/utils/__tests__/transformationNegativeTests.test.ts", {"duration": 35.828199999999924, "failed": false}], [":src/components/resolution/utils/__tests__/testAnalysis.test.ts", {"duration": 35.95060000000012, "failed": false}], [":src/components/resolution/utils/__tests__/realWorldTest.test.ts", {"duration": 144.84570000000008, "failed": true}], [":src/components/resolution/utils/__tests__/matrixTransformation.test.ts", {"duration": 5.020500000000084, "failed": false}], [":src/components/resolution/utils/__tests__/celluleNonSelectionneeProbleme.test.ts", {"duration": 41.639900000000125, "failed": true}], [":test_correction_filtrage_excludesolved.test.ts", {"duration": 18.6875, "failed": false}], [":test_probleme_reel_excludesolved.test.ts", {"duration": 26.52509999999984, "failed": false}], [":test_solution_finale_excludesolved.test.ts", {"duration": 30.098199999999906, "failed": false}], [":test-unified-commands.test.ts", {"duration": 3.685600000000022, "failed": false}], [":src/services/__tests__/unifiedCommandExecutor.test.ts", {"duration": 45.92959999999994, "failed": true}], [":src/services/__tests__/unifiedCommandParser.test.ts", {"duration": 22.077099999999973, "failed": true}], [":src/services/__tests__/unifiedExecutorIntegration.test.ts", {"duration": 20.484399999999823, "failed": false}], [":src/hooks/useUnifiedSelection.test.ts", {"duration": 44.92540000000008, "failed": true}], [":src/components/resolution/hooks/__tests__/useAutomationStore.optimizations.test.ts", {"duration": 15.375900000000001, "failed": true}], [":src/components/unified/UnifiedGridComponent.test.tsx", {"duration": 55.22420000000011, "failed": true}], [":src/components/unified/ActionButton.test.tsx", {"duration": 168.9004, "failed": false}], [":src/components/unified/ParameterDialog.test.tsx", {"duration": 409.42959999999994, "failed": true}], [":src/components/unified/UnifiedScenarioEditor.test.tsx", {"duration": 189.86149999999998, "failed": false}], [":src/components/unified/ScenarioHistory.test.tsx", {"duration": 225.8809000000001, "failed": true}], [":src/services/__tests__/intelligentReplay.test.ts", {"duration": 0, "failed": true}], [":src/services/__tests__/commandOptimizer.test.ts", {"duration": 0, "failed": true}], [":src/components/resolution/utils/__tests__/unifiedCommandUtils.test.ts", {"duration": 14.609699999999975, "failed": true}], [":src/components/resolution/hooks/__tests__/useUnifiedHistory.test.ts", {"duration": 52.88889999999992, "failed": true}], [":src/components/resolution/utils/__tests__/unifiedSnapshotManager.test.ts", {"duration": 35.300500000000284, "failed": false}], [":src/components/resolution/utils/__tests__/integration.test.ts", {"duration": 19.159999999999854, "failed": false}], [":src/components/resolution/utils/__tests__/performance.test.ts", {"duration": 217.5317, "failed": false}], [":src/components/resolution/hooks/__tests__/useUnifiedPlayback.test.ts", {"duration": 3554.3576999999996, "failed": false}], [":src/components/resolution/automation/__tests__/PlaybackControls.test.tsx", {"duration": 33.63370000000009, "failed": true}], [":src/__tests__/unifiedCommands.test.ts", {"duration": 1544.6129999999998, "failed": false}], [":src/__tests__/performance.test.ts", {"duration": 168.1169, "failed": false}], [":src/__tests__/integration/unifiedWorkflow.test.tsx", {"duration": 273.3056999999999, "failed": false}], [":src/components/resolution/utils/__tests__/integratedSelectInvert.test.ts", {"duration": 0, "failed": true}], [":src/components/resolution/utils/commandTransitionUtils.test.ts", {"duration": 0, "failed": true}], [":src/components/resolution/utils/__tests__/commandGenerationUtils.test.ts", {"duration": 29.111699999999928, "failed": true}], [":src/components/resolution/hooks/__tests__/useSelection.test.ts", {"duration": 60.794500000000426, "failed": false}], [":src/services/__tests__/clipboardManager.test.ts", {"duration": 32.41249999999991, "failed": true}], [":src/services/__tests__/transformationEngine.test.ts", {"duration": 56.05580000000009, "failed": true}], [":src/services/__tests__/transformationValidator.test.ts", {"duration": 21.214600000000246, "failed": false}], [":src/services/__tests__/transformationValidationComplete.test.ts", {"duration": 97.01579999999967, "failed": true}], [":src/services/__tests__/transformationValidationBasic.test.ts", {"duration": 13.479899999999816, "failed": false}], [":src/services/__tests__/migrationStrategy.test.ts", {"duration": 12.338600000000042, "failed": false}], [":src/services/__tests__/backwardCompatibilityHandler.test.ts", {"duration": 20.27579999999989, "failed": false}], [":src/services/__tests__/unifiedCommandGenerator.test.ts", {"duration": 10.392100000000028, "failed": false}], [":src/services/__tests__/commandPlayer.test.ts", {"duration": 18.483099999999922, "failed": false}], [":src/services/__tests__/commandPlayerIntegration.test.ts", {"duration": 15.653099999999995, "failed": false}], [":src/services/__tests__/frontendTransformationValidator.test.ts", {"duration": 0, "failed": true}], [":src/components/unified/__tests__/TransformationCommandInput.test.tsx", {"duration": 0, "failed": true}], [":src/components/resolution/utils/__tests__/unifiedCoordinateParsing.test.ts", {"duration": 8.45620000000008, "failed": false}], [":tests/test-transformations-motifs-selections.test.ts", {"duration": 8.633100000000013, "failed": false}], [":tests/test_correction_synchronisation.test.ts", {"duration": 0, "failed": true}], [":tests/test_solution_finale_excludesolved.test.ts", {"duration": 0, "failed": true}], [":tests/test_probleme_reel_excludesolved.test.ts", {"duration": 0, "failed": true}], [":tests/test_correction_filtrage_excludesolved.test.ts", {"duration": 0, "failed": true}]]}