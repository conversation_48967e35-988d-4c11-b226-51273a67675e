#!/usr/bin/env python3
"""
Test de décompression des commandes EDITS avec coordonnées multiples.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from ai.command_decompressor import CommandDecompressor

def test_edits_decompression():
    """Test la décompression des commandes EDITS avec différents formats."""
    
    decompressor = CommandDecompressor()
    
    # Test cases
    test_cases = [
        {
            'name': 'EDITS simple',
            'input': ['EDITS {EDIT 7 [0,0]; EDIT 8 [0,1]}'],
            'expected': ['EDIT 7 [0,0]', 'EDIT 8 [0,1]']
        },
        {
            'name': 'EDITS avec coordonnées multiples',
            'input': ['EDITS {EDIT 0 ([0,1] [1,2]); EDIT 3 [1,1]}'],
            'expected': ['EDIT 0 [0,1]', 'EDIT 0 [1,2]', 'EDIT 3 [1,1]']
        },
        {
            'name': 'EDITS mixte',
            'input': ['EDITS {EDIT 5 [2,2]; EDIT 1 ([3,3] [4,4] [5,5])}'],
            'expected': ['EDIT 5 [2,2]', 'EDIT 1 [3,3]', 'EDIT 1 [4,4]', 'EDIT 1 [5,5]']
        }
    ]
    
    print("🧪 Test de décompression des commandes EDITS")
    print("=" * 60)
    
    all_passed = True
    
    for test_case in test_cases:
        print(f"\n📋 Test: {test_case['name']}")
        print(f"📤 Input: {test_case['input']}")
        
        try:
            result = decompressor.decompress_commands(test_case['input'])
            print(f"📥 Output: {result}")
            print(f"🎯 Expected: {test_case['expected']}")
            
            if result == test_case['expected']:
                print("✅ PASS")
            else:
                print("❌ FAIL")
                all_passed = False
                
        except Exception as e:
            print(f"💥 ERROR: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 Tous les tests sont passés !")
    else:
        print("❌ Certains tests ont échoué")
    
    return all_passed

if __name__ == "__main__":
    test_edits_decompression()