#!/usr/bin/env python3
"""
Script pour nettoyer useAutomation.ts des fonctions dupliquées et code obsolète
"""

import re

# Li<PERSON> le fichier
with open('frontend/src/components/resolution/hooks/useAutomation.ts', 'r', encoding='utf-8') as f:
    content = f.read()

# Supprimer les imports obsolètes
content = re.sub(r'import.*useProposalContext.*\n', '', content)

# Supprimer les références à coordinates et additionalCoordinates dans executeUnifiedCommand
content = re.sub(r'const \{ action, parameters, coordinates \} = command;', 
                'const { action, parameters, coordinateBlocks } = command;', content)

# Supprimer les appels à executeUnifiedFill obsolète
content = re.sub(r'return await executeUnifiedFill\(parameters, coordinates, \[\], grid, setGrid\);',
                'return await executeWithCoordinateBlocks(action, parameters, coordinateBlocks, grid, setGrid, command.raw);', content)

# Supprimer les fonctions dupliquées (garder seulement les versions les plus récentes)
# Supprimer la première occurrence de processCoordinateBlocks (version synchrone)
pattern1 = r'function processCoordinateBlocks\([^{]*\): void \{[^}]*(?:\{[^}]*\}[^}]*)*\}'
content = re.sub(pattern1, '// SUPPRIMÉ - processCoordinateBlocks dupliqué', content, count=1, flags=re.DOTALL)

# Supprimer la première occurrence de executeUnifiedEnd
pattern2 = r'async function executeUnifiedEnd\(\): Promise<boolean> \{[^}]*(?:\{[^}]*\}[^}]*)*\}'
content = re.sub(pattern2, '// SUPPRIMÉ - executeUnifiedEnd dupliqué', content, count=1, flags=re.DOTALL)

# Ajouter l'import manquant pour useProposalContext
if 'useProposalContext' in content and 'import.*useProposalContext' not in content:
    # Trouver la ligne d'import et ajouter useProposalContext
    content = re.sub(r'(import.*useGridStateStore.*\n)', 
                    r'\1import { useProposalContext } from \'./useProposalContext\';\n', content)

print("✅ Nettoyage terminé!")

# Écrire le fichier nettoyé
with open('frontend/src/components/resolution/hooks/useAutomation.ts', 'w', encoding='utf-8') as f:
    f.write(content)