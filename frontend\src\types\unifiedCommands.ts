// Types pour les sélections unifiées
export interface SelectionRectangle {
  startRow: number; // ligne (y)
  startCol: number; // colonne (x)
  endRow: number;
  endCol: number;
  isAdditional: boolean; // true pour les sélections avec +
}

export interface UnifiedSelection {
  rectangles: SelectionRectangle[];
  mode: 'replace' | 'add' | 'subtract';
  isInverted?: boolean;
  colorFilter?: number[];
  specialOperator?: SpecialOperator;
}

// Types pour les opérateurs spéciaux
export type SpecialOperatorType = 'INVERT' | 'COLOR' | 'XOR' | 'AND' | 'OR';

export interface SpecialOperator {
  type: SpecialOperatorType;
  operands?: UnifiedSelection[]; // Pour XOR, AND, OR
  colors?: number[]; // Pour COLOR
}

// Types pour les commandes
export type CommandType = 'ACTION' | 'TRANSFORMATION' | 'SELECTION';

export type ActionSubtype =
  | 'CLEAR' | 'FILL' | 'SURROUND' | 'REPLACE'
  | 'INSERT' | 'DELETE' | 'FLIP' | 'ROTATE'
  | 'INVERSE' | 'COLOR';

export interface CommandParameters {
  color?: number;
  colors?: number[];
  sourceColors?: number[];
  targetColor?: number;
  // direction?: 'HORIZONTAL' | 'VERTICAL' | 'LEFT' | 'RIGHT' | 'before' | 'after' | 'left' | 'right';
  direction?: 'BEFORE' | 'AFTER' | 'ABOVE' | 'BELOW';
  count?: number;
  type?: 'ROWS' | 'COLUMNS';
  [key: string]: any;
}

export interface ParsedCommand {
  type: CommandType;
  action: ActionSubtype;
  selections: UnifiedSelection;
  parameters: CommandParameters;
  rawCommand: string;
}

// Types pour les résultats d'exécution
export interface ExecutionResult {
  success: boolean;
  newGrid?: Grid;
  modifiedCells?: Array<{row: number, col: number, oldValue: number, newValue: number}>;
  error?: string;
  warnings?: string[];
}

// Types pour la validation
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  code: string;
  message: string;
  line?: number;
  position?: {row: number, col: number};
}

export interface ValidationWarning {
  code: string;
  message: string;
  suggestion?: string;
}

// Types pour les métadonnées de commandes
export interface UnifiedCommandDefinition {
  id: number;
  name: string;
  commandType: CommandType;
  actionSubtype?: ActionSubtype;
  requiresColor: boolean;
  requiresDirection: boolean;
  requiresMultipleColors: boolean;
  requiresCount: boolean;
  syntaxTemplate: string;
  description: string;
  example: string;
  validationRules: Record<string, any>;  isActive: boolean;
}

// Import de la classe Grid existante
import { Grid } from '../lib/grid';
