# Guide d'utilisation du module Error Message Formatter

Le module `error_message_formatter` fournit des outils pour créer des messages d'erreur cohérents et informatifs dans tous les scripts de validation des scénarios ARC.

## Installation

Le module se trouve dans `backend/utils/error_message_formatter.py` et peut être importé dans n'importe quel script Python du projet.

```python
from utils.error_message_formatter import (
    ErrorMessageFormatter,
    ValidationErrorContext,
    format_validation_error,
    create_error_context
)
```

## Utilisation

### 1. Utilisation directe avec ErrorMessageFormatter

La classe `ErrorMessageFormatter` fournit des méthodes statiques pour formater différents types d'erreurs :

```python
# Erreur d'exécution de commandes
error_msg = ErrorMessageFormatter.format_execution_error(
    "Commande inconnue: INVALID_CMD",
    ["INPUT", "RESIZE 9x9", "INVALID_CMD"],  # historique
    5  # nombre total de commandes
)
# Résultat: "Échec à l'étape 3/5: Commande inconnue: INVALID_CMD. Dernière commande exécutée: 'INVALID_CMD'"

# Erreur de comparaison de grilles
error_msg = ErrorMessageFormatter.format_grid_comparison_error(
    [[1, 1], [1, 1]],  # grille générée
    [[7, 0, 7], [7, 0, 7], [7, 7, 0]],  # grille attendue
    2,  # cellules correspondantes
    9   # total cellules attendues
)
# Résultat: "Dimensions incorrectes: grille générée 2x2, attendue 3x3. Correspondance partielle: 2/9 cellules (22.2%)"

# Autres méthodes disponibles
ErrorMessageFormatter.format_empty_scenario_error()
ErrorMessageFormatter.format_task_loading_error(task_id, subset)
ErrorMessageFormatter.format_invalid_test_index_error(test_index, task_id, max_index)
ErrorMessageFormatter.format_missing_output_error(test_index, task_id)
ErrorMessageFormatter.format_no_grid_generated_error(total_commands)
ErrorMessageFormatter.format_internal_error(task_id, test_index, error)
```

### 2. Utilisation avec ValidationErrorContext

La classe `ValidationErrorContext` encapsule le contexte d'une validation (task_id, test_index, subset) et simplifie la création de messages d'erreur :

```python
# Créer un contexte
context = ValidationErrorContext("007bbfb7", 0, "training")

# Formater différents types d'erreurs
error_msg = context.format_error("task_loading")
error_msg = context.format_error("invalid_test_index", max_index=2)
error_msg = context.format_error("execution", 
                                error_msg="Syntaxe invalide",
                                history=["INPUT", "RESIZE 9x9"],
                                total_commands=4)
```

### 3. Fonctions utilitaires

Pour une utilisation rapide, des fonctions utilitaires sont disponibles :

```python
# Fonction utilitaire pour formater rapidement une erreur
error_msg = format_validation_error(
    "execution",  # type d'erreur
    "007bbfb7",   # task_id
    0,            # test_index
    error_msg="Syntaxe invalide",
    history=["INPUT", "RESIZE 9x9"],
    total_commands=4
)

# Créer un contexte réutilisable
context = create_error_context("007bbfb7", 0, "training")
```

## Types d'erreurs supportés

### Erreurs de validation de base

| Type | Description | Paramètres requis |
|------|-------------|-------------------|
| `execution` | Erreur d'exécution de commandes | `error_msg`, `history`, `total_commands` |
| `grid_comparison` | Grille générée différente de l'attendue | `generated_grid`, `expected_grid`, `matching_cells`, `total_cells` |
| `task_loading` | Impossible de charger les données de tâche | Aucun (utilise le contexte) |
| `invalid_test_index` | Index de test invalide | `max_index` |
| `missing_output` | Sortie attendue manquante | Aucun (utilise le contexte) |
| `no_grid_generated` | Aucune grille générée | `total_commands` |
| `internal` | Erreur interne | `error` |

### Erreurs de parsing et décompression

```python
# Erreur de parsing de commande
error_msg = ErrorMessageFormatter.format_command_parsing_error(
    "INVALID_COMMAND", 
    3  # numéro de ligne
)

# Erreur de décompression
error_msg = ErrorMessageFormatter.format_decompression_error(
    "Bloc MOTIF mal formé", 
    5  # nombre de commandes
)
```

### Résumés de validation

```python
# Résumé de validation
summary = ErrorMessageFormatter.format_validation_summary(
    total_scenarios=10,
    successful=7,
    failed=3,
    success_rate=70.0
)
```

## Intégration dans un script existant

### Exemple d'intégration dans un service de validation

```python
from utils.error_message_formatter import ValidationErrorContext, ErrorMessageFormatter

class MyValidationService:
    def validate_scenario(self, task_id, test_index, content):
        context = ValidationErrorContext(task_id, test_index)
        
        try:
            # ... logique de validation ...
            
            if not commands:
                return {
                    'success': False,
                    'error': ErrorMessageFormatter.format_empty_scenario_error()
                }
            
            if execution_failed:
                return {
                    'success': False,
                    'error': context.format_error('execution',
                                                error_msg=execution_error,
                                                history=command_history,
                                                total_commands=len(commands))
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': context.format_error('internal', error=str(e))
            }
```

### Exemple d'intégration dans un script de validation batch

```python
from utils.error_message_formatter import create_error_context, ErrorMessageFormatter

def validate_multiple_scenarios(scenarios):
    successful = 0
    failed = 0
    
    for scenario in scenarios:
        context = create_error_context(scenario['task_id'], scenario['test_index'])
        
        try:
            result = validate_single_scenario(scenario)
            if result['success']:
                successful += 1
            else:
                failed += 1
                print(f"❌ {context.scenario_name} - {result['error']}")
        except Exception as e:
            failed += 1
            error_msg = context.format_error('internal', error=str(e))
            print(f"❌ {context.scenario_name} - {error_msg}")
    
    # Résumé final
    total = len(scenarios)
    success_rate = (successful / total * 100) if total > 0 else 0
    summary = ErrorMessageFormatter.format_validation_summary(
        total, successful, failed, success_rate
    )
    print(summary)
```

## Avantages

1. **Cohérence** : Tous les messages d'erreur suivent le même format
2. **Réutilisabilité** : Le module peut être utilisé dans n'importe quel script
3. **Maintenabilité** : Les messages sont centralisés et faciles à modifier
4. **Informatif** : Les messages contiennent des détails utiles pour le diagnostic
5. **Extensibilité** : Facile d'ajouter de nouveaux types d'erreurs

## Exemples complets

Voir le fichier `scripts/validation_with_improved_errors.py` pour des exemples complets d'utilisation du module dans différents contextes.