{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Analyse et Résolution de Tâche ARC\n", "\n", "Ce notebook vous permet d'analyser et de résoudre une tâche ARC en utilisant le module `arc_utils`. Il est conçu pour fonctionner avec la tâche actuellement chargée dans l'application ARC Puzzle."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Configuration et Importation des Modules\n", "\n", "Commençons par importer les modules nécessaires et configurer l'environnement."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importer le module arc_utils et les bibliothèques nécessaires", "import arc_utils", "import numpy as np", "import matplotlib.pyplot as plt", "import json", "", "# Vérifier que le module est correctement chargé", "print(\"Module arc_utils chargé avec succès!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonctions d'analyse avancées\n", "\nCes fonctions permettent d'analyser en détail les grilles et les transformations."]}, {"cell_type": "code", "metadata": {}, "source": ["# Fonctions d'analyse avancées", "", "def analyze_patterns(grid):", "    \"\"\"Analyse les patterns dans une grille\"\"\"", "    if isinstance(grid, list):", "        grid = np.array(grid)", "", "    # Dimensions de la grille", "    height, width = grid.shape", "    print(f\"Dimensions: {height}x{width}\")", "", "    # Valeurs uniques et leur fréquence", "    values, counts = np.unique(grid, return_counts=True)", "    print(\"\\nValeurs uniques et leur fréquence:\")", "    for val, count in zip(values, counts):", "        print(f\"  Valeur {val}: {count} occurrences ({count/(height*width)*100:.1f}%)\")", "", "    # Recherche de symétries", "    print(\"\\nAnalyse des symétries:\")", "", "    # Symétrie horizontale", "    is_h_symmetric = np.array_equal(grid, np.flipud(grid))", "    print(f\"  Symétrie horizontale: {'Oui' if is_h_symmetric else 'Non'}\")", "", "    # Symétrie verticale", "    is_v_symmetric = np.array_equal(grid, np.fliplr(grid))", "    print(f\"  Symétrie verticale: {'Oui' if is_v_symmetric else 'Non'}\")", "", "    # <PERSON><PERSON><PERSON><PERSON><PERSON> diagonale (si la grille est carrée)", "    if height == width:", "        is_diag_symmetric = np.array_equal(grid, grid.T)", "        print(f\"  Symétrie diagonale: {'Oui' if is_diag_symmetric else 'Non'}\")", "", "    # Recherche de motifs répétitifs", "    print(\"\\nRecherche de motifs répétitifs:\")", "", "    # Motifs 2x2", "    if height >= 2 and width >= 2:", "        patterns_2x2 = {}", "        for y in range(height-1):", "            for x in range(width-1):", "                pattern = tuple(grid[y:y+2, x:x+2].flatten())", "                patterns_2x2[pattern] = patterns_2x2.get(pattern, 0) + 1", "", "        # Afficher les motifs les plus fréquents", "        if patterns_2x2:", "            most_common = sorted(patterns_2x2.items(), key=lambda x: x[1], reverse=True)[:3]", "            print(\"  Motifs 2x2 les plus fréquents:\")", "            for pattern, count in most_common:", "                print(f\"    {pattern}: {count} occurrences\")", "", "    return {", "        \"dimensions\": (height, width),", "        \"unique_values\": list(zip(values.tolist(), counts.tolist())),", "        \"symmetries\": {", "            \"horizontal\": is_h_symmetric,", "            \"vertical\": is_v_symmetric,", "            \"diagonal\": is_diag_symmetric if height == width else None", "        }", "    }", "", "def analyze_transformations(input_grid, output_grid):", "    \"\"\"Analyse les transformations entre deux grilles\"\"\"", "    if isinstance(input_grid, list):", "        input_grid = np.array(input_grid)", "    if isinstance(output_grid, list):", "        output_grid = np.array(output_grid)", "", "    # Vérifier si les dimensions sont les mêmes", "    if input_grid.shape != output_grid.shape:", "        print(f\"Les dimensions sont différentes: {input_grid.shape} -> {output_grid.shape}\")", "        return None", "", "    height, width = input_grid.shape", "", "    # Compter les cellules modifiées", "    diff = (input_grid != output_grid)", "    num_diff = np.sum(diff)", "    print(f\"Nombre de cellules modifiées: {num_diff} sur {input_grid.size} ({num_diff/input_grid.size*100:.1f}%)\")", "", "    # Analyser les valeurs", "    input_values = np.unique(input_grid)", "    output_values = np.unique(output_grid)", "    print(f\"Valeurs dans l'entrée: {input_values}\")", "    print(f\"Valeurs dans la sortie: {output_values}\")", "", "    # Analyser les transformations par valeur", "    print(\"\\nTransformations par valeur:\")", "    transformations = {}", "    for val in input_values:", "        mask = (input_grid == val)", "        if np.sum(mask) > 0:", "            output_vals, counts = np.unique(output_grid[mask], return_counts=True)", "            transformations[int(val)] = {int(o_val): int(count) for o_val, count in zip(output_vals, counts)}", "", "            print(f\"  Valeur {val} -> \", end=\"\")", "            for o_val, count in zip(output_vals, counts):", "                print(f\"{o_val}: {count} ({count/np.sum(mask)*100:.1f}%), \", end=\"\")", "            print()", "", "    # Vérifier les transformations courantes", "    print(\"\\nVérification des transformations courantes:\")", "", "    # Rotation de 90°", "    rot90 = np.rot90(input_grid)", "    is_rot90 = np.array_equal(rot90, output_grid)", "    print(f\"  Rotation 90°: {'Oui' if is_rot90 else 'Non'}\")", "", "    # Rotation de 180°", "    rot180 = np.rot90(input_grid, 2)", "    is_rot180 = np.array_equal(rot180, output_grid)", "    print(f\"  Rotation 180°: {'Oui' if is_rot180 else 'Non'}\")", "", "    # Rotation de 270°", "    rot270 = np.rot90(input_grid, 3)", "    is_rot270 = np.array_equal(rot270, output_grid)", "    print(f\"  Rotation 270°: {'Oui' if is_rot270 else 'Non'}\")", "", "    # Miroir horizontal", "    flip_h = np.flipud(input_grid)", "    is_flip_h = np.array_equal(flip_h, output_grid)", "    print(f\"  Miroir horizontal: {'Oui' if is_flip_h else 'Non'}\")", "", "    # Miroir vertical", "    flip_v = np.fliplr(input_grid)", "    is_flip_v = np.array_equal(flip_v, output_grid)", "    print(f\"  Miroir vertical: {'Oui' if is_flip_v else 'Non'}\")", "", "    # Transposition (si la grille est carrée)", "    if height == width:", "        transpose = input_grid.T", "        is_transpose = np.array_equal(transpose, output_grid)", "        print(f\"  Transposition: {'<PERSON><PERSON>' if is_transpose else 'Non'}\")", "", "    # Afficher une grille des différences", "    print(\"\\nGrille des différences (cellules modifiées):\")", "    diff_grid = np.zeros_like(input_grid)", "    diff_grid[diff] = output_grid[diff]", "    display_grid(diff_grid, \"Différences\")", "", "    return {", "        \"num_diff\": int(num_diff),", "        \"diff_percentage\": float(num_diff/input_grid.size*100),", "        \"transformations\": transformations,", "        \"common_transformations\": {", "            \"rotation_90\": is_rot90,", "            \"rotation_180\": is_rot180,", "            \"rotation_270\": is_rot270,", "            \"flip_horizontal\": is_flip_h,", "            \"flip_vertical\": is_flip_v,", "            \"transpose\": is_transpose if height == width else None", "        }", "    }", "", "def detect_objects(grid, background=0):", "    \"\"\"Détecte les objets dans une grille\"\"\"", "    if isinstance(grid, list):", "        grid = np.array(grid)", "", "    height, width = grid.shape", "", "    # C<PERSON>er une grille de labels pour les objets", "    labels = np.zeros_like(grid)", "    next_label = 1", "", "    # Fonction pour étiqueter un objet de manière récursive", "    def label_object(y, x, label):", "        if y < 0 or y >= height or x < 0 or x >= width:", "            return 0  # Hors limites", "", "        if grid[y, x] == background or labels[y, x] != 0:", "            return 0  # <PERSON><PERSON> ou d<PERSON><PERSON><PERSON>", "", "        # Étiqueter la cellule", "        labels[y, x] = label", "        size = 1", "", "        # Éti<PERSON>er les voisins (4-connexité)", "        size += label_object(y-1, x, label)  # Haut", "        size += label_object(y+1, x, label)  # Bas", "        size += label_object(y, x-1, label)  # Gauche", "        size += label_object(y, x+1, label)  # Droite", "", "        return size", "", "    # Parcourir la grille et étiqueter les objets", "    objects = {}", "    for y in range(height):", "        for x in range(width):", "            if grid[y, x] != background and labels[y, x] == 0:", "                size = label_object(y, x, next_label)", "                objects[next_label] = {", "                    \"value\": int(grid[y, x]),", "                    \"size\": size,", "                    \"label\": next_label", "                }", "                next_label += 1", "", "    print(f\"Nombre d'objets détectés: {len(objects)}\")", "", "    # Afficher les informations sur les objets", "    if objects:", "        print(\"\\nInformations sur les objets:\")", "        for label, obj in objects.items():", "            print(f\"  Objet {label}: valeur {obj['value']}, taille {obj['size']}\")", "", "    # C<PERSON>er une grille colorée pour visualiser les objets", "    if objects:", "        print(\"\\nVisualisation des objets:\")", "        # Utiliser une palette de couleurs différente pour chaque objet", "        object_grid = np.zeros_like(grid)", "        for y in range(height):", "            for x in range(width):", "                if labels[y, x] > 0:", "                    object_grid[y, x] = labels[y, x]", "", "        display_grid(object_grid, \"Objets détectés\")", "", "    return objects"], "outputs": []}, {"cell_type": "code", "metadata": {}, "source": ["# Exemple d'utilisation des fonctions d'analyse avancées\n", "if training_examples:\n", "    example = training_examples[0]\n", "    \n", "    print(\"Analyse des patterns dans la grille d'entrée:\")\n", "    analyze_patterns(example['input'])\n", "    \n", "    print(\"\\nAnalyse des transformations entre l'entrée et la sortie:\")\n", "    analyze_transformations(example['input'], example['output'])\n", "    \n", "    print(\"\\nDétection des objets dans la grille d'entrée:\")\n", "    detect_objects(example['input'])\n"], "outputs": []}, {"cell_type": "code", "metadata": {}, "source": ["# Importer le module arc_utils et les bibliothèques nécessairesimport arc_utilsimport numpy as npimport matplotlib.pyplot as pltimport jsonfrom IPython.display import display, HTML, Javascript# Vérifier que le module est correctement chargéprint(\"Module arc_utils chargé avec succès!\")# Fonction pour afficher une grilledef display_grid(grid, title=None):    plt.figure(figsize=(5, 5))    plt.imshow(grid, cmap='tab10', vmin=0, vmax=9)    plt.grid(True, color='black', linestyle='-', linewidth=1)    plt.xticks(np.arange(-0.5, len(grid[0]), 1), [])    plt.yticks(np.arange(-0.5, len(grid), 1), [])        # Ajouter les valeurs dans les cellules    for i in range(len(grid)):        for j in range(len(grid[0])):            plt.text(j, i, str(grid[i][j]), ha='center', va='center', color='white', fontweight='bold')        if title:        plt.title(title)    plt.tight_layout()    plt.show()", "", "# Fonction pour envoyer des commandes à l'éditeur d'automatisation", "def send_commands(commands):", "    try:", "        # Importer le module js pour interagir avec JavaScript", "        import js", "        ", "        # Convertir les commandes en JSON", "        commands_json = json.dumps(commands)", "        ", "        # Envoyer les commandes au frontend", "        if hasattr(js.window.parent, 'notebookCommandsCallback'):", "            js.window.parent.notebookCommandsCallback(commands_json)", "            print('Commandes envoyées au frontend')", "        else:", "            print('Callback pour les commandes non disponible dans le frontend')", "    except Exception as e:", "        print(f'Erreur lors de l\\'envoi des commandes au frontend: {e}')"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Chargement des Données de la Tâche\n", "\n", "Récupérons les données de la tâche actuellement chargée dans l'application."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Variables pour stocker les données de la tâche\n", "task_id = None\n", "train_input = []\n", "train_output = []\n", "test_input = []\n", "\n", "# Essayer de récupérer les données depuis le frontend\n", "try:\n", "    from IPython.display import display, Javascript\n", "    display(Javascript(\"\"\"\n", "    try {\n", "        // Récupérer les données de la tâche depuis le frontend\n", "        const taskData = window.parent.currentTask;\n", "        if (taskData) {\n", "            // Extraire l'ID de la tâche\n", "            const taskId = window.parent.taskName || 'unknown';\n", "            \n", "            // Extraire les données d'entrée et de sortie d'entraînement\n", "            const trainInput = taskData.train.map(pair => pair.input);\n", "            const trainOutput = taskData.train.map(pair => pair.output);\n", "            \n", "            // Extraire les données d'entrée de test\n", "            const testInput = taskData.test.map(pair => pair.input);\n", "            \n", "            // Assigner les variables Python\n", "            IPython.notebook.kernel.execute(`task_id = \"${taskId}\"`);\n", "            IPython.notebook.kernel.execute(`train_input = ${JSON.stringify(trainInput)}`);\n", "            IPython.notebook.kernel.execute(`train_output = ${JSON.stringify(trainOutput)}`);\n", "            IPython.notebook.kernel.execute(`test_input = ${JSON.stringify(testInput)}`);\n", "            \n", "            console.log('Donn<PERSON> de la tâche récupérées avec succès');\n", "        } else {\n", "            console.error('Aucune tâche chargée dans le frontend');\n", "        }\n", "    } catch (e) {\n", "        console.error('Erreur lors de la récupération des données de la tâche:', e);\n", "    }\n", "    \"\"\"))\n", "    \n", "    # Attendre que les données soient chargées\n", "    import time\n", "    time.sleep(1)\n", "except Exception as e:\n", "    print(f\"Erreur lors de la récupération des données depuis le frontend: {e}\")\n", "\n", "# Si les données n'ont pas été récupérées depuis le frontend, essayer de les charger depuis arcdata\n", "if not train_input and task_id:\n", "    print(f\"Tentative de chargement de la tâche {task_id} depuis arcdata...\")\n", "    try:\n", "        task_data = arc_utils.load_task(task_id)\n", "        if task_data:\n", "            train_input = [pair[\"input\"] for pair in task_data[\"train\"]]\n", "            train_output = [pair[\"output\"] for pair in task_data[\"train\"]]\n", "            test_input = [pair[\"input\"] for pair in task_data[\"test\"]]\n", "            print(f\"Tâche {task_id} chargée avec succès depuis arcdata\")\n", "    except Exception as e:\n", "        print(f\"Erreur lors du chargement de la tâche depuis arcdata: {e}\")\n", "\n", "# Afficher les informations sur la tâche\n", "if train_input:\n", "    print(f\"Tâche: {task_id}\")\n", "    print(f\"Nombre d'exemples d'entraînement: {len(train_input)}\")\n", "    print(f\"Nombre d'exemples de test: {len(test_input)}\")\n", "else:\n", "    print(\"Aucune donnée de tâche disponible. Veuillez charger une tâche dans l'application.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Visualisation des Exemples d'Entraînement\n", "\n", "Visualisons les exemples d'entraînement pour mieux comprendre la tâche."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fonction pour visualiser une grille\n", "def visualize_grid(grid, title=None):\n", "    plt.figure(figsize=(5, 5))\n", "    plt.imshow(grid, cmap='tab10', vmin=0, vmax=9)\n", "    plt.grid(True, color='black', linestyle='-', linewidth=1)\n", "    plt.xticks(np.arange(-0.5, len(grid[0]), 1), [])\n", "    plt.yticks(np.arange(-0.5, len(grid), 1), [])\n", "    \n", "    # Ajouter les valeurs dans les cellules\n", "    for i in range(len(grid)):\n", "        for j in range(len(grid[0])):\n", "            plt.text(j, i, str(grid[i][j]), ha='center', va='center', color='white', fontweight='bold')\n", "    \n", "    if title:\n", "        plt.title(title)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Visualiser les exemples d'entraînement\n", "if train_input and train_output:\n", "    for i in range(len(train_input)):\n", "        print(f\"Exemple d'entraînement {i+1}:\")\n", "        visualize_grid(train_input[i], title=f\"Entrée {i+1}\")\n", "        visualize_grid(train_output[i], title=f\"Sortie {i+1}\")\n", "else:\n", "    print(\"Aucun exemple d'entraînement disponible.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON><PERSON> Transformation\n", "\n", "Analysons la transformation entre les grilles d'entrée et de sortie pour comprendre la règle."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fonction pour analyser les différences entre deux grilles\n", "def analyze_differences(input_grid, output_grid):\n", "    if len(input_grid) != len(output_grid) or len(input_grid[0]) != len(output_grid[0]):\n", "        print(\"Les dimensions des grilles sont différentes.\")\n", "        return\n", "    \n", "    height = len(input_grid)\n", "    width = len(input_grid[0])\n", "    \n", "    # C<PERSON>er une grille de différences\n", "    diff_grid = np.zeros((height, width), dtype=int)\n", "    for i in range(height):\n", "        for j in range(width):\n", "            if input_grid[i][j] != output_grid[i][j]:\n", "                diff_grid[i][j] = 1\n", "    \n", "    # Visualiser la grille de différences\n", "    plt.figure(figsize=(5, 5))\n", "    plt.imshow(diff_grid, cmap='binary')\n", "    plt.grid(True, color='black', linestyle='-', linewidth=1)\n", "    plt.title(\"Différences (blanc = changé)\")\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Compter les changements\n", "    changes = np.sum(diff_grid)\n", "    print(f\"Nombre de cellules modifiées: {changes} sur {height*width} ({changes/(height*width)*100:.1f}%)\")\n", "\n", "# Analyser les différences pour chaque exemple d'entraînement\n", "if train_input and train_output:\n", "    for i in range(len(train_input)):\n", "        print(f\"\\nAnalyse des différences pour l'exemple {i+1}:\")\n", "        analyze_differences(train_input[i], train_output[i])\n", "else:\n", "    print(\"Aucun exemple d'entraînement disponible.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Génération de Commandes\n", "\n", "Générons des commandes d'automatisation pour résoudre la tâche."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fonction pour générer des commandes à partir d'une paire entrée-sortie\n", "def generate_commands_for_pair(input_grid, output_grid):\n", "    # Utiliser la fonction de arc_utils pour générer les commandes\n", "    commands = []\n", "    \n", "    # Initialiser la grille avec les dimensions de l'entrée\n", "    height = len(input_grid)\n", "    width = len(input_grid[0])\n", "    commands.append(f\"INIT {width} {height}\")\n", "    \n", "    # Copier la grille d'entrée\n", "    for i in range(height):\n", "        for j in range(width):\n", "            if input_grid[i][j] > 0:\n", "                commands.append(f\"EDIT {j} {i} {input_grid[i][j]}\")\n", "    \n", "    # Appliquer les modifications pour obtenir la sortie\n", "    for i in range(height):\n", "        for j in range(width):\n", "            if input_grid[i][j] != output_grid[i][j]:\n", "                commands.append(f\"EDIT {j} {i} {output_grid[i][j]}\")\n", "    \n", "    # Proposer la solution\n", "    commands.append(\"PROPOSE\")\n", "    \n", "    return commands\n", "\n", "# Générer des commandes pour le premier exemple d'entraînement\n", "if train_input and train_output:\n", "    print(\"Génération de commandes pour le premier exemple d'entraînement:\")\n", "    commands = generate_commands_for_pair(train_input[0], train_output[0])\n", "    for cmd in commands:\n", "        print(cmd)\n", "    \n", "    # Valider les commandes avec arc_utils\n", "    print(\"\\nValidation des commandes avec arc_utils:\")\n", "    validation_result = arc_utils.validate_commands(commands)\n", "    if validation_result['valid']:\n", "        print(\"Les commandes sont valides.\")\n", "    else:\n", "        print(f\"Les commandes ne sont pas valides: {validation_result['error']}\")\n", "    \n", "    # Exécuter les commandes avec arc_utils\n", "    print(\"\\nExécution des commandes avec arc_utils:\")\n", "    execution_result = arc_utils.execute_commands(commands)\n", "    if execution_result['success']:\n", "        print(\"Exécution réussie.\")\n", "        # Visualiser la grille résultante\n", "        visualize_grid(execution_result['grid'], title=\"Résultat de l'exécution\")\n", "    else:\n", "        print(f\"Erreur lors de l'exécution: {execution_result['error']}\")\n", "else:\n", "    print(\"Aucun exemple d'entraînement disponible.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Application aux Exemples de Test\n", "\n", "Appliquons notre solution aux exemples de test."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualiser les exemples de test\n", "if test_input:\n", "    for i in range(len(test_input)):\n", "        print(f\"Exemple de test {i+1}:\")\n", "        visualize_grid(test_input[i], title=f\"Entrée de test {i+1}\")\n", "else:\n", "    print(\"Aucun exemple de test disponible.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Envoyer les commandes à l'éditeur d'automatisation", "if test_example and 'commands' in locals():", "    send_commands(commands)", "    ", "    # Valider les commandes avec arc_utils", "    validation_result = arc_utils.validate_commands(commands)", "    if validation_result['valid']:", "        print(\"Les commandes sont valides.\")", "    else:", "        print(f\"Les commandes ne sont pas valides: {validation_result['errors']}\")", "else:", "    print(\"Aucun exemple de test disponible ou aucune commande générée.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Guide d'utilisation du notebook", "", "Ce notebook vous permet d'analyser et de résoudre des tâches ARC (Abstraction and Reasoning Corpus). Voici comment l'utiliser efficacement :", "", "## Structure du notebook", "", "Le notebook est organisé en sections :", "1. **Configuration et importation** : Importe les bibliothèques nécessaires", "2. **Récupération des données** : Charge les données de la tâche courante", "3. **Visualisation des exemples** : Affiche les grilles d'entrée et de sortie", "4. **Analyse des données** : Fournit des outils pour analyser les patterns et transformations", "5. **Développement de la solution** : Espace pour implémenter votre solution", "6. **Génération de commandes** : Convertit votre solution en commandes pour l'éditeur d'automatisation", "", "## Fonctions disponibles", "", "### Fonctions de base", "- `display_grid(grid, title=None)` : Affiche une grille avec un titre optionnel", "- `arc_utils.load_task(task_id)` : Charge une tâche ARC depuis le répertoire arcdata", "- `arc_utils.validate_commands(commands)` : <PERSON><PERSON> une liste de commandes", "- `arc_utils.execute_commands(commands)` : Exécute une liste de commandes et retourne la grille résultante", "- `arc_utils.generate_commands_from_grid(grid)` : <PERSON>énère des commandes à partir d'une grille", "", "### Fonctions d'analyse avancées", "- `analyze_patterns(grid)` : Analyse les patterns dans une grille (symétries, motifs répétitifs)", "- `analyze_transformations(input_grid, output_grid)` : Analyse les transformations entre deux grilles", "- `detect_objects(grid, background=0)` : Détecte les objets dans une grille", "", "## Commandes d'automatisation", "", "Les commandes d'automatisation permettent de construire une solution. Voici les commandes disponibles :", "", "- `INIT width height` : Initialise une grille vide de dimensions width × height", "- `EDIT x y value` : Définit la valeur de la cellule aux coordonnées (x, y)", "- `COPY x1 y1 x2 y2` : Co<PERSON> la valeur de la cellule (x1, y1) vers la cellule (x2, y2)", "- `FILL x y width height value` : Remplit un rectangle avec une valeur", "- `PROPOSE` : Propose la solution actuelle", "", "## Envoi des commandes à l'éditeur d'automatisation", "", "Pour envoyer des commandes à l'éditeur d'automatisation, utilisez le code suivant :", "", "```python", "# G<PERSON><PERSON><PERSON> les commandes", "commands = arc_utils.generate_commands_from_grid(output_grid)", "", "# Affiche<PERSON> les commandes", "for cmd in commands:", "    print(f\"  {cmd}\")", "", "# Envoyer les commandes à l'éditeur d'automatisation", "from IPython.display import display, Javascript", "display(Javascript(\"\"\"", "try {", "    // Récupérer les commandes générées", "    const commands = %s;", "    ", "    // Envoyer les commandes au frontend", "    if (window.parent.notebookCommandsCallback) {", "        window.parent.notebookCommandsCallback(commands);", "        console.log('Commandes envoyées au frontend:', commands);", "    } else {", "        console.error('Callback pour les commandes non disponible dans le frontend');", "    }", "} catch (e) {", "    console.error('<PERSON><PERSON>ur lors de l\\'envoi des commandes au frontend:', e);", "}", "\"\"\" % json.dumps(commands)))", "```", "", "## Conseils pour résoudre les tâches ARC", "", "1. **Examinez attentivement les exemples** : Comprenez la transformation entre l'entrée et la sortie", "2. **Recherchez des patterns** : Utilisez les fonctions d'analyse pour détecter des patterns", "3. **Testez différentes hypothèses** : Implémentez et testez plusieurs approches", "4. **Validez votre solution** : Vérifiez que votre solution fonctionne sur tous les exemples d'entraînement", "5. **<PERSON><PERSON><PERSON><PERSON> des commandes** : Convertissez votre solution en commandes pour l'éditeur d'automatisation"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}, "description": "Template pour analyser et résoudre les tâches ARC avec arc_utils"}, "nbformat": 4, "nbformat_minor": 4}