#!/usr/bin/env python3
"""
Analyse précise de la position de collage pour PASTE [0,1 1,2]
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import numpy as np

def analyze_expected_result():
    """Analyse du résultat attendu pour déterminer la position de collage correcte"""
    
    print("🔍 Analyse du résultat attendu...")
    
    # Clipboard après MULTIPLY 4 false
    clipboard = np.array([
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0]
    ])
    
    # Résultat attendu
    expected = np.array([
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    
    print("📋 Clipboard (8x8):")
    print(clipboard)
    print("\n📋 Résultat attendu (9x9):")
    print(expected)
    
    # Chercher toutes les positions possibles où le clipboard pourrait être collé
    print("\n🔍 Recherche de la position de collage...")
    
    for start_row in range(10):  # Tester même les positions négatives
        for start_col in range(10):
            test_grid = np.zeros((9, 9), dtype=int)
            
            # Simuler le collage à cette position
            matches = 0
            total_non_zero = 0
            
            for r in range(8):
                for c in range(8):
                    if clipboard[r, c] != 0:  # Seulement les cellules non-zéro
                        total_non_zero += 1
                        grid_r, grid_c = start_row + r, start_col + c
                        if 0 <= grid_r < 9 and 0 <= grid_c < 9:
                            test_grid[grid_r, grid_c] = clipboard[r, c]
                            if expected[grid_r, grid_c] == clipboard[r, c]:
                                matches += 1
            
            if total_non_zero > 0:
                percentage = (matches / total_non_zero) * 100
                if percentage > 90:  # Seulement les bonnes correspondances
                    print(f"Position ({start_row}, {start_col}): {matches}/{total_non_zero} ({percentage:.1f}%)")
                    
                    if percentage == 100:
                        print(f"✅ Position parfaite trouvée: ({start_row}, {start_col})")
                        print("Grille résultante:")
                        print(test_grid)
                        return start_row, start_col

def test_multiple_paste_positions():
    """Test de collage à plusieurs positions pour comprendre le comportement attendu"""
    
    print("\n" + "="*60)
    print("🧪 Test de collage à plusieurs positions...")
    
    clipboard = np.array([
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0]
    ])
    
    expected = np.array([
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    
    # D'après la doc, PASTE [0,1 1,2] pourrait signifier :
    # 1. Coller à la position (0,1)
    # 2. Coller dans le rectangle de (0,1) à (1,2)
    # 3. Coller à toutes les positions du rectangle
    
    interpretations = [
        ("Position (0,1)", [(0, 1)]),
        ("Rectangle (0,1) à (1,2)", [(0, 1), (0, 2), (1, 1), (1, 2)]),
        ("Positions multiples", [(0, 1), (1, 2)])
    ]
    
    for name, positions in interpretations:
        print(f"\n--- {name} ---")
        
        test_grid = np.zeros((9, 9), dtype=int)
        
        for x_dest, y_dest in positions:
            print(f"Collage à ({x_dest}, {y_dest})")
            
            for r in range(8):
                for c in range(8):
                    if clipboard[r, c] != 0:  # Seulement les cellules non-zéro
                        grid_r, grid_c = x_dest + r, y_dest + c
                        if 0 <= grid_r < 9 and 0 <= grid_c < 9:
                            test_grid[grid_r, grid_c] = clipboard[r, c]
        
        # Comparer avec l'attendu
        matches = np.sum(test_grid == expected)
        total = test_grid.size
        percentage = (matches / total) * 100
        
        print(f"Résultat: {matches}/{total} ({percentage:.1f}%)")
        
        if percentage > 95:
            print("✅ Interprétation correcte trouvée!")
            print("Grille résultante:")
            print(test_grid)

def analyze_rectangle_interpretation():
    """Analyse de l'interprétation rectangle selon la documentation"""
    
    print("\n" + "="*60)
    print("🔍 Analyse de l'interprétation rectangle selon la doc...")
    
    # D'après la doc: [0,1 1,2] = zone rectangulaire de (0,1) à (1,2)
    # Cela couvre: (0,1), (0,2), (1,1), (1,2)
    
    # Mais pour PASTE, cela pourrait signifier :
    # - Coller le clipboard en adaptant sa taille au rectangle
    # - Coller le clipboard à la position du coin supérieur gauche du rectangle
    
    clipboard = np.array([
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0],
        [3, 3, 3, 3, 0, 0, 0, 0]
    ])
    
    expected = np.array([
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    
    print("Rectangle [0,1 1,2] couvre les positions:")
    rectangle_positions = [(0, 1), (0, 2), (1, 1), (1, 2)]
    for pos in rectangle_positions:
        print(f"  {pos}")
    
    # Test: coller le clipboard en utilisant le coin supérieur gauche du rectangle
    print(f"\n🔧 Test: coller à la position du coin supérieur gauche (0,1)...")
    
    test_grid = np.zeros((9, 9), dtype=int)
    x_dest, y_dest = 0, 1  # Coin supérieur gauche du rectangle
    
    for r in range(8):
        for c in range(8):
            grid_r, grid_c = x_dest + r, y_dest + c
            if 0 <= grid_r < 9 and 0 <= grid_c < 9:
                test_grid[grid_r, grid_c] = clipboard[r, c]
    
    matches = np.sum(test_grid == expected)
    total = test_grid.size
    percentage = (matches / total) * 100
    
    print(f"Résultat: {matches}/{total} ({percentage:.1f}%)")
    print("Grille résultante:")
    print(test_grid)
    
    if percentage < 100:
        print("\n❌ Ce n'est pas la bonne interprétation.")
        print("Il faut chercher une autre logique...")

if __name__ == "__main__":
    # Analyse du résultat attendu
    pos = analyze_expected_result()
    
    # Test de différentes interprétations
    test_multiple_paste_positions()
    
    # Analyse selon la documentation
    analyze_rectangle_interpretation()