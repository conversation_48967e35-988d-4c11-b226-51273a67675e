"""
CommandExecutor refactorisé pour utiliser directement les commandes unifiées
"""

import numpy as np
import traceback
from typing import List, Dict, Any, Optional, Tuple
from .unified_command import UnifiedCommand
from .coordinate_operators import CoordinateOperators, resolve_coordinate_operator

# CommandDecompressor supprimé - plus utilisé dans le système moderne


class CommandExecutor:
    """Exécuteur de commandes d'automatisation pour les puzzles ARC - Version Unifiée"""

    def __init__(self, task_data=None, test_index=0):
        """Initialise l'exécuteur de commandes"""
        # Ne réinitialiser que si la grille n'est pas déjà configurée
        if not hasattr(self, 'grid') or self.grid is None:
            self.grid = None
            self.width = 0
            self.height = 0
        self.history = []
        self.error = None
        self.clipboard = None  # Pour les opérations COPY/CUT/PASTE
        self.clipboard_mask = None  # Masque pour les sélections spéciales

        # Nouvelles propriétés pour la commande INPUT
        self.task_data = task_data
        self.test_index = test_index

    def execute_commands(self, commands: List[str]) -> Dict[str, Any]:
        """
        Exécute une liste de commandes unifiées et retourne le résultat
        
        Args:
            commands: Liste de commandes au format unifié
            
        Returns:
            Dict contenant success, grid, width, height, history, error
        """
        self.grid = None
        self.width = 0
        self.height = 0
        self.history = []
        self.error = None
        self.clipboard = None

        for i, command in enumerate(commands):
            if not command.strip():
                continue

            try:
                if not self._execute_command(command):
                    # L'erreur est maintenant définie dans _execute_command
                    if not self.error:
                         self.error = f"Échec à la commande {i+1}: {command}"
                    break
            except Exception as e:
                import traceback
                self.error = f"Exception à la commande {i+1}: '{command}'. Erreur: {str(e)}\n{traceback.format_exc()}"
                break

        result = {
            "success": self.error is None,
            "grid": self.grid.tolist() if self.grid is not None else None,
            "width": self.width,
            "height": self.height,
            "history": self.history,
            "error": self.error
        }

        return result

    def validate_solution(self, commands: List[str], expected_output: List[List[int]]) -> Dict[str, Any]:
        """Valide une solution en exécutant les commandes et en comparant avec la sortie attendue"""
        result = self.execute_commands(commands)

        if not result["success"]:
            return result

        expected = expected_output

        # Vérifier que les dimensions correspondent
        if self.grid is None:
            result["error"] = "La grille n'a pas été initialisée"
            return result
        grid_height = len(self.grid)
        grid_width = len(self.grid[0]) if grid_height > 0 else 0
        expected_height = len(expected)
        expected_width = len(expected[0]) if expected_height > 0 else 0

        if grid_height != expected_height or grid_width != expected_width:
            result["success"] = False
            result["error"] = f"Dimensions incorrectes: obtenu {grid_height}x{grid_width}, attendu {expected_height}x{expected_width}"
            return result

        # Vérifier que les valeurs correspondent
        grid_list = self.grid.tolist()
        if grid_list != expected:
            result["success"] = False
            result["error"] = "La grille générée ne correspond pas à la sortie attendue"
            return result

        return result

    def execute(self, command: UnifiedCommand) -> Dict[str, Any]:
        """
        Exécute une seule commande unifiée
        
        Args:
            command: Commande unifiée à exécuter
            
        Returns:
            Dict contenant success, grid, width, height, history, error
        """
        try:
            success = self._execute_unified_command(command)
            
            result = {
                "success": success and self.error is None,
                "grid": self.grid.tolist() if self.grid is not None else None,
                "width": self.width,
                "height": self.height,
                "history": self.history,
                "error": self.error
            }
            
            return result
            
        except Exception as e:
            self.error = f"Exception lors de l'exécution: {str(e)}"
            return {
                "success": False,
                "grid": None,
                "width": 0,
                "height": 0,
                "history": self.history,
                "error": self.error
            }

    def _execute_command(self, command: str) -> bool:
        """Exécute une commande individuelle au format unifié"""
        try:
            # Parser la commande unifiée
            unified_cmd = UnifiedCommand.parse(command)
            if not unified_cmd:
                self.error = f"Impossible de parser la commande: {command}"
                return False
            
            # Enregistrer la commande originale dans l'historique
            self.history.append(command)
            
            # Router vers la méthode appropriée
            action = unified_cmd.action
            
            if action == 'INIT':
                return self._cmd_init(unified_cmd)
            elif action == 'EDIT':
                return self._cmd_edit(unified_cmd)
            elif action == 'FILL':
                return self._cmd_fill(unified_cmd)
            elif action == 'CLEAR':
                return self._cmd_clear(unified_cmd)
            elif action == 'SURROUND':
                return self._cmd_surround(unified_cmd)
            elif action == 'REPLACE':
                return self._cmd_replace(unified_cmd)
            elif action == 'FLOODFILL':
                return self._cmd_floodfill(unified_cmd)
            elif action == 'CUT':
                return self._cmd_cut(unified_cmd)
            elif action == 'COPY':
                return self._cmd_copy(unified_cmd)
            elif action == 'PASTE':
                return self._cmd_paste(unified_cmd)
            elif action == 'FLIP':
                return self._cmd_flip(unified_cmd)
            elif action == 'ROTATE':
                return self._cmd_rotate(unified_cmd)
            elif action == 'INSERT':
                return self._cmd_insert(unified_cmd)
            elif action == 'DELETE':
                return self._cmd_delete(unified_cmd)
            elif action == 'EXTRACT':
                return self._cmd_extract(unified_cmd)
            elif action == 'INPUT':
                return self._cmd_input(unified_cmd)
            elif action == 'RESIZE':
                return self._cmd_resize(unified_cmd)
            elif action == 'MOTIF':
                return self._cmd_motif(unified_cmd)
            elif action == 'MULTIPLY':
                return self._cmd_multiply(unified_cmd)
            elif action == 'DIVIDE':
                return self._cmd_divide(unified_cmd)
            elif action == 'END':
                return self._cmd_end(unified_cmd)
            elif action == 'AND':
                return self._cmd_and(unified_cmd)
            elif action == 'OR':
                return self._cmd_or(unified_cmd)
            elif action == 'XOR':
                return self._cmd_xor(unified_cmd)
            else:
                self.error = f"_execute_command Commande inconnue: {action}"
                return False
                
        except Exception as e:
            import traceback
            self.error = f"Exception inattendue lors de l'exécution de '{command}': {str(e)}\n{traceback.format_exc()}"
            return False

    def _execute_unified_command(self, unified_cmd: UnifiedCommand) -> bool:
        """Exécute une commande unifiée déjà parsée"""
        try:
            # Enregistrer la commande dans l'historique
            self.history.append(str(unified_cmd))
            
            # Router vers la méthode appropriée
            action = unified_cmd.action
            
            if action == 'INIT':
                return self._cmd_init(unified_cmd)
            elif action == 'EDIT':
                return self._cmd_edit(unified_cmd)
            elif action == 'FILL':
                return self._cmd_fill(unified_cmd)
            elif action == 'CLEAR':
                return self._cmd_clear(unified_cmd)
            elif action == 'SURROUND':
                return self._cmd_surround(unified_cmd)
            elif action == 'REPLACE':
                return self._cmd_replace(unified_cmd)
            elif action == 'COPY':
                return self._cmd_copy(unified_cmd)
            elif action == 'CUT':
                return self._cmd_cut(unified_cmd)
            elif action == 'PASTE':
                return self._cmd_paste(unified_cmd)
            elif action == 'FLIP':
                return self._cmd_flip(unified_cmd)
            elif action == 'ROTATE':
                return self._cmd_rotate(unified_cmd)
            elif action == 'INSERT':
                return self._cmd_insert(unified_cmd)
            elif action == 'DELETE':
                return self._cmd_delete(unified_cmd)
            elif action == 'EXTRACT':
                return self._cmd_extract(unified_cmd)
            elif action == 'INPUT':
                return self._cmd_input(unified_cmd)
            elif action == 'RESIZE':
                return self._cmd_resize(unified_cmd)
            elif action == 'MOTIF':
                return self._cmd_motif(unified_cmd)
            elif action == 'MULTIPLY':
                return self._cmd_multiply(unified_cmd)
            elif action == 'DIVIDE':
                return self._cmd_divide(unified_cmd)
            elif action == 'END':
                return self._cmd_end(unified_cmd)
            elif action == 'AND':
                return self._cmd_and(unified_cmd)
            elif action == 'OR':
                return self._cmd_or(unified_cmd)
            elif action == 'XOR':
                return self._cmd_xor(unified_cmd)
            else:
                self.error = f"_execute_unified_command Commande inconnue: {action}"
                return False
                
        except Exception as e:
            self.error = f"Erreur lors de l'exécution de {unified_cmd.action}: {str(e)}"
            return False

    # === COMMANDES DE BASE ===

    def _cmd_init(self, cmd: UnifiedCommand) -> bool:
        """Initialise une grille de taille width x height"""
        if len(cmd.parameters) != 2:
            return False

        try:
            width = int(cmd.parameters[0])
            height = int(cmd.parameters[1])

            if width <= 0 or height <= 0:
                return False

            # Si une grille d'entrée existe déjà avec les bonnes dimensions, la préserver
            if (hasattr(self, 'grid') and self.grid is not None and 
                hasattr(self, 'width') and hasattr(self, 'height') and
                self.width == width and self.height == height and
                self.grid.shape == (width, height)):
                # Grille déjà initialisée avec les bonnes dimensions, ne pas l'écraser
                return True

            self.width = width
            self.height = height
            # CORRECTION: INIT 7x3 signifie 7 colonnes x 3 lignes
            # numpy utilise (rows, cols) donc pour width=7, height=3 -> (height, width) = (3, 7)
            self.grid = np.zeros((height, width), dtype=int)
            return True
        except (ValueError, TypeError):
            return False

    def _cmd_edit(self, cmd: UnifiedCommand) -> bool:
        """Modifie une ou plusieurs cellules avec une valeur"""
        if len(cmd.parameters) != 1:
            return False

        if self.grid is None:
            self.error = "La grille n'a pas été initialisée."
            return False

        try:
            value = int(cmd.parameters[0])
            
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                
                if len(coords) == 2:
                    # Format simple: EDIT 1 [x,y]
                    coordinate_blocks.append([f"{coords[0]},{coords[1]}"])
                elif len(coords) % 2 == 0:
                    # Format multiple: EDIT 1 [x1,y1] [x2,y2] [x3,y3] ...
                    for i in range(0, len(coords), 2):
                        coordinate_blocks.append([f"{coords[i]},{coords[i+1]}"])
                else:
                    self.error = f"Format de coordonnées non valide pour EDIT: {cmd.coordinates}"
                    return False
            
            #print(f"[EDIT] Traitement de {len(coordinate_blocks)} blocs de coordonnées: {coordinate_blocks}")
            
            # Utiliser la fonction générique pour traiter les blocs
            def edit_action(x1, y1, x2, y2):
                if self.grid is None:
                    return False

                # Pour EDIT, traiter chaque cellule individuellement
                for x in range(x1, x2 + 1):
                    for y in range(y1, y2 + 1):
                        self.grid[x, y] = value
                
                # if x1 == x2 and y1 == y2:
                #     print(f"[EDIT] Cellule éditée: ({x1},{y1}) = {value}")
                # else:
                #     print(f"[EDIT] Rectangle édité: ({x1},{y1}) -> ({x2},{y2}) = {value}")
            
            self._process_coordinate_blocks(coordinate_blocks, edit_action)
            return True
                
        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Erreur dans _cmd_edit: {e}"
            return False

    def _cmd_fill(self, cmd: UnifiedCommand) -> bool:
        """Remplit les zones sélectionnées avec une couleur"""
        if len(cmd.parameters) != 1:
            return False

        if self.grid is None:
            return False

        try:
            color = int(cmd.parameters[0])
            
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de 4 coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coords):
                    x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                    if x1 == x2 and y1 == y2:
                        # Cellule simple
                        coordinate_blocks.append([f"{x1},{y1}"])
                    else:
                        # Rectangle
                        coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4
            
            #print(f"[FILL] Traitement de {len(coordinate_blocks)} blocs de coordonnées: {coordinate_blocks}")
            
            # Utiliser la fonction générique pour traiter les blocs
            def fill_action(x1, y1, x2, y2):
                if self.grid is None:
                    return False

                self.grid[x1:x2+1, y1:y2+1] = color
                # if x1 == x2 and y1 == y2:
                #     print(f"[FILL] Cellule remplie: ({x1},{y1}) avec couleur {color}")
                # else:
                #     print(f"[FILL] Rectangle rempli: ({x1},{y1}) -> ({x2},{y2}) avec couleur {color}")
            
            self._process_coordinate_blocks(coordinate_blocks, fill_action)
            return True
            
        except (ValueError, TypeError, IndexError) as e:
            print(f"[FILL] Erreur: {e}")
            return False

    def _cmd_clear(self, cmd: UnifiedCommand) -> bool:
        """Efface les zones sélectionnées (remplit avec 0)"""
        if self.grid is None:
            return False

        try:
            
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de 4 coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coords):
                    x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                    if x1 == x2 and y1 == y2:
                        # Cellule simple
                        coordinate_blocks.append([f"{x1},{y1}"])
                    else:
                        # Rectangle
                        coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4
            
            #print(f"[CLEAR] Traitement de {len(coordinate_blocks)} blocs de coordonnées: {coordinate_blocks}")
            
            # Utiliser la fonction générique pour traiter les blocs
            def clear_action(x1, y1, x2, y2):
                if self.grid is None:
                    return False

                self.grid[x1:x2+1, y1:y2+1] = 0
                # if x1 == x2 and y1 == y2:
                #     print(f"[CLEAR] Cellule effacée: ({x1},{y1})")
                # else:
                #     print(f"[CLEAR] Rectangle effacé: ({x1},{y1}) -> ({x2},{y2})")
            
            self._process_coordinate_blocks(coordinate_blocks, clear_action)
            return True
            
        except (ValueError, TypeError, IndexError) as e:
            print(f"[CLEAR] Erreur: {e}")
            return False

    def _cmd_surround(self, cmd: UnifiedCommand) -> bool:
        """Entoure les zones sélectionnées avec une couleur"""
        if len(cmd.parameters) != 1:
            return False

        if self.grid is None:
            return False

        try:
            color = int(cmd.parameters[0])
            
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de 4 coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                if cmd.coordinates:
                    # Vérification du format des coordonnées existant
                    if isinstance(cmd.coordinates[0], list):
                        # Format [[x1,y1,x2,y2]] ou [[x,y]]
                        for selection in cmd.coordinates:
                            if len(selection) == 2:
                                x, y = selection
                                coordinate_blocks.append([f"{x},{y}"])
                            elif len(selection) == 4:
                                x1, y1, x2, y2 = selection
                                if x1 == x2 and y1 == y2:
                                    coordinate_blocks.append([f"{x1},{y1}"])
                                else:
                                    coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    else:
                        # Format plat [x1,y1,x2,y2,...]
                        i = 0
                        while i + 3 < len(coords):
                            x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                            if x1 == x2 and y1 == y2:
                                coordinate_blocks.append([f"{x1},{y1}"])
                            else:
                                coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                            i += 4
            
            #print(f"[SURROUND] Traitement de {len(coordinate_blocks)} blocs de coordonnées: {coordinate_blocks}")
            
            # Utiliser la fonction générique pour traiter les blocs
            def surround_action(x1, y1, x2, y2):
                if self.grid is None:
                    return False

                x_min, x_max = min(x1, x2), max(x1, x2)
                y_min, y_max = min(y1, y2), max(y1, y2)

                # Coordonnées de la boîte englobante pour l'entourage
                surround_x_min = max(0, x_min - 1)
                surround_x_max = min(self.height - 1, x_max + 1)
                surround_y_min = max(0, y_min - 1)
                surround_y_max = min(self.width - 1, y_max + 1)

                # Itérer sur la boîte d'entourage et colorier si c'est une bordure
                for r in range(surround_x_min, surround_x_max + 1):
                    for c in range(surround_y_min, surround_y_max + 1):
                        # Ne pas colorier l'intérieur de la sélection originale
                        if not (x_min <= r <= x_max and y_min <= c <= y_max):
                            self.grid[r, c] = color
                
                # if x1 == x2 and y1 == y2:
                #     print(f"[SURROUND] Cellule entourée: ({x1},{y1}) avec couleur {color}")
                # else:
                #     print(f"[SURROUND] Rectangle entouré: ({x1},{y1}) -> ({x2},{y2}) avec couleur {color}")
            
            self._process_coordinate_blocks(coordinate_blocks, surround_action)
            return True
            
        except (ValueError, TypeError, IndexError) as e:
            print(f"[SURROUND] Erreur: {e}")
            return False

    def _cmd_replace(self, cmd: UnifiedCommand) -> bool:
        """Remplace des couleurs par une autre dans les zones sélectionnées"""
        if len(cmd.parameters) < 2:
            return False

        if self.grid is None:
            return False

        try:
            # Gestion des couleurs multiples pour REPLACE
            if len(cmd.parameters) >= 2:
                # Les paramètres peuvent être dans deux formats :
                # Format simple: [7, 5, 0] -> source_colors=[7, 5], target_color=0
                # Format groupé: [[7, 5], 0] -> source_colors=[7, 5], target_color=0
                
                if isinstance(cmd.parameters[0], list):
                    # Format groupé: [[7, 5], 0]
                    source_colors = cmd.parameters[0]
                    target_color = int(cmd.parameters[1])
                else:
                    # Format simple: [7, 5, 0]
                    source_colors = [int(c) for c in cmd.parameters[:-1]]
                    target_color = int(cmd.parameters[-1])
            else:
                return False
            
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode
                coordinate_blocks = []
                coords_source = getattr(cmd, 'coords', None) or getattr(cmd, 'coordinates', [])
                
                if coords_source:
                    if isinstance(coords_source[0], list):
                        # Format [[x1,y1,x2,y2]]
                        for coord_group in coords_source:
                            if len(coord_group) == 4:
                                x1, y1, x2, y2 = coord_group
                                if x1 == x2 and y1 == y2:
                                    coordinate_blocks.append([f"{x1},{y1}"])
                                else:
                                    coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    else:
                        # Format plat [x1,y1,x2,y2,...]
                        i = 0
                        while i + 3 < len(coords_source):
                            x1, y1, x2, y2 = coords_source[i], coords_source[i+1], coords_source[i+2], coords_source[i+3]
                            if x1 == x2 and y1 == y2:
                                coordinate_blocks.append([f"{x1},{y1}"])
                            else:
                                coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                            i += 4
            
            # print(f"[REPLACE] Traitement de {len(coordinate_blocks)} blocs de coordonnées: {coordinate_blocks}")
            # print(f"[REPLACE] Remplacement de {source_colors} par {target_color}")
            
            # Utiliser la fonction générique pour traiter les blocs
            def replace_action(x1, y1, x2, y2):
                if self.grid is None:
                    return False

                # Accès direct à la grille pour éviter les problèmes de vues NumPy
                region = self.grid[x1:x2+1, y1:y2+1]
                mask = np.isin(region, source_colors)
                # Application directe sur self.grid
                self.grid[x1:x2+1, y1:y2+1][mask] = target_color
                
                # if x1 == x2 and y1 == y2:
                #     print(f"[REPLACE] Cellule traitée: ({x1},{y1})")
                # else:
                #     print(f"[REPLACE] Rectangle traité: ({x1},{y1}) -> ({x2},{y2}), cellules modifiées: {np.sum(mask)}")
            
            self._process_coordinate_blocks(coordinate_blocks, replace_action)
            return True
            
        except (ValueError, TypeError, IndexError) as e:
            print(f"[REPLACE] Erreur: {e}")
            return False

    # === COMMANDES PRESSE-PAPIER ===

    def _cmd_cut(self, cmd: UnifiedCommand) -> bool:
        """Coupe un rectangle ou une sélection spéciale et le place dans le presse-papier"""
        if self._cmd_copy(cmd):
            # Effacer la zone copiée en utilisant le parsing générique
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de 4 coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coords):
                    x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                    if x1 == x2 and y1 == y2:
                        # Cellule simple
                        coordinate_blocks.append([f"{x1},{y1}"])
                    else:
                        # Rectangle
                        coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4
            
            # Utiliser la fonction générique pour traiter les blocs
            def cut_action(x1, y1, x2, y2):
                if self.grid is None:
                    return False

                self.grid[x1:x2+1, y1:y2+1] = 0
            
            self._process_coordinate_blocks(coordinate_blocks, cut_action)
            return True
        return False

    def _cmd_flip(self, unified_cmd: UnifiedCommand) -> bool:
        """Effectue une opération FLIP sur le contenu du presse-papier."""
        if not unified_cmd.parameters:
            self.error = "La commande FLIP nécessite une direction (HORIZONTAL ou VERTICAL)"
            return False

        direction = unified_cmd.parameters[0].upper()

        # FLIP opère maintenant uniquement sur le presse-papier
        if self.clipboard is None:
            self.error = "FLIP nécessite une commande COPY préalable pour avoir du contenu dans le presse-papier."
            return False

        if direction == 'HORIZONTAL':
            self._flip_horizontal()
        elif direction == 'VERTICAL':
            self._flip_vertical()
        else:
            self.error = f"Direction invalide pour FLIP: {direction}. Utilisez HORIZONTAL ou VERTICAL"
            return False

        return True

    def _cmd_rotate(self, unified_cmd: UnifiedCommand) -> bool:
        """Effectue une opération ROTATE sur le contenu du presse-papier."""
        if not unified_cmd.parameters:
            self.error = "La commande ROTATE nécessite une direction (LEFT ou RIGHT)"
            return False

        direction = unified_cmd.parameters[0].upper()

        # ROTATE opère maintenant uniquement sur le presse-papier
        if self.clipboard is None:
            self.error = "ROTATE nécessite une commande COPY préalable pour avoir du contenu dans le presse-papier."
            return False

        if direction == 'LEFT':
            self._rotate_left()
        elif direction == 'RIGHT':
            self._rotate_right()
        else:
            self.error = f"Direction invalide pour ROTATE: {direction}. Utilisez LEFT ou RIGHT"
            return False

        return True

    def _flip_horizontal(self):
        """Flips the clipboard horizontally."""
        if self.clipboard is not None:
            self.clipboard = np.fliplr(self.clipboard)
            if self.clipboard_mask is not None:
                self.clipboard_mask = np.fliplr(self.clipboard_mask)

    def _flip_vertical(self):
        """Flips the clipboard vertically."""
        if self.clipboard is not None:
            self.clipboard = np.flipud(self.clipboard)
            if self.clipboard_mask is not None:
                self.clipboard_mask = np.flipud(self.clipboard_mask)

    def _rotate_left(self):
        """Rotates the clipboard 90 degrees to the left."""
        if self.clipboard is not None:
            self.clipboard = np.rot90(self.clipboard)
            if self.clipboard_mask is not None:
                self.clipboard_mask = np.rot90(self.clipboard_mask)

    def _rotate_right(self):
        """Rotates the clipboard 90 degrees to the right."""
        if self.clipboard is not None:
            self.clipboard = np.rot90(self.clipboard, k=-1)
            if self.clipboard_mask is not None:
                self.clipboard_mask = np.rot90(self.clipboard_mask, k=-1)
        # print(f"[ROTATE] _rotate_right: ", self.clipboard, self.clipboard_mask) 

    def _cmd_copy(self, cmd: UnifiedCommand) -> bool:
        """Copie une ou plusieurs sélections dans le presse-papier en tant que motif unifié."""
        if self.grid is None:
            self.error = "La grille n'a pas été initialisée."
            return False

        try:
            # Utiliser uniquement la fonction générique pour parser les blocs de coordonnées
            coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command) if hasattr(cmd, 'raw_command') and cmd.raw_command else []

            # Si aucun bloc de coordonnées n'est trouvé, essayer les coordonnées standards
            if not coordinate_blocks and cmd.coordinates:
                # Fallback: traiter les coordonnées par paires de 4
                coords = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coords):
                    x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                    if x1 == x2 and y1 == y2:
                        # Cellule simple
                        coordinate_blocks.append([f"{x1},{y1}"])
                    else:
                        # Rectangle
                        coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4

            # Traitement unifié des blocs avec modificateurs
            selected_cells = []
            min_x, min_y = float('inf'), float('inf')
            max_x, max_y = -1, -1

            def copy_action(x1, y1, x2, y2):
                if self.grid is None:
                    return False

                nonlocal min_x, min_y, max_x, max_y
                min_x, min_y = min(min_x, x1), min(min_y, y1)
                max_x, max_y = max(max_x, x2), max(max_y, y2)
                
                for x in range(x1, x2 + 1):
                    for y in range(y1, y2 + 1):
                        selected_cells.append((x, y, self.grid[x, y]))

            # Utiliser la fonction générique pour traiter les blocs
            self._process_coordinate_blocks(coordinate_blocks, copy_action)

            if not selected_cells:
                self.error = "Aucune cellule à copier"
                return False

            # Créer le presse-papier avec les dimensions minimales
            clip_height = max_x - min_x + 1
            clip_width = max_y - min_y + 1
            self.clipboard = np.zeros((clip_height, clip_width), dtype=int)
            self.clipboard_mask = np.zeros((clip_height, clip_width), dtype=bool)

            # Remplir le presse-papier avec les cellules sélectionnées
            for x, y, value in selected_cells:
                rel_x, rel_y = x - min_x, y - min_y
                self.clipboard[rel_x, rel_y] = value
                self.clipboard_mask[rel_x, rel_y] = True

            return True
        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Exception dans _cmd_copy: {e}"
            return False


    def _cmd_paste(self, cmd: UnifiedCommand) -> bool:
        """Colle le contenu du presse-papier."""
        if self.grid is None or self.clipboard is None:
            self.error = "La grille n'est pas initialisée ou le presse-papiers est vide."
            return False

        try:
            # Si aucune coordonnée n'est fournie, on ne fait rien (le collage doit être explicite)
            if not cmd.coordinates:
                self.error = "La commande PASTE nécessite au moins une coordonnée de destination."
                return False

            # CORRECTION: Utiliser seulement la première position comme le frontend
            # Pour maintenir la cohérence avec le comportement frontend
            if len(cmd.coordinates) >= 2:
                x_dest, y_dest = cmd.coordinates[0], cmd.coordinates[1]
            else:
                self.error = "La commande PASTE nécessite au moins une coordonnée complète (x,y)."
                return False

            clip_height, clip_width = self.clipboard.shape

            # Vérifier les limites de la destination
            if x_dest < 0 or y_dest < 0 or x_dest >= self.height or y_dest >= self.width:
                self.error = f"Coordonnée de collage ({x_dest},{y_dest}) hors limites."
                return False

            # Coller en respectant le masque
            for r in range(clip_height):
                for c in range(clip_width):
                    if self.clipboard_mask is None or self.clipboard_mask[r, c]:
                        grid_r, grid_c = x_dest + r, y_dest + c
                        if 0 <= grid_r < self.height and 0 <= grid_c < self.width:
                            self.grid[grid_r, grid_c] = self.clipboard[r, c]
            return True
        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Exception dans _cmd_paste: {e}"
            return False



    # === COMMANDES DE TRANSFORMATION ===







    # === COMMANDES STRUCTURELLES ===

    def _cmd_insert(self, cmd: UnifiedCommand) -> bool:
        """Insère des lignes ou colonnes"""
        # INSERT nombre ROWS/COLUMNS BEFORE/AFTER/ABOVE/BELOW [coordinates]
        if len(cmd.parameters) < 2:
            self.error = "INSERT nécessite au moins 2 paramètres: nombre et type (ROWS/COLUMNS)"
            return False

        if self.grid is None:
            self.error = "La grille n'a pas été initialisée."
            return False

        try:
            count = int(cmd.parameters[0])
            element_type = cmd.parameters[1].upper()

            if count <= 0:
                self.error = f"Le nombre d'éléments à insérer doit être positif: {count}"
                return False

            if element_type not in ['ROWS', 'COLUMNS']:
                self.error = f"Type d'élément non valide: {element_type}. Utilisez ROWS ou COLUMNS"
                return False

            # Position par défaut si non spécifiée
            position = 'AFTER' if len(cmd.parameters) < 3 else cmd.parameters[2].upper()

            if position not in ['BEFORE', 'AFTER', 'ABOVE', 'BELOW']:
                self.error = f"Position non valide: {position}. Utilisez BEFORE, AFTER, ABOVE ou BELOW"
                return False

            # Normaliser les positions pour les lignes et colonnes
            if element_type == 'ROWS':
                if position in ['BEFORE', 'ABOVE']:
                    position = 'ABOVE'
                elif position in ['AFTER', 'BELOW']:
                    position = 'BELOW'
            elif element_type == 'COLUMNS':
                if position in ['BEFORE', 'LEFT']:
                    position = 'BEFORE'
                elif position in ['AFTER', 'RIGHT']:
                    position = 'AFTER'

            # Si des coordonnées sont spécifiées, insérer à ces positions
            if cmd.coordinates:
                return self._insert_at_coordinates(count, element_type, position, cmd)
            else:
                # Insérer au début ou à la fin selon la position
                return self._insert_global(count, element_type, position)

        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Erreur dans _cmd_insert: {e}"
            return False

    def _cmd_delete(self, cmd: UnifiedCommand) -> bool:
        """Supprime des lignes ou colonnes"""
        if len(cmd.parameters) < 1:
            self.error = "DELETE nécessite au moins 1 paramètre: type (ROWS/COLUMNS)"
            return False

        if self.grid is None:
            self.error = "La grille n'a pas été initialisée."
            return False

        try:
            element_type = cmd.parameters[0].upper()

            if element_type not in ['ROWS', 'COLUMNS']:
                self.error = f"Type d'élément non valide: {element_type}. Utilisez ROWS ou COLUMNS"
                return False

            # Si des coordonnées sont spécifiées, supprimer ces lignes/colonnes spécifiques
            if cmd.coordinates:
                return self._delete_at_coordinates(element_type, cmd)
            else:
                self.error = "DELETE nécessite des coordonnées pour spécifier quoi supprimer"
                return False

        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Erreur dans _cmd_delete: {e}"
            return False

    def _insert_at_coordinates(self, count: int, element_type: str, position: str, cmd) -> bool:
        """Insère des lignes/colonnes aux positions spécifiées par les coordonnées"""
        try:
            # Utiliser la fonction générique pour parser les blocs de coordonnées depuis raw_command
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: traiter les coordonnées par paires de 4
                coordinates = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coordinates):
                    x1, y1, x2, y2 = coordinates[i], coordinates[i+1], coordinates[i+2], coordinates[i+3]
                    coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4

            # Collecter toutes les positions d'insertion
            insert_positions = set()

            for block in coordinate_blocks:
                if len(block) == 1:
                    # Cellule simple
                    x, y = map(int, block[0].split(','))
                    if element_type == 'ROWS':
                        insert_positions.add(x if position == 'ABOVE' else x + 1)
                    else:  # COLUMNS
                        insert_positions.add(y if position == 'BEFORE' else y + 1)
                elif len(block) == 2:
                    # Rectangle
                    x1, y1 = map(int, block[0].split(','))
                    x2, y2 = map(int, block[1].split(','))

                    if element_type == 'ROWS':
                        if position == 'ABOVE':
                            insert_positions.add(min(x1, x2))
                        else:  # BELOW
                            insert_positions.add(max(x1, x2) + 1)
                    else:  # COLUMNS
                        if position == 'BEFORE':
                            insert_positions.add(min(y1, y2))
                        else:  # AFTER
                            insert_positions.add(max(y1, y2) + 1)

            # Trier les positions en ordre décroissant pour éviter les décalages
            sorted_positions = sorted(insert_positions, reverse=True)

            # Insérer à chaque position
            for pos in sorted_positions:
                if not self._insert_single(count, element_type, pos):
                    return False

            return True

        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Erreur dans _insert_at_coordinates: {e}"
            return False

    def _insert_global(self, count: int, element_type: str, position: str) -> bool:
        """Insère des lignes/colonnes au début ou à la fin de la grille"""
        try:
            if element_type == 'ROWS':
                insert_pos = 0 if position == 'ABOVE' else self.height
            else:  # COLUMNS
                insert_pos = 0 if position == 'BEFORE' else self.width

            return self._insert_single(count, element_type, insert_pos)

        except Exception as e:
            self.error = f"Erreur dans _insert_global: {e}"
            return False

    def _insert_single(self, count: int, element_type: str, position: int) -> bool:
        """Insère count lignes/colonnes à la position spécifiée"""
        try:
            if element_type == 'ROWS':
                # Vérifier les limites
                if position < 0 or position > self.height:
                    self.error = f"Position d'insertion de ligne invalide: {position}"
                    return False

                # Créer une nouvelle grille avec plus de lignes
                new_height = self.height + count
                new_grid = np.zeros((new_height, self.width), dtype=int)

                # Copier les lignes avant la position d'insertion
                if position > 0:
                    new_grid[:position, :] = self.grid[:position, :]

                # Copier les lignes après la position d'insertion (décalées)
                if position < self.height:
                    new_grid[position + count:, :] = self.grid[position:, :]

                # Mettre à jour la grille et les dimensions
                self.grid = new_grid
                self.height = new_height

            else:  # COLUMNS
                # Vérifier les limites
                if position < 0 or position > self.width:
                    self.error = f"Position d'insertion de colonne invalide: {position}"
                    return False

                # Créer une nouvelle grille avec plus de colonnes
                new_width = self.width + count
                new_grid = np.zeros((self.height, new_width), dtype=int)

                # Copier les colonnes avant la position d'insertion
                if position > 0:
                    new_grid[:, :position] = self.grid[:, :position]

                # Copier les colonnes après la position d'insertion (décalées)
                if position < self.width:
                    new_grid[:, position + count:] = self.grid[:, position:]

                # Mettre à jour la grille et les dimensions
                self.grid = new_grid
                self.width = new_width

            return True

        except Exception as e:
            self.error = f"Erreur dans _insert_single: {e}"
            return False

    def _delete_at_coordinates(self, element_type: str, cmd) -> bool:
        """Supprime des lignes/colonnes aux positions spécifiées par les coordonnées"""
        try:
            # Utiliser la fonction générique pour parser les blocs de coordonnées depuis raw_command
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: traiter les coordonnées par paires de 4
                coordinates = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coordinates):
                    x1, y1, x2, y2 = coordinates[i], coordinates[i+1], coordinates[i+2], coordinates[i+3]
                    coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4

            # Collecter toutes les positions de suppression
            delete_positions = set()

            for block in coordinate_blocks:
                if len(block) == 1:
                    # Cellule simple
                    x, y = map(int, block[0].split(','))
                    if element_type == 'ROWS':
                        delete_positions.add(x)
                    else:  # COLUMNS
                        delete_positions.add(y)
                elif len(block) == 2:
                    # Rectangle
                    x1, y1 = map(int, block[0].split(','))
                    x2, y2 = map(int, block[1].split(','))

                    if element_type == 'ROWS':
                        # Pour les lignes, utiliser les coordonnées x (lignes)
                        for row in range(min(x1, x2), max(x1, x2) + 1):
                            delete_positions.add(row)
                    else:  # COLUMNS
                        # Pour les colonnes, utiliser les coordonnées y (colonnes)
                        for col in range(min(y1, y2), max(y1, y2) + 1):
                            delete_positions.add(col)

            # Vérifier que les positions sont valides
            if element_type == 'ROWS':
                invalid_positions = [pos for pos in delete_positions if pos < 0 or pos >= self.height]
                if invalid_positions:
                    self.error = f"Positions de lignes invalides: {invalid_positions}"
                    return False

                # Vérifier qu'on ne supprime pas toutes les lignes
                if len(delete_positions) >= self.height:
                    self.error = "Impossible de supprimer toutes les lignes de la grille"
                    return False

            else:  # COLUMNS
                invalid_positions = [pos for pos in delete_positions if pos < 0 or pos >= self.width]
                if invalid_positions:
                    self.error = f"Positions de colonnes invalides: {invalid_positions}"
                    return False

                # Vérifier qu'on ne supprime pas toutes les colonnes
                if len(delete_positions) >= self.width:
                    self.error = "Impossible de supprimer toutes les colonnes de la grille"
                    return False

            # Trier les positions en ordre décroissant pour éviter les décalages
            sorted_positions = sorted(delete_positions, reverse=True)

            # Supprimer chaque position
            for pos in sorted_positions:
                if not self._delete_single(element_type, pos):
                    return False

            return True

        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Erreur dans _delete_at_coordinates: {e}"
            return False

    def _delete_single(self, element_type: str, position: int) -> bool:
        """Supprime une seule ligne/colonne à la position spécifiée"""
        try:
            if element_type == 'ROWS':
                # Vérifier les limites
                if position < 0 or position >= self.height:
                    self.error = f"Position de ligne invalide: {position}"
                    return False

                if self.height <= 1:
                    self.error = "Impossible de supprimer la dernière ligne"
                    return False

                # Créer une nouvelle grille sans cette ligne
                new_height = self.height - 1
                new_grid = np.zeros((new_height, self.width), dtype=int)

                # Copier les lignes avant la position
                if position > 0:
                    new_grid[:position, :] = self.grid[:position, :]

                # Copier les lignes après la position
                if position < self.height - 1:
                    new_grid[position:, :] = self.grid[position + 1:, :]

                # Mettre à jour la grille et les dimensions
                self.grid = new_grid
                self.height = new_height

            else:  # COLUMNS
                # Vérifier les limites
                if position < 0 or position >= self.width:
                    self.error = f"Position de colonne invalide: {position}"
                    return False

                if self.width <= 1:
                    self.error = "Impossible de supprimer la dernière colonne"
                    return False

                # Créer une nouvelle grille sans cette colonne
                new_width = self.width - 1
                new_grid = np.zeros((self.height, new_width), dtype=int)

                # Copier les colonnes avant la position
                if position > 0:
                    new_grid[:, :position] = self.grid[:, :position]

                # Copier les colonnes après la position
                if position < self.width - 1:
                    new_grid[:, position:] = self.grid[:, position + 1:]

                # Mettre à jour la grille et les dimensions
                self.grid = new_grid
                self.width = new_width

            return True

        except Exception as e:
            self.error = f"Erreur dans _delete_single: {e}"
            return False

    def _cmd_extract(self, cmd: UnifiedCommand) -> bool:
        """Extrait une zone de la grille"""
        if len(cmd.coordinates) != 4:
            return False

        if self.grid is None:
            return False

        try:
            x1, y1, x2, y2 = cmd.coordinates
            
            # S'assurer que x1 <= x2 et y1 <= y2
            x1, x2 = min(x1, x2), max(x1, x2)
            y1, y2 = min(y1, y2), max(y1, y2)
            
            # Vérifier les limites
            if x1 < 0 or x2 >= self.height or y1 < 0 or y2 >= self.width:
                return False
            
            # Extraire la zone
            extracted = self.grid[x1:x2+1, y1:y2+1].copy()
            
            # Remplacer la grille par la zone extraite
            self.grid = extracted
            self.height, self.width = extracted.shape
            
            return True
        except (ValueError, TypeError, IndexError):
            return False

    # === COMMANDES DE SÉLECTION SPÉCIALES ===


    # === COMMANDES SYSTÈME ===

    def _cmd_input(self, cmd: UnifiedCommand) -> bool:
        """Traite une commande INPUT pour charger la grille d'input du test"""
        # La commande INPUT ne prend pas de paramètres
        if len(cmd.parameters) != 0:
            self.error = "La commande INPUT ne doit pas avoir de paramètres"
            return False

        # Vérifier que nous avons les données de la tâche
        if not self.task_data:
            self.error = "Aucune donnée de tâche disponible pour la commande INPUT"
            return False

        # Vérifier que nous avons des tests
        if 'test' not in self.task_data or not self.task_data['test']:
            self.error = "Aucun test disponible dans les données de la tâche"
            return False

        # Vérifier que l'index de test est valide
        if self.test_index < 0 or self.test_index >= len(self.task_data['test']):
            self.error = f"Index de test invalide: {self.test_index} (max: {len(self.task_data['test']) - 1})"
            return False

        # Récupérer la grille d'input du test spécifié
        test_data = self.task_data['test'][self.test_index]
        if 'input' not in test_data:
            self.error = f"Aucune grille d'input dans le test {self.test_index}"
            return False

        # Charger la grille d'input
        import numpy as np
        try:
            input_grid = np.array(test_data['input'])
            self.grid = input_grid
            self.height = len(input_grid)
            self.width = len(input_grid[0]) if len(input_grid) > 0 else 0

            # Ajouter à l'historique
            self.history.append(f"INPUT: Grille d'input du test {self.test_index} chargée ({self.height}x{self.width})")

            return True
        except Exception as e:
            self.error = f"Erreur lors du chargement de la grille d'input: {str(e)}"
            return False



    def _cmd_floodfill(self, cmd: UnifiedCommand) -> bool:
        """Remplit une zone connectée avec une couleur donnée"""
        if len(cmd.parameters) != 1:
            return False

        if self.grid is None:
            return False

        try:
            new_color = int(cmd.parameters[0])
            
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: traiter les coordonnées par paires (x, y)
                coords = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 1 < len(coords):
                    x, y = coords[i], coords[i+1]
                    coordinate_blocks.append([f"{x},{y}"])
                    i += 2
            
            #print(f"[FLOODFILL] Traitement de {len(coordinate_blocks)} points de départ: {coordinate_blocks}")
            
            # Pour le flood fill, on traite chaque cellule comme point de départ
            for block in coordinate_blocks:
                if len(block) == 1:
                    # Point de départ simple
                    try:
                        coord_parts = block[0].split(',')
                        if len(coord_parts) == 2:
                            x, y = int(coord_parts[0]), int(coord_parts[1])
                            if (0 <= x < self.height and 0 <= y < self.width):
                                self._flood_fill_from_point(x, y, new_color)
                    except (ValueError, IndexError):
                        continue
                elif len(block) == 2:
                    # Rectangle : flood fill à partir de chaque cellule du rectangle
                    try:
                        coord1_parts = block[0].split(',')
                        coord2_parts = block[1].split(',')
                        if len(coord1_parts) == 2 and len(coord2_parts) == 2:
                            x1, y1 = int(coord1_parts[0]), int(coord1_parts[1])
                            x2, y2 = int(coord2_parts[0]), int(coord2_parts[1])
                            
                            if (0 <= x1 < self.height and 0 <= x2 < self.height and
                                0 <= y1 < self.width and 0 <= y2 < self.width):
                                # Normaliser les coordonnées
                                min_x, max_x = min(x1, x2), max(x1, x2)
                                min_y, max_y = min(y1, y2), max(y1, y2)
                                
                                # Flood fill à partir de chaque cellule du rectangle
                                for x in range(min_x, max_x + 1):
                                    for y in range(min_y, max_y + 1):
                                        self._flood_fill_from_point(x, y, new_color)
                    except (ValueError, IndexError):
                        continue
            
            return True
            
        except (ValueError, TypeError, IndexError) as e:
            print(f"[FLOODFILL] Erreur: {e}")
            return False

    def _flood_fill_from_point(self, start_x: int, start_y: int, new_color: int):
        """Effectue un flood fill à partir d'un point donné"""
        # Couleur d'origine
        original_color = self.grid[start_x, start_y]
        
        # Si la couleur est déjà la bonne, ne rien faire
        if original_color == new_color:
            return
        
        #print(f"[FLOODFILL] Début flood fill à partir de ({start_x},{start_y}): {original_color} -> {new_color}")
        
        # Algorithme de flood fill avec une queue
        queue = [(start_x, start_y)]
        visited = set()
        
        while queue:
            cur_x, cur_y = queue.pop(0)
            
            # Vérifier si déjà visité ou hors limites
            if (cur_x, cur_y) in visited:
                continue
            if cur_x < 0 or cur_x >= self.height or cur_y < 0 or cur_y >= self.width:
                continue
            if self.grid[cur_x, cur_y] != original_color:
                continue
            
            # Marquer comme visité et changer la couleur
            visited.add((cur_x, cur_y))
            self.grid[cur_x, cur_y] = new_color
            
            # Ajouter les voisins à la queue
            queue.append((cur_x + 1, cur_y))  # Bas
            queue.append((cur_x - 1, cur_y))  # Haut
            queue.append((cur_x, cur_y + 1))  # Droite
            queue.append((cur_x, cur_y - 1))  # Gauche

    def _flip_vertical_with_mask(self, cmd: UnifiedCommand) -> bool:
        """Applique FLIP VERTICAL en respectant le masque de sélection"""
        special = cmd.special_selection
        colors = special["params"]
        coords = special["coords"]
        
        if len(coords) != 4:
            return False
            
        x1, y1, x2, y2 = coords
        x1, x2 = min(x1, x2), max(x1, x2)
        y1, y2 = min(y1, y2), max(y1, y2)
        
        if x1 < 0 or x2 >= self.height or y1 < 0 or y2 >= self.width:
            return False
        
        # Créer le masque des cellules sélectionnées dans la région spécifiée
        region = self.grid[x1:x2+1, y1:y2+1]
        mask = np.isin(region, colors)
        
        # Extraire seulement les cellules sélectionnées
        selected_values = region[mask]
        
        if len(selected_values) == 0:
            return True  # Rien à transformer
        
        # Créer une copie de la région pour la transformation
        region_copy = region.copy()
        
        # Appliquer le flip vertical seulement aux cellules sélectionnées
        # 1. Extraire les positions des cellules sélectionnées
        selected_positions = np.where(mask)
        
        # 2. Calculer les nouvelles positions après flip vertical
        height = region.shape[0]
        
        # 3. Sauvegarder les valeurs sélectionnées et leurs nouvelles positions
        old_values = []
        new_positions = []
        
        for old_y, old_x in zip(selected_positions[0], selected_positions[1]):
            new_y = height - 1 - old_y
            new_x = old_x
            if 0 <= new_y < height and 0 <= new_x < region.shape[1]:
                old_values.append(region[old_y, old_x])
                new_positions.append((new_y, new_x))
        
        # 4. Sauvegarder les cellules non-sélectionnées dans la région
        non_selected_backup = {}
        for y in range(region.shape[0]):
            for x in range(region.shape[1]):
                if not mask[y, x]:  # Cellule non-sélectionnée
                    non_selected_backup[(y, x)] = region[y, x]
        
        # 5. Nettoyer seulement les anciennes positions des cellules sélectionnées
        for old_y, old_x in zip(selected_positions[0], selected_positions[1]):
            self.grid[x1 + old_y, y1 + old_x] = 0
        
        # 6. Placer les valeurs aux nouvelles positions
        for value, (new_y, new_x) in zip(old_values, new_positions):
            self.grid[x1 + new_y, y1 + new_x] = value
        
        # 7. Restaurer les cellules non-sélectionnées
        for (y, x), value in non_selected_backup.items():
            self.grid[x1 + y, y1 + x] = value
        
        return True

    def _flip_horizontal_with_mask(self, cmd: UnifiedCommand) -> bool:
        """Applique FLIP HORIZONTAL en respectant le masque de sélection"""
        special = cmd.special_selection
        colors = special["params"]
        coords = special["coords"]
        
        if len(coords) != 4:
            return False
            
        x1, y1, x2, y2 = coords
        x1, x2 = min(x1, x2), max(x1, x2)
        y1, y2 = min(y1, y2), max(y1, y2)
        
        if x1 < 0 or x2 >= self.height or y1 < 0 or y2 >= self.width:
            return False
        
        # Créer le masque des cellules sélectionnées dans la région spécifiée
        region = self.grid[x1:x2+1, y1:y2+1]
        mask = np.isin(region, colors)
        
        # Extraire seulement les cellules sélectionnées
        selected_values = region[mask]
        
        if len(selected_values) == 0:
            return True  # Rien à transformer
        
        # Créer une copie de la région pour la transformation
        region_copy = region.copy()
        
        # Appliquer le flip horizontal seulement aux cellules sélectionnées
        # 1. Extraire les positions des cellules sélectionnées
        selected_positions = np.where(mask)
        
        # 2. Calculer les nouvelles positions après flip horizontal
        width = region.shape[1]
        
        # 3. Sauvegarder les valeurs sélectionnées et leurs nouvelles positions
        old_values = []
        new_positions = []
        
        for old_y, old_x in zip(selected_positions[0], selected_positions[1]):
            new_y = old_y
            new_x = width - 1 - old_x
            if 0 <= new_y < region.shape[0] and 0 <= new_x < width:
                old_values.append(region[old_y, old_x])
                new_positions.append((new_y, new_x))
        
        # 4. Sauvegarder les cellules non-sélectionnées dans la région
        non_selected_backup = {}
        for y in range(region.shape[0]):
            for x in range(region.shape[1]):
                if not mask[y, x]:  # Cellule non-sélectionnée
                    non_selected_backup[(y, x)] = region[y, x]
        
        # 5. Nettoyer seulement les anciennes positions des cellules sélectionnées
        for old_y, old_x in zip(selected_positions[0], selected_positions[1]):
            self.grid[x1 + old_y, y1 + old_x] = 0
        
        # 6. Placer les valeurs aux nouvelles positions
        for value, (new_y, new_x) in zip(old_values, new_positions):
            self.grid[x1 + new_y, y1 + new_x] = value
        
        # 7. Restaurer les cellules non-sélectionnées
        for (y, x), value in non_selected_backup.items():
            self.grid[x1 + y, y1 + x] = value
        
        return True

    def _rotate_left_with_mask(self, cmd: UnifiedCommand) -> bool:
        """Applique ROTATE LEFT en respectant le masque de sélection"""
        special = cmd.special_selection
        colors = special["params"]
        coords = special["coords"]
        
        if len(coords) != 4:
            return False
            
        x1, y1, x2, y2 = coords
        x1, x2 = min(x1, x2), max(x1, x2)
        y1, y2 = min(y1, y2), max(y1, y2)
        
        if x1 < 0 or x2 >= self.height or y1 < 0 or y2 >= self.width:
            return False
        
        # Créer le masque des cellules sélectionnées dans la région spécifiée
        region = self.grid[x1:x2+1, y1:y2+1]
        mask = np.isin(region, colors)
        
        # Extraire seulement les cellules sélectionnées
        selected_values = region[mask]
        
        if len(selected_values) == 0:
            return True  # Rien à transformer
        
        # Créer une copie de la région pour la transformation
        region_copy = region.copy()
        
        # Appliquer la rotation gauche seulement aux cellules sélectionnées
        # 1. Extraire les positions des cellules sélectionnées
        selected_positions = np.where(mask)
        
        # 2. Calculer les nouvelles positions après rotation gauche (90° antihoraire)
        height, width = region.shape
        
        # 3. Sauvegarder les valeurs sélectionnées et leurs nouvelles positions
        old_values = []
        new_positions = []
        
        # Rotation gauche: (y, x) -> (width-1-x, y)
        for old_y, old_x in zip(selected_positions[0], selected_positions[1]):
            new_y = width - 1 - old_x
            new_x = old_y
            # Vérifier que la nouvelle position est dans les limites de la région
            if 0 <= new_y < height and 0 <= new_x < width:
                old_values.append(region[old_y, old_x])
                new_positions.append((new_y, new_x))
        
        # 4. Sauvegarder les cellules non-sélectionnées dans la région
        non_selected_backup = {}
        for y in range(region.shape[0]):
            for x in range(region.shape[1]):
                if not mask[y, x]:  # Cellule non-sélectionnée
                    non_selected_backup[(y, x)] = region[y, x]
        
        # 5. Nettoyer seulement les anciennes positions des cellules sélectionnées
        for old_y, old_x in zip(selected_positions[0], selected_positions[1]):
            self.grid[x1 + old_y, y1 + old_x] = 0
        
        # 6. Placer les valeurs aux nouvelles positions
        for value, (new_y, new_x) in zip(old_values, new_positions):
            self.grid[x1 + new_y, y1 + new_x] = value
        
        # 7. Restaurer les cellules non-sélectionnées
        for (y, x), value in non_selected_backup.items():
            self.grid[x1 + y, y1 + x] = value
        
        return True

    def _cmd_propose(self, cmd: UnifiedCommand) -> bool:
        """Propose la solution actuelle"""
        if self.grid is None:
            return False
        # Cette commande ne fait rien, elle indique simplement que la solution est complète
        return True

    def _cmd_motif(self, cmd: UnifiedCommand) -> bool:
        """
        Exécute un motif (séquence de commandes groupées)
        Format: MOTIF {COPY [0,0 2,2]; ROTATE RIGHT [0,3 2,5]; PASTE [0,3]}
        """
        if not hasattr(cmd, 'raw_command') or not cmd.raw_command:
            self.error = "MOTIF nécessite une commande brute avec des sous-commandes"
            return False

        # Extraire les sous-commandes entre accolades
        import re
        motif_match = re.search(r'MOTIF\s*\{([^}]+)\}', cmd.raw_command)
        if not motif_match:
            self.error = "Format MOTIF invalide - utilisez MOTIF {commande1; commande2; ...}"
            return False

        subcommands_str = motif_match.group(1)
        subcommands = [cmd.strip() for cmd in subcommands_str.split(';') if cmd.strip()]

        # Exécuter chaque sous-commande
        for subcmd in subcommands:
            if not self._execute_command(subcmd):
                self.error = f"Erreur dans le motif lors de l'exécution de: {subcmd}"
                return False

        return True

    def _cmd_multiply(self, cmd: UnifiedCommand) -> bool:
        """
        Multiplie le contenu du presse-papier par un facteur
        Format: MULTIPLY factor [replace_content] ([coordonnées])
        Utilise le système de parsing générique des coordonnées
        """
        if not cmd.parameters or len(cmd.parameters) < 1:
            self.error = "MULTIPLY nécessite un facteur de multiplication"
            return False

        if self.clipboard is None:
            self.error = "MULTIPLY nécessite une commande COPY préalable"
            return False

        try:
            factor = int(cmd.parameters[0])
            if factor < 2:
                self.error = "Le facteur de multiplication doit être >= 2"
                return False
        except ValueError:
            self.error = "Le facteur de multiplication doit être un entier"
            return False

        # Vérifier si le paramètre 'replace_content' est présent
        replace_content = False  # Par défaut false selon la documentation
        if len(cmd.parameters) >= 2:
            try:
                replace_param = str(cmd.parameters[1]).lower()
                if replace_param in ['true', '1', 'yes']:
                    replace_content = True
                elif replace_param in ['false', '0', 'no']:
                    replace_content = False
                else:
                    self.error = f"Paramètre replace_content invalide: {replace_param}. Utilisez true/false"
                    return False
            except (ValueError, AttributeError):
                self.error = "Le paramètre replace_content doit être true ou false"
                return False

        # Obtenir les dimensions actuelles du clipboard
        clip_height, clip_width = self.clipboard.shape

        # Calculer les nouvelles dimensions
        new_height = clip_height * factor
        new_width = clip_width * factor

        # Créer le nouveau clipboard multiplié
        new_clipboard = np.zeros((new_height, new_width), dtype=int)
        new_clipboard_mask = np.zeros((new_height, new_width), dtype=bool)

        # NOUVEAU: Utiliser le système de parsing générique des coordonnées
        # Si des coordonnées sont spécifiées, elles définissent quelles cellules du clipboard multiplier
        if hasattr(cmd, 'raw_command') and cmd.raw_command and ('[' in cmd.raw_command):
            coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            
            if coordinate_blocks:
                # Créer un masque pour les cellules à multiplier
                multiply_mask = np.zeros((clip_height, clip_width), dtype=bool)
                
                # Fonction pour marquer les cellules à multiplier
                def mark_multiply_cells(x1, y1, x2, y2):
                    # Vérifier les limites du clipboard
                    if (0 <= x1 < clip_height and 0 <= x2 < clip_height and
                        0 <= y1 < clip_width and 0 <= y2 < clip_width):
                        multiply_mask[x1:x2+1, y1:y2+1] = True
                
                # Traiter les blocs de coordonnées
                self._process_coordinate_blocks(coordinate_blocks, mark_multiply_cells)
                
                # Utiliser le masque créé au lieu du clipboard_mask original
                effective_mask = multiply_mask
            else:
                # Pas de coordonnées spécifiées, utiliser le masque original
                effective_mask = self.clipboard_mask if self.clipboard_mask is not None else np.ones((clip_height, clip_width), dtype=bool)
        else:
            # Pas de coordonnées spécifiées, utiliser le masque original
            effective_mask = self.clipboard_mask if self.clipboard_mask is not None else np.ones((clip_height, clip_width), dtype=bool)

        if replace_content:
            # Mode replace_content=True : remplacer le contenu dans chaque rectangle correspondant aux cellules sélectionnées
            # Chaque cellule sélectionnée devient un bloc factor x factor avec le motif entier
            for i in range(clip_height):
                for j in range(clip_width):
                    if effective_mask[i, j]:
                        # Cette cellule était sélectionnée, la remplacer par le motif entier factor x factor fois
                        for fi in range(factor):
                            for fj in range(factor):
                                new_row = i * factor + fi
                                new_col = j * factor + fj

                                # Utiliser le motif original pour remplir ce bloc
                                motif_row = fi % clip_height
                                motif_col = fj % clip_width
                                new_clipboard[new_row, new_col] = self.clipboard[motif_row, motif_col]
                                new_clipboard_mask[new_row, new_col] = True
        else:
            # Mode replace_content=False : multiplier seulement les cellules du masque
            for i in range(clip_height):
                for j in range(clip_width):
                    if effective_mask[i, j]:
                        # Cette cellule était sélectionnée, la répliquer factor x factor fois
                        for fi in range(factor):
                            for fj in range(factor):
                                new_row = i * factor + fi
                                new_col = j * factor + fj
                                new_clipboard[new_row, new_col] = self.clipboard[i, j]
                                new_clipboard_mask[new_row, new_col] = True

        # Remplacer le clipboard par la version multipliée
        self.clipboard = new_clipboard
        self.clipboard_mask = new_clipboard_mask

        return True

    def _cmd_divide(self, unified_cmd: UnifiedCommand) -> bool:
        """Divise le contenu du presse-papier par un facteur (échantillonnage)."""
        if not unified_cmd.parameters:
            self.error = "La commande DIVIDE nécessite un facteur de division"
            return False

        try:
            factor = int(unified_cmd.parameters[0])
            if factor < 2:
                self.error = "Le facteur de division doit être >= 2"
                return False

            # DIVIDE opère uniquement sur le presse-papier
            if self.clipboard is None:
                self.error = "DIVIDE nécessite une commande COPY préalable pour avoir du contenu dans le presse-papier."
                return False

            # Diviser le contenu du presse-papier par échantillonnage
            original_height, original_width = self.clipboard.shape
            
            if original_height % factor != 0 or original_width % factor != 0:
                self.error = f"Les dimensions du presse-papier ({original_height}x{original_width}) ne sont pas divisibles par {factor}"
                return False

            new_height = original_height // factor
            new_width = original_width // factor

            # Créer la nouvelle grille divisée par échantillonnage
            divided_clipboard = np.zeros((new_height, new_width), dtype=int)

            # Échantillonner le contenu original
            for i in range(new_height):
                for j in range(new_width):
                    # Prendre la première cellule de chaque bloc factor x factor
                    source_row = i * factor
                    source_col = j * factor
                    divided_clipboard[i, j] = self.clipboard[source_row, source_col]

            # Mettre à jour le presse-papier
            self.clipboard = divided_clipboard
            if self.clipboard_mask is not None:
                # Diviser aussi le masque
                divided_mask = np.zeros((new_height, new_width), dtype=bool)
                for i in range(new_height):
                    for j in range(new_width):
                        source_row = i * factor
                        source_col = j * factor
                        divided_mask[i, j] = self.clipboard_mask[source_row, source_col]
                self.clipboard_mask = divided_mask

            return True

        except (ValueError, TypeError) as e:
            self.error = f"Erreur dans DIVIDE: {e}"
            return False

    def _cmd_end(self, cmd: UnifiedCommand) -> bool:
        """Termine l'exécution"""
        return True



    # ==================================================================
    # FONCTIONS GÉNÉRIQUES POUR PARSER LES BLOCS DE COORDONNÉES
    # ==================================================================

    def _parse_coordinate_blocks(self, raw_command: str) -> List[List[str]]:
        """
        Parse les blocs de coordonnées depuis la commande brute
        Retourne un tableau où chaque élément est un bloc de coordonnées :
        - [coord] pour une cellule simple
        - [coord1, coord2] pour un rectangle
        - ['INVERT', coord1, coord2] pour un modificateur INVERT
        - ['COLOR', params, coord1, coord2] pour un modificateur COLOR
        """
        coordinate_blocks = []
        
        if not raw_command:
            return coordinate_blocks
        
        # Vérifier d'abord les nouveaux formats avec modificateurs
        import re
        
        # Détecter (INVERT ([...])) ou (INVERT [...]) ou ancien format SELECT_INVERT([...])
        invert_pattern_double = r'\(INVERT\s*\(([^)]+)\)\)'
        invert_pattern_single = r'\(INVERT\s*\[([^\]]+)\]\)'
        # Support ancien format SELECT_INVERT
        legacy_invert_pattern = r'SELECT_INVERT\s*\(([^)]+)\)'
        
        invert_match = re.search(invert_pattern_double, raw_command)
        invert_content = None
        
        if invert_match:
            invert_content = invert_match.group(1)
        else:
            invert_match = re.search(invert_pattern_single, raw_command)
            if invert_match:
                invert_content = f'[{invert_match.group(1)}]'  # Remettre les crochets pour uniformiser
            else:
                # Vérifier l'ancien format SELECT_INVERT
                invert_match = re.search(legacy_invert_pattern, raw_command)
                if invert_match:
                    invert_content = invert_match.group(1)  # Contenu déjà avec crochets
        
        if invert_content:
            # Extraire TOUTES les coordonnées pour créer UN SEUL bloc INVERT
            coord_pattern = r'\[([^\]]+)\]'
            coord_matches = re.findall(coord_pattern, invert_content)
            all_coords = []
            for coord_match in coord_matches:
                if ' ' in coord_match:
                    coords = [c.strip() for c in coord_match.split() if c.strip()]
                    all_coords.extend(coords)
                else:
                    all_coords.append(coord_match)
            # Créer UN SEUL bloc INVERT avec toutes les coordonnées
            if all_coords:
                coordinate_blocks.append(['INVERT'] + all_coords)
            return coordinate_blocks
        
        # Détecter les opérateurs XOR, AND, OR
        xor_pattern = r'\(XOR\s*\(([^,]+),\s*([^)]+)\)\)'
        and_pattern = r'\(AND\s*\(([^)]+)\)\)'
        or_pattern = r'\(OR\s*\(([^)]+)\)\)'
        
        # Vérifier XOR
        xor_match = re.search(xor_pattern, raw_command)
        if xor_match:
            operand1 = xor_match.group(1).strip()
            operand2 = xor_match.group(2).strip()
            resolved_coords = resolve_coordinate_operator(raw_command, self.height, self.width)
            if resolved_coords:
                # Convertir les coordonnées résolues en blocs
                coordinate_blocks = [[coord] for coord in resolved_coords]
            return coordinate_blocks
        
        # Vérifier AND
        and_match = re.search(and_pattern, raw_command)
        if and_match:
            resolved_coords = resolve_coordinate_operator(raw_command, self.height, self.width)
            if resolved_coords:
                # Convertir les coordonnées résolues en blocs
                coordinate_blocks = [[coord] for coord in resolved_coords]
            return coordinate_blocks
        
        # Vérifier OR
        or_match = re.search(or_pattern, raw_command)
        if or_match:
            resolved_coords = resolve_coordinate_operator(raw_command, self.height, self.width)
            if resolved_coords:
                # Convertir les coordonnées résolues en blocs
                coordinate_blocks = [[coord] for coord in resolved_coords]
            return coordinate_blocks

        # Détecter (COLOR params ([...])) ou (COLOR params [...]) ou COMMAND (COLOR params [...])
        color_pattern_double = r'\(COLOR\s+([^(]+)\s*\(([^)]+)\)\)'
        color_pattern_single = r'\(COLOR\s+([^\[]+)\s*\[([^\]]+)\]\)'
        color_pattern_with_command = r'\w+\s*\(COLOR\s+([^\[]+)\s*\[([^\]]+)\]\)'
        # Support ancien format SELECT_COLOR
        # legacy_color_pattern = r'SELECT_COLOR\s+([^(]+)\s*\(([^)]+)\)'
        
        color_match = re.search(color_pattern_double, raw_command)
        color_params = None
        color_content = None
        
        if color_match:
            color_params = color_match.group(1).strip()
            color_content = color_match.group(2)
        else:
            color_match = re.search(color_pattern_single, raw_command)
            if color_match:
                color_params = color_match.group(1).strip()
                color_content = f'[{color_match.group(2)}]'  # Remettre les crochets pour uniformiser
            else:
                # Nouveau: vérifier COMMAND (COLOR params [...])
                color_match = re.search(color_pattern_with_command, raw_command)
                if color_match:
                    color_params = color_match.group(1).strip()
                    color_content = f'[{color_match.group(2)}]'  # Remettre les crochets pour uniformiser
                # else:
                #     # Vérifier l'ancien format SELECT_COLOR
                #     color_match = re.search(legacy_color_pattern, raw_command)
                #     if color_match:
                #         color_params = color_match.group(1).strip()
                #         color_content = f'[{color_match.group(2)}]'  # Remettre les crochets pour uniformiser
        
        if color_params and color_content:
            # Extraire TOUTES les coordonnées pour créer UN SEUL bloc COLOR
            coord_pattern = r'\[([^\]]+)\]'
            coord_matches = re.findall(coord_pattern, color_content)
            all_coords = []
            for coord_match in coord_matches:
                if ' ' in coord_match:
                    coords = [c.strip() for c in coord_match.split() if c.strip()]
                    all_coords.extend(coords)
                else:
                    all_coords.append(coord_match)
            # Créer UN SEUL bloc COLOR avec toutes les coordonnées
            if all_coords:
                coordinate_blocks.append(['COLOR', color_params] + all_coords)
            return coordinate_blocks
        
        # Format traditionnel : extraire tous les blocs [...]
        block_pattern = r'\[([^\]]+)\]'
        matches = re.finditer(block_pattern, raw_command)
        
        for match in matches:
            block_content = match.group(1).strip()
            
            if ' ' in block_content:
                # Rectangle: "7,6 13,6" -> ["7,6", "13,6"]
                coords = [c.strip() for c in block_content.split() if c.strip()]
                coordinate_blocks.append(coords)
            else:
                # Cellule simple: "17,1" -> ["17,1"]
                coordinate_blocks.append([block_content])
        
        return coordinate_blocks

    def _process_coordinate_blocks(self, coordinate_blocks: List[List[str]], action_func):
        """
        Applique une action sur chaque bloc de coordonnées
        
        Args:
            coordinate_blocks: Blocs de coordonnées parsés
            action_func: Fonction à appliquer pour chaque bloc (x1, y1, x2, y2)
                        Reçoit les coordonnées normalisées (min, min, max, max)
        """
        
        for block in coordinate_blocks:
            # Détecter les modificateurs de coordonnées
            if len(block) > 0 and block[0] == 'INVERT':
                # Bloc avec modificateur INVERT - traiter toutes les coordonnées comme un ensemble
                coords = block[1:]  # Enlever le premier élément 'INVERT'
                
                # Créer un masque qui exclut toutes les zones spécifiées
                excluded_mask = np.zeros(self.grid.shape, dtype=bool)
                
                # Traiter les coordonnées par paires pour créer des rectangles/cellules
                for i in range(0, len(coords), 2):
                    try:
                        if i + 1 < len(coords):
                            # Rectangle
                            coord1_parts = coords[i].split(',')
                            coord2_parts = coords[i + 1].split(',')
                            if len(coord1_parts) == 2 and len(coord2_parts) == 2:
                                x1, y1 = int(coord1_parts[0]), int(coord1_parts[1])
                                x2, y2 = int(coord2_parts[0]), int(coord2_parts[1])
                                
                                if (0 <= x1 < self.grid.shape[0] and 0 <= x2 < self.grid.shape[0] and
                                    0 <= y1 < self.grid.shape[1] and 0 <= y2 < self.grid.shape[1]):
                                    min_x, max_x = min(x1, x2), max(x1, x2)
                                    min_y, max_y = min(y1, y2), max(y1, y2)
                                    # Marquer cette zone comme exclue
                                    excluded_mask[min_x:max_x+1, min_y:max_y+1] = True
                        else:
                            # Cellule simple
                            coord_parts = coords[i].split(',')
                            if len(coord_parts) == 2:
                                x, y = int(coord_parts[0]), int(coord_parts[1])
                                if (0 <= x < self.grid.shape[0] and 0 <= y < self.grid.shape[1]):
                                    excluded_mask[x, y] = True
                    except (ValueError, IndexError):
                        continue
                
                # Créer le masque final inversé
                final_mask = ~excluded_mask
                
                # Appliquer l'action à toutes les cellules du masque inversé
                original_grid = self.grid.copy()
                for x in range(self.grid.shape[0]):
                    for y in range(self.grid.shape[1]):
                        if final_mask[x, y]:
                            action_func(x, y, x, y)
                        
            elif len(block) > 0 and block[0] == 'COLOR':
                # Bloc avec modificateur COLOR
                if len(block) >= 3:
                    try:
                        color_params = block[1]  # Les paramètres de couleur
                        colors = [int(c.strip()) for c in color_params.split(',')]
                        coords = block[2:]  # Toutes les coordonnées après les paramètres
                        
                        matched_coordinates = []
                        
                        # Traiter les coordonnées par paires pour créer des rectangles/cellules
                        for i in range(0, len(coords), 2):
                            if i + 1 < len(coords):
                                # Rectangle avec COLOR
                                coord1_parts = coords[i].split(',')
                                coord2_parts = coords[i + 1].split(',')
                                if len(coord1_parts) == 2 and len(coord2_parts) == 2:
                                    x1, y1 = int(coord1_parts[0]), int(coord1_parts[1])
                                    x2, y2 = int(coord2_parts[0]), int(coord2_parts[1])
                                    
                                    if (0 <= x1 < self.grid.shape[0] and 0 <= x2 < self.grid.shape[0] and
                                        0 <= y1 < self.grid.shape[1] and 0 <= y2 < self.grid.shape[1]):
                                        min_x, max_x = min(x1, x2), max(x1, x2)
                                        min_y, max_y = min(y1, y2), max(y1, y2)
                                        
                                        # Appliquer l'action seulement aux cellules des couleurs spécifiées
                                        for x in range(min_x, max_x + 1):
                                            for y in range(min_y, max_y + 1):
                                                cell_color = self.grid[x, y]
                                                if cell_color in colors:
                                                    matched_coordinates.append(f"{x},{y}")
                                                    action_func(x, y, x, y)
                            else:
                                # Cellule simple avec COLOR
                                coord_parts = coords[i].split(',')
                                if len(coord_parts) == 2:
                                    x, y = int(coord_parts[0]), int(coord_parts[1])
                                    if (0 <= x < self.grid.shape[0] and 0 <= y < self.grid.shape[1]):
                                        cell_color = self.grid[x, y]
                                        if cell_color in colors:
                                            matched_coordinates.append(f"{x},{y}")
                                            action_func(x, y, x, y)
                        
                        if matched_coordinates:
                            coords_str = " ".join([f"[{coord}]" for coord in matched_coordinates])
                            # print(f"[COLOR] {coords_str}")
                        # else:
                            # print("[COLOR] []")
                        
                    except (ValueError, IndexError):
                        # print("[COLOR] []")
                        continue
                        
            elif len(block) == 1:
                # Cellule simple traditionnelle
                try:
                    coord_parts = block[0].split(',')
                    if len(coord_parts) == 2:
                        x, y = int(coord_parts[0]), int(coord_parts[1])
                        if (0 <= x < self.grid.shape[0] and 0 <= y < self.grid.shape[1]):
                            action_func(x, y, x, y)
                except (ValueError, IndexError):
                    continue
                    
            elif len(block) == 2:
                # Rectangle traditionnel
                try:
                    coord1_parts = block[0].split(',')
                    coord2_parts = block[1].split(',')
                    if len(coord1_parts) == 2 and len(coord2_parts) == 2:
                        x1, y1 = int(coord1_parts[0]), int(coord1_parts[1])
                        x2, y2 = int(coord2_parts[0]), int(coord2_parts[1])
                        
                        if (0 <= x1 < self.grid.shape[0] and 0 <= x2 < self.grid.shape[0] and
                            0 <= y1 < self.grid.shape[1] and 0 <= y2 < self.grid.shape[1]):
                            # Normaliser les coordonnées
                            min_x, max_x = min(x1, x2), max(x1, x2)
                            min_y, max_y = min(y1, y2), max(y1, y2)
                            action_func(min_x, min_y, max_x, max_y)
                except (ValueError, IndexError):
                    continue

    def _cmd_resize(self, cmd: UnifiedCommand) -> bool:
        """Redimensionne la grille - RESTAURÉ pour compatibilité avec les scénarios existants"""
        if len(cmd.parameters) != 1:
            return False

        try:
            size_str = str(cmd.parameters[0])
            if 'x' in size_str:
                width, height = map(int, size_str.split('x'))
            else:
                return False

            if width <= 0 or height <= 0:
                return False

            # Créer une nouvelle grille
            new_grid = np.zeros((height, width), dtype=int)

            # Copier l'ancienne grille si elle existe
            if self.grid is not None:
                min_h = min(self.height, height)
                min_w = min(self.width, width)
                new_grid[:min_h, :min_w] = self.grid[:min_h, :min_w]

            # Mettre à jour la grille et les dimensions
            self.grid = new_grid
            self.width = width
            self.height = height

            return True

        except (ValueError, TypeError):
            return False

    # ==================================================================
    # SYSTÈME MODERNE - PLUS DE MÉTHODES LEGACY
    # ==================================================================
    # === OPÉRATEURS BINAIRES ===

    def _cmd_and(self, cmd: UnifiedCommand) -> bool:
        """Exécute l'opérateur AND entre plusieurs zones - compatible avec le frontend"""
        print(f"[DEBUG] _cmd_and appelée avec: {cmd.raw_command}")
        if self.grid is None:
            self.error = "La grille n'a pas été initialisée."
            return False

        try:
            # Parser les coordonnées directement depuis la commande brute
            # Format: AND ([0,0 2,2] [0,4 2,6]) -> extraire [0,0 2,2] et [0,4 2,6]
            import re
            raw_command = cmd.raw_command if hasattr(cmd, 'raw_command') else str(cmd)
            coord_pattern = r'\[([^\]]+)\]'
            coord_matches = re.findall(coord_pattern, raw_command)

            print(f"[DEBUG] _cmd_and coordonnées extraites: {coord_matches}")
            if len(coord_matches) < 2:
                self.error = f"AND nécessite au moins 2 zones, trouvées: {len(coord_matches)}"
                return False

            # Parser les rectangles depuis les coordonnées (comme le frontend)
            rectangles = []
            for coord_match in coord_matches:
                # Extraire les coordonnées individuelles
                coords = []
                if ' ' in coord_match:
                    # Rectangle : "0,0 2,2" -> [(0,0), (2,2)]
                    parts = coord_match.split()
                    start_coords = list(map(int, parts[0].split(',')))
                    end_coords = list(map(int, parts[1].split(',')))
                    coords = [start_coords, end_coords]
                else:
                    # Cellule simple : "0,0" -> [(0,0)]
                    coords = [list(map(int, coord_match.split(',')))]

                if len(coords) == 2:
                    # Rectangle
                    start_row, start_col = coords[0]
                    end_row, end_col = coords[1]
                    rectangles.append({
                        'start_row': min(start_row, end_row),
                        'start_col': min(start_col, end_col),
                        'end_row': max(start_row, end_row),
                        'end_col': max(start_col, end_col)
                    })
                else:
                    # Cellule simple
                    row, col = coords[0]
                    rectangles.append({
                        'start_row': row,
                        'start_col': col,
                        'end_row': row,
                        'end_col': col
                    })

            print(f"[DEBUG] _cmd_and rectangles parsés: {rectangles}")

            # Vérifier que toutes les zones ont la même taille (comme dans le frontend)
            first_rect = rectangles[0]
            width = first_rect['end_col'] - first_rect['start_col'] + 1
            height = first_rect['end_row'] - first_rect['start_row'] + 1

            for i, rect in enumerate(rectangles[1:], 1):
                rect_width = rect['end_col'] - rect['start_col'] + 1
                rect_height = rect['end_row'] - rect['start_row'] + 1
                if rect_width != width or rect_height != height:
                    self.error = f"Toutes les zones doivent avoir la même taille. Zone 0: {width}x{height}, Zone {i}: {rect_width}x{rect_height}"
                    return False

            print(f"[DEBUG] _cmd_and traitement zone {width}x{height}")

            # Appliquer l'opération cellule par cellule (comme dans le frontend)
            for row in range(height):
                for col in range(width):
                    first_zone_row = first_rect['start_row'] + row
                    first_zone_col = first_rect['start_col'] + col

                    # Obtenir la valeur de la première zone
                    result_value = self.grid[first_zone_row, first_zone_col]

                    # Appliquer l'opération avec toutes les autres zones
                    for i in range(1, len(rectangles)):
                        other_rect = rectangles[i]
                        other_zone_row = other_rect['start_row'] + row
                        other_zone_col = other_rect['start_col'] + col
                        other_value = self.grid[other_zone_row, other_zone_col]

                        # Appliquer l'opération binaire AND (0=fond, 1-9=valeur)
                        # AND: résultat = 0 si au moins une cellule est 0, sinon préserver la première valeur non-nulle
                        if result_value == 0 or other_value == 0:
                            result_value = 0  # Si l'une des cellules est 0, le résultat est 0
                        # Si les deux sont non-nulles, on garde result_value (première valeur non-nulle)

                    # Écrire le résultat dans la première zone
                    self.grid[first_zone_row, first_zone_col] = result_value

            print("[DEBUG] _cmd_and: opération terminée")
            return True

        except Exception as e:
            self.error = f"Erreur lors de l'exécution de AND: {str(e)}"
            return False

    def _cmd_or(self, cmd: UnifiedCommand) -> bool:
        """Exécute l'opérateur OR entre plusieurs zones - compatible avec le frontend"""
        print(f"[DEBUG] _cmd_or appelée avec: {cmd.raw_command}")
        if self.grid is None:
            self.error = "La grille n'a pas été initialisée."
            return False

        try:
            # Parser les coordonnées directement depuis la commande brute (même logique que AND)
            import re
            raw_command = cmd.raw_command if hasattr(cmd, 'raw_command') else str(cmd)
            coord_pattern = r'\[([^\]]+)\]'
            coord_matches = re.findall(coord_pattern, raw_command)

            print(f"[DEBUG] _cmd_or coordonnées extraites: {coord_matches}")
            if len(coord_matches) < 2:
                self.error = f"OR nécessite au moins 2 zones, trouvées: {len(coord_matches)}"
                return False

            # Parser les rectangles depuis les coordonnées (comme le frontend)
            rectangles = []
            for coord_match in coord_matches:
                # Extraire les coordonnées individuelles
                coords = []
                if ' ' in coord_match:
                    # Rectangle : "0,0 2,2" -> [(0,0), (2,2)]
                    parts = coord_match.split()
                    start_coords = list(map(int, parts[0].split(',')))
                    end_coords = list(map(int, parts[1].split(',')))
                    coords = [start_coords, end_coords]
                else:
                    # Cellule simple : "0,0" -> [(0,0)]
                    coords = [list(map(int, coord_match.split(',')))]

                if len(coords) == 2:
                    # Rectangle
                    start_row, start_col = coords[0]
                    end_row, end_col = coords[1]
                    rectangles.append({
                        'start_row': min(start_row, end_row),
                        'start_col': min(start_col, end_col),
                        'end_row': max(start_row, end_row),
                        'end_col': max(start_col, end_col)
                    })
                else:
                    # Cellule simple
                    row, col = coords[0]
                    rectangles.append({
                        'start_row': row,
                        'start_col': col,
                        'end_row': row,
                        'end_col': col
                    })

            print(f"[DEBUG] _cmd_or rectangles parsés: {rectangles}")

            # Vérifier que toutes les zones ont la même taille (comme dans le frontend)
            first_rect = rectangles[0]
            width = first_rect['end_col'] - first_rect['start_col'] + 1
            height = first_rect['end_row'] - first_rect['start_row'] + 1

            for i, rect in enumerate(rectangles[1:], 1):
                rect_width = rect['end_col'] - rect['start_col'] + 1
                rect_height = rect['end_row'] - rect['start_row'] + 1
                if rect_width != width or rect_height != height:
                    self.error = f"Toutes les zones doivent avoir la même taille. Zone 0: {width}x{height}, Zone {i}: {rect_width}x{rect_height}"
                    return False

            print(f"[DEBUG] _cmd_or traitement zone {width}x{height}")

            # Appliquer l'opération cellule par cellule (comme dans le frontend)
            for row in range(height):
                for col in range(width):
                    first_zone_row = first_rect['start_row'] + row
                    first_zone_col = first_rect['start_col'] + col

                    # Obtenir la valeur de la première zone
                    result_value = self.grid[first_zone_row, first_zone_col]

                    # Appliquer l'opération avec toutes les autres zones
                    for i in range(1, len(rectangles)):
                        other_rect = rectangles[i]
                        other_zone_row = other_rect['start_row'] + row
                        other_zone_col = other_rect['start_col'] + col
                        other_value = self.grid[other_zone_row, other_zone_col]

                        # Appliquer l'opération binaire OR (0=fond, 1-9=valeur)
                        # OR: résultat = première valeur non-nulle trouvée
                        if result_value == 0 and other_value != 0:
                            result_value = other_value
                        # Si result_value est déjà non-nul, on le garde

                    # Écrire le résultat dans la première zone
                    self.grid[first_zone_row, first_zone_col] = result_value

            print("[DEBUG] _cmd_or: opération terminée")
            return True

        except Exception as e:
            self.error = f"Erreur lors de l'exécution de OR: {str(e)}"
            return False

    def _cmd_xor(self, cmd: UnifiedCommand) -> bool:
        """Exécute l'opérateur XOR entre deux zones - compatible avec le frontend"""
        print(f"[DEBUG] _cmd_xor appelée avec: {cmd.raw_command}")
        if self.grid is None:
            self.error = "La grille n'a pas été initialisée."
            return False

        try:
            # Parser les coordonnées directement depuis la commande brute (même logique que AND)
            import re
            raw_command = cmd.raw_command if hasattr(cmd, 'raw_command') else str(cmd)
            coord_pattern = r'\[([^\]]+)\]'
            coord_matches = re.findall(coord_pattern, raw_command)

            print(f"[DEBUG] _cmd_xor coordonnées extraites: {coord_matches}")
            if len(coord_matches) != 2:
                self.error = f"XOR nécessite exactement 2 zones, trouvées: {len(coord_matches)}"
                return False

            # Parser les rectangles depuis les coordonnées (comme le frontend)
            rectangles = []
            for coord_match in coord_matches:
                # Extraire les coordonnées individuelles
                coords = []
                if ' ' in coord_match:
                    # Rectangle : "0,0 2,2" -> [(0,0), (2,2)]
                    parts = coord_match.split()
                    start_coords = list(map(int, parts[0].split(',')))
                    end_coords = list(map(int, parts[1].split(',')))
                    coords = [start_coords, end_coords]
                else:
                    # Cellule simple : "0,0" -> [(0,0)]
                    coords = [list(map(int, coord_match.split(',')))]

                if len(coords) == 2:
                    # Rectangle
                    start_row, start_col = coords[0]
                    end_row, end_col = coords[1]
                    rectangles.append({
                        'start_row': min(start_row, end_row),
                        'start_col': min(start_col, end_col),
                        'end_row': max(start_row, end_row),
                        'end_col': max(start_col, end_col)
                    })
                else:
                    # Cellule simple
                    row, col = coords[0]
                    rectangles.append({
                        'start_row': row,
                        'start_col': col,
                        'end_row': row,
                        'end_col': col
                    })

            print(f"[DEBUG] _cmd_xor rectangles parsés: {rectangles}")

            # Vérifier que les deux zones ont la même taille (comme dans le frontend)
            first_rect = rectangles[0]
            second_rect = rectangles[1]
            width = first_rect['end_col'] - first_rect['start_col'] + 1
            height = first_rect['end_row'] - first_rect['start_row'] + 1

            second_width = second_rect['end_col'] - second_rect['start_col'] + 1
            second_height = second_rect['end_row'] - second_rect['start_row'] + 1

            if second_width != width or second_height != height:
                self.error = f"Les deux zones doivent avoir la même taille. Zone 0: {width}x{height}, Zone 1: {second_width}x{second_height}"
                return False

            print(f"[DEBUG] _cmd_xor traitement zone {width}x{height}")

            # Appliquer l'opération cellule par cellule (comme dans le frontend)
            for row in range(height):
                for col in range(width):
                    first_zone_row = first_rect['start_row'] + row
                    first_zone_col = first_rect['start_col'] + col
                    second_zone_row = second_rect['start_row'] + row
                    second_zone_col = second_rect['start_col'] + col

                    # Obtenir les valeurs des deux zones
                    first_value = self.grid[first_zone_row, first_zone_col]
                    second_value = self.grid[second_zone_row, second_zone_col]

                    # Appliquer l'opération binaire XOR (0=fond, 1-9=valeur)
                    # XOR: résultat = 0 si les deux valeurs sont identiques, sinon première valeur non-nulle
                    if first_value == second_value:
                        result_value = 0  # Identiques -> 0
                    elif first_value != 0:
                        result_value = first_value  # Première valeur non-nulle
                    else:
                        result_value = second_value  # Deuxième valeur non-nulle

                    # Écrire le résultat dans la première zone
                    self.grid[first_zone_row, first_zone_col] = result_value

            print("[DEBUG] _cmd_xor: opération terminée")
            return True

        except Exception as e:
            self.error = f"Erreur lors de l'exécution de XOR: {str(e)}"
            return False