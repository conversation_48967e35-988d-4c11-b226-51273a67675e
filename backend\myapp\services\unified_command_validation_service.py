"""
Service de validation pour les commandes unifiées.
Implémente la validation selon les spécifications de la Phase 5.
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from django.core.exceptions import ValidationError

from myapp.models import UnifiedCommandNew

logger = logging.getLogger(__name__)


class UnifiedCommandValidationService:
    """Service de validation pour les commandes au format unifié"""
    
    def __init__(self):
        self.unified_commands = self._load_unified_commands()
        self._compile_validation_patterns()
    
    def _load_unified_commands(self) -> Dict[str, UnifiedCommandNew]:
        """Charge les commandes unifiées depuis la base de données et ajoute les commandes de fallback"""
        commands = {}

        # Toujours inclure les commandes de fallback
        fallback_commands = self._get_fallback_commands()

        try:
            # Charger les commandes depuis la base de données
            for cmd in UnifiedCommandNew.objects.filter(is_active=True):
                commands[cmd.name] = cmd

            # Ajouter les commandes de fallback qui ne sont pas dans la base
            for name, spec in fallback_commands.items():
                if name not in commands:
                    # Créer un objet mock pour les commandes de fallback
                    commands[name] = type('MockCommand', (), {
                        'name': name,
                        'coordinate_format': spec.get('coordinate_format', 'unified'),
                        'supports_additional_coordinates': spec.get('supports_additional_coordinates', False),
                        'is_motif_compatible': spec.get('is_motif_compatible', False)
                    })()

        except Exception as e:
            logger.warning(f"Impossible de charger les commandes unifiées: {e}")
            # En cas d'erreur, utiliser uniquement les commandes de fallback
            for name, spec in fallback_commands.items():
                commands[name] = type('MockCommand', (), {
                    'name': name,
                    'coordinate_format': spec.get('coordinate_format', 'unified'),
                    'supports_additional_coordinates': spec.get('supports_additional_coordinates', False),
                    'is_motif_compatible': spec.get('is_motif_compatible', False)
                })()

        return commands

    def reload_commands(self):
        """Force le rechargement des commandes depuis la base de données"""
        self.unified_commands = self._load_unified_commands()
        logger.info(f"Commandes rechargées: {list(self.unified_commands.keys())}")

    def _get_fallback_commands(self) -> Dict[str, Any]:
        """Commandes de fallback si la base n'est pas accessible"""
        return {
            'CLEAR': {'coordinate_format': 'unified', 'supports_additional_coordinates': True},
            'FILL': {'coordinate_format': 'unified', 'supports_additional_coordinates': True},
            'EDIT': {'coordinate_format': 'unified', 'supports_additional_coordinates': False},
            'REPLACE': {'coordinate_format': 'unified', 'supports_additional_coordinates': False},
            'SURROUND': {'coordinate_format': 'unified', 'supports_additional_coordinates': False},
            'FLIP': {'coordinate_format': 'none', 'is_motif_compatible': True},
            'ROTATE': {'coordinate_format': 'none', 'is_motif_compatible': True},
            'COPY': {'coordinate_format': 'motif', 'is_motif_compatible': True},
            'CUT': {'coordinate_format': 'motif', 'is_motif_compatible': True},
            'PASTE': {'coordinate_format': 'motif', 'is_motif_compatible': True},
            'INSERT': {'coordinate_format': 'unified'},
            'MULTIPLY': {'coordinate_format': 'none', 'supports_additional_coordinates': False},
            'DIVIDE': {'coordinate_format': 'none', 'supports_additional_coordinates': False},
            'RESIZE': {'coordinate_format': 'unified', 'supports_additional_coordinates': False},
            'DELETE': {'coordinate_format': 'unified'},
            'EXTRACT': {'coordinate_format': 'unified'},
            'SELECT': {'coordinate_format': 'unified', 'supports_additional_coordinates': True},
            'TRANSFERT': {'coordinate_format': 'unified'},
            'INPUT': {'coordinate_format': 'none', 'supports_additional_coordinates': False},
            'END': {'coordinate_format': 'unified'},
            'INIT': {'coordinate_format': 'unified'},
            'XOR': {'coordinate_format': 'coordinate_operator', 'supports_additional_coordinates': False},
            'AND': {'coordinate_format': 'coordinate_operator', 'supports_additional_coordinates': False},
            'OR': {'coordinate_format': 'coordinate_operator', 'supports_additional_coordinates': False}
        }
    
    def _compile_validation_patterns(self):
        """Compile les patterns de validation regex"""
        self.patterns = {
            # Format unifié standard: ACTION paramètres [coordonnées]
            'unified_standard': re.compile(r'^([A-Z_]+)(\s+[^\[\](){}]+)?(\s*\[[^\]]+\])*$'),
            # Format motif: ACTION paramètres([coordonnées])
            'motif': re.compile(r'^([A-Z_]+)\s+[A-Z_]+\s*\([^)]+\)$'),
            # Nouveaux formats avec modificateurs de coordonnées
            # INVERT: ACTION (INVERT ([coordonnées]))
            'invert_modifier': re.compile(r'^([A-Z_]+)(\s+[^(]*?)?\s*\(INVERT\s*\([^)]+\)\)$'),
            # COLOR: ACTION (COLOR params ([coordonnées]))
            'color_modifier': re.compile(r'^([A-Z_]+)(\s+[^(]*?)?\s*\(COLOR\s+[^(]+\([^)]+\)\)$'),
            # XOR: ACTION (XOR (location1, location2))
            'xor_operator': re.compile(r'^([A-Z_]+)(\s+[^(]*?)?\s*\(XOR\s*\([^,]+,\s*[^)]+\)\)$'),
            # AND: ACTION (AND (location1, location2, ...))
            'and_operator': re.compile(r'^([A-Z_]+)(\s+[^(]*?)?\s*\(AND\s*\([^)]+\)\)$'),
            # OR: ACTION (OR (location1, location2, ...))
            'or_operator': re.compile(r'^([A-Z_]+)(\s+[^(]*?)?\s*\(OR\s*\([^)]+\)\)$'),
            # TRANSFERT avec accolades: TRANSFERT {commandes}
            'transfert_braces': re.compile(r'^TRANSFERT\s*\{[^}]+\}$'),
            # Commandes simples: ACTION ou ACTION paramètres
            'simple': re.compile(r'^([A-Z_]+)(\s+[^\[\](){}]+)?$'),
            # Coordonnées dans crochets: [x1,y1 x2,y2]
            'coordinates': re.compile(r'\[([^\]]+)\]'),
            # Commandes groupées: CLEARS {CLEAR ...; CLEAR ...}
            'grouped': re.compile(r'^(CLEARS|FILLS|EDITS|REPLACES|SURROUNDS)\s*\((.+)\)$', re.DOTALL)
        }
    
    def validate_command(self, command_line: str) -> Dict[str, Any]:
        """
        Valide une ligne de commande selon le format unifié
        
        Args:
            command_line: La ligne de commande à valider
            
        Returns:
            Dict avec:
            - valid: bool
            - command_name: str
            - format_type: str
            - errors: List[str]
            - parsed_data: Dict
        """
        result = {
            'valid': False,
            'command_name': None,
            'format_type': None,
            'errors': [],
            'parsed_data': {}
        }
        
        if not command_line or not command_line.strip():
            result['errors'].append('Commande vide')
            return result
        
        command_line = command_line.strip()

        # Support des commandes groupées (CLEARS, FILLS, ...)
        grouped_match = self.patterns['grouped'].match(command_line)
        if grouped_match:
            group_type = grouped_match.group(1)
            subcommands_str = grouped_match.group(2)
            # Découper les sous-commandes par ';'
            subcommands = [s.strip() for s in subcommands_str.split(';') if s.strip()]
            group_results = []
            all_valid = True
            for subcmd in subcommands:
                sub_result = self.validate_command(subcmd)
                group_results.append(sub_result)
                if not sub_result['valid']:
                    all_valid = False
            return {
                'valid': all_valid,
                'command_name': group_type,
                'format_type': 'grouped',
                'errors': [] if all_valid else [f'Commande groupée invalide: {command_line}'],
                'parsed_data': {'subcommands': group_results}
            }
        
        # Extraire le nom de la commande
        if '(INVERT (' in command_line:
            # Pour ACTION (INVERT (...)), l'action est le premier mot
            first_word = command_line.split()[0].upper()
            result['format_type'] = 'coordinate_modifier'
            result['modifier_type'] = 'INVERT'
        elif '(COLOR ' in command_line:
            # Pour ACTION (COLOR ...), l'action est le premier mot
            first_word = command_line.split()[0].upper()
            result['format_type'] = 'coordinate_modifier'
            result['modifier_type'] = 'COLOR'
        elif '(XOR ' in command_line:
            # Pour ACTION (XOR (...)), l'action est le premier mot
            first_word = command_line.split()[0].upper()
            result['format_type'] = 'coordinate_operator'
            result['operator_type'] = 'XOR'
        elif '(AND ' in command_line:
            # Pour ACTION (AND (...)), l'action est le premier mot
            first_word = command_line.split()[0].upper()
            result['format_type'] = 'coordinate_operator'
            result['operator_type'] = 'AND'
        elif '(OR ' in command_line:
            # Pour ACTION (OR (...)), l'action est le premier mot
            first_word = command_line.split()[0].upper()
            result['format_type'] = 'coordinate_operator'
            result['operator_type'] = 'OR'
        else:
            first_word = command_line.split()[0].upper() if command_line.split() else ''
        
        result['command_name'] = first_word
        
        # Vérifier si la commande est connue
        if first_word not in self.unified_commands:
            result['errors'].append(f'Commande inconnue: {first_word}')
            return result
        
        # Récupérer les spécifications de la commande
        cmd_spec = self.unified_commands[first_word]
        
        # Valider selon le format de la commande
        validation_result = self._validate_by_format(command_line, cmd_spec, result)
        
        return validation_result
    
    def _validate_by_format(self, command_line: str, cmd_spec: Any, result: Dict[str, Any]) -> Dict[str, Any]:
        """Valide selon le format spécifique de la commande"""
        
        # Déterminer le format attendu
        if hasattr(cmd_spec, 'coordinate_format'):
            format_type = cmd_spec.coordinate_format
        else:
            format_type = cmd_spec.get('coordinate_format', 'unified')
        
        result['format_type'] = format_type
        
        if format_type == 'unified':
            return self._validate_unified_format(command_line, cmd_spec, result)
        elif format_type == 'motif':
            return self._validate_motif_format(command_line, cmd_spec, result)
        elif format_type == 'special':
            return self._validate_special_format(command_line, cmd_spec, result)
        elif format_type == 'coordinate_modifier':
            return self._validate_coordinate_modifier_format(command_line, cmd_spec, result)
        elif format_type == 'coordinate_operator':
            return self._validate_coordinate_operator_format(command_line, cmd_spec, result)
        else:
            result['errors'].append(f'Format de commande non supporté: {format_type}')
            return result
    
    def _validate_unified_format(self, command_line: str, cmd_spec: Any, result: Dict[str, Any]) -> Dict[str, Any]:
        """Valide le format unifié: ACTION paramètres [coordonnées] [+coordonnées]"""
        
        # Cas spéciaux
        if command_line.upper() in ['INIT', 'INPUT', 'END', 'TRANSFERT']:
            result['valid'] = True
            return result
        
        # TRANSFERT avec accolades
        if command_line.startswith('TRANSFERT {'):
            if self.patterns['transfert_braces'].match(command_line):
                result['valid'] = True
                result['parsed_data']['has_braces'] = True
            else:
                result['errors'].append('Format TRANSFERT avec accolades invalide')
            return result
        
        # Vérifier le pattern général
        if not (self.patterns['unified_standard'].match(command_line) or 
                self.patterns['simple'].match(command_line)):
            result['errors'].append('Syntaxe unifiée invalide')
            return result
        
        # Extraire et valider les coordonnées
        coordinates = self.patterns['coordinates'].findall(command_line)
        
        if coordinates:
            coord_validation = self._validate_coordinates(coordinates[0])
            if not coord_validation['valid']:
                result['errors'].extend(coord_validation['errors'])
                return result
            result['parsed_data']['coordinates'] = coord_validation['coordinates']

        result['valid'] = len(result['errors']) == 0
        return result
    
    def _validate_motif_format(self, command_line: str, cmd_spec: Any, result: Dict[str, Any]) -> Dict[str, Any]:
        """Valide le format motif: ACTION DIRECTION([coordonnées])"""
        
        if not self.patterns['motif'].match(command_line):
            result['errors'].append('Format motif invalide')
            return result
        
        # Extraire les coordonnées des parenthèses
        paren_match = re.search(r'\(([^)]+)\)', command_line)
        if not paren_match:
            result['errors'].append('Coordonnées manquantes dans les parenthèses')
            return result
        
        coord_validation = self._validate_coordinates(paren_match.group(1))
        if not coord_validation['valid']:
            result['errors'].extend(coord_validation['errors'])
            return result
        
        result['parsed_data']['coordinates'] = coord_validation['coordinates']
        result['parsed_data']['is_motif'] = True
        result['valid'] = True
        return result
    
    def _validate_special_format(self, command_line: str, cmd_spec: Any, result: Dict[str, Any]) -> Dict[str, Any]:
        """Valide le format spécial - SELECT_COLOR et SELECT_INVERT ne sont plus supportés"""
        result['errors'].append(f'Commande obsolète non supportée: {result["command_name"]}')
        return result
    
    def _validate_coordinate_modifier_format(self, command_line: str, cmd_spec: Any, result: Dict[str, Any]) -> Dict[str, Any]:
        """Valide le format des modificateurs de coordonnées: ACTION (INVERT ([coords])) ou ACTION (COLOR params ([coords]))"""
        
        modifier_type = result.get('modifier_type', '')
        
        if modifier_type == 'INVERT':
            if not self.patterns['invert_modifier'].match(command_line):
                result['errors'].append('Format INVERT modifier invalide')
                return result
            
            # Extraire les coordonnées
            invert_match = re.search(r'INVERT\s*\(([^)]+)\)', command_line)
            if invert_match:
                coord_content = invert_match.group(1)
                # Extraire les coordonnées des crochets
                coord_matches = re.findall(r'\[([^\]]+)\]', coord_content)
                if coord_matches:
                    coord_validation = self._validate_coordinates(coord_matches[0])
                    if not coord_validation['valid']:
                        result['errors'].extend(coord_validation['errors'])
                        return result
                    result['parsed_data']['coordinates'] = coord_validation['coordinates']
                    result['parsed_data']['modifier'] = 'INVERT'
                
        elif modifier_type == 'COLOR':
            if not self.patterns['color_modifier'].match(command_line):
                result['errors'].append('Format COLOR modifier invalide')
                return result
            
            # Extraire les paramètres et coordonnées
            color_match = re.search(r'COLOR\s+([^(]+)\s*\(([^)]+)\)', command_line)
            if color_match:
                color_params = color_match.group(1).strip()
                coord_content = color_match.group(2)
                
                # Valider les couleurs
                try:
                    colors = [int(c.strip()) for c in color_params.split(',')]
                    if not all(0 <= c <= 9 for c in colors):
                        result['errors'].append('Couleurs doivent être entre 0 et 9')
                        return result
                    result['parsed_data']['colors'] = colors
                except ValueError:
                    result['errors'].append('Format de couleurs invalide')
                    return result
                
                # Extraire les coordonnées des crochets
                coord_matches = re.findall(r'\[([^\]]+)\]', coord_content)
                if coord_matches:
                    coord_validation = self._validate_coordinates(coord_matches[0])
                    if not coord_validation['valid']:
                        result['errors'].extend(coord_validation['errors'])
                        return result
                    result['parsed_data']['coordinates'] = coord_validation['coordinates']
                    result['parsed_data']['modifier'] = 'COLOR'
        
        result['valid'] = len(result['errors']) == 0
        return result
    
    def _validate_coordinate_operator_format(self, command_line: str, cmd_spec: Any, result: Dict[str, Any]) -> Dict[str, Any]:
        """Valide le format des opérateurs de coordonnées: ACTION (XOR/AND/OR (...))"""
        
        operator_type = result.get('operator_type', '')
        
        if operator_type == 'XOR':
            if not self.patterns['xor_operator'].match(command_line):
                result['errors'].append('Format XOR operator invalide')
                return result
            
            # Extraire les deux opérandes
            xor_match = re.search(r'XOR\s*\(([^,]+),\s*([^)]+)\)', command_line)
            if xor_match:
                operand1 = xor_match.group(1).strip()
                operand2 = xor_match.group(2).strip()
                
                # Valider chaque opérande
                coords1 = self._extract_coordinates_from_operand(operand1)
                coords2 = self._extract_coordinates_from_operand(operand2)
                
                if not coords1['valid'] or not coords2['valid']:
                    result['errors'].extend(coords1.get('errors', []))
                    result['errors'].extend(coords2.get('errors', []))
                    return result
                
                result['parsed_data']['operator'] = 'XOR'
                result['parsed_data']['operands'] = [coords1['coordinates'], coords2['coordinates']]
                
        elif operator_type in ['AND', 'OR']:
            pattern_key = f'{operator_type.lower()}_operator'
            if not self.patterns[pattern_key].match(command_line):
                result['errors'].append(f'Format {operator_type} operator invalide')
                return result
            
            # Extraire les opérandes multiples
            operator_match = re.search(rf'{operator_type}\s*\(([^)]+)\)', command_line)
            if operator_match:
                operands_str = operator_match.group(1)
                operands = [op.strip() for op in operands_str.split(',')]
                
                if len(operands) < 2:
                    result['errors'].append(f'{operator_type} nécessite au moins 2 opérandes')
                    return result
                
                # Valider chaque opérande
                all_coords = []
                for operand in operands:
                    coords = self._extract_coordinates_from_operand(operand)
                    if not coords['valid']:
                        result['errors'].extend(coords.get('errors', []))
                        return result
                    all_coords.append(coords['coordinates'])
                
                result['parsed_data']['operator'] = operator_type
                result['parsed_data']['operands'] = all_coords
        
        result['valid'] = len(result['errors']) == 0
        return result
    
    def _extract_coordinates_from_operand(self, operand: str) -> Dict[str, Any]:
        """Extrait et valide les coordonnées d'un opérande"""
        result = {
            'valid': False,
            'coordinates': [],
            'errors': []
        }
        
        # Nettoyer l'opérande
        clean_operand = operand.strip().replace('(', '').replace(')', '')
        
        # Extraire les coordonnées des crochets
        coord_matches = re.findall(r'\[([^\]]+)\]', clean_operand)
        
        if not coord_matches:
            result['errors'].append(f'Aucune coordonnée trouvée dans l\'opérande: {operand}')
            return result
        
        # Valider chaque bloc de coordonnées
        for coord_match in coord_matches:
            coord_validation = self._validate_coordinates(coord_match)
            if not coord_validation['valid']:
                result['errors'].extend(coord_validation['errors'])
                return result
            result['coordinates'].extend(coord_validation['coordinates'])
        
        result['valid'] = True
        return result
    
    def _validate_coordinates(self, coord_string: str) -> Dict[str, Any]:
        """Valide une chaîne de coordonnées"""
        result = {
            'valid': False,
            'coordinates': [],
            'errors': []
        }
        
        if not coord_string or not coord_string.strip():
            result['errors'].append('Coordonnées vides')
            return result
        
        # Diviser les coordonnées
        coord_parts = coord_string.strip().split()
        
        coordinates = []
        for part in coord_parts:
            if ',' in part:
                try:
                    x, y = part.split(',', 1)
                    x, y = int(x.strip()), int(y.strip())
                    if x < 0 or y < 0:
                        result['errors'].append(f'Coordonnées négatives non autorisées: {part}')
                        return result
                    coordinates.append((x, y))
                except ValueError:
                    result['errors'].append(f'Format de coordonnées invalide: {part}')
                    return result
            else:
                result['errors'].append(f'Coordonnées doivent être au format x,y: {part}')
                return result
        
        if not coordinates:
            result['errors'].append('Aucune coordonnée valide trouvée')
            return result
        
        result['coordinates'] = coordinates
        result['valid'] = True
        return result
    
    def validate_scenario_commands(self, commands: List[str]) -> Dict[str, Any]:
        """
        Valide une liste complète de commandes de scénario
        
        Args:
            commands: Liste des commandes du scénario
            
        Returns:
            Dict avec:
            - valid: bool
            - command_count: int
            - valid_commands: int
            - errors: List[str]
            - command_details: List[Dict]
        """
        result = {
            'valid': False,
            'command_count': len(commands),
            'valid_commands': 0,
            'errors': [],
            'command_details': []
        }
        
        if not commands:
            result['errors'].append('Aucune commande fournie')
            return result
        
        for i, command in enumerate(commands):
            cmd_result = self.validate_command(command)
            result['command_details'].append({
                'line': i + 1,
                'command': command,
                'validation': cmd_result
            })
            
            if cmd_result['valid']:
                result['valid_commands'] += 1
            else:
                for error in cmd_result['errors']:
                    result['errors'].append(f'Ligne {i + 1}: {error}')
        
        # Considérer le scénario comme valide si au moins 80% des commandes sont valides
        if result['command_count'] > 0:
            validity_ratio = result['valid_commands'] / result['command_count']
            result['valid'] = validity_ratio >= 0.8
            
            if not result['valid']:
                result['errors'].append(
                    f'Trop de commandes invalides: {result["valid_commands"]}/{result["command_count"]} '
                    f'({validity_ratio:.1%}) - minimum requis: 80%'
                )
        
        return result
    
    def get_command_suggestions(self, partial_command: str) -> List[Dict[str, Any]]:
        """Retourne des suggestions de commandes basées sur une saisie partielle"""
        suggestions = []
        
        if not partial_command:
            return suggestions
        
        partial_upper = partial_command.upper()
        
        for cmd_name, cmd_spec in self.unified_commands.items():
            if cmd_name.startswith(partial_upper):
                suggestion = {
                    'name': cmd_name,
                    'syntax': getattr(cmd_spec, 'unified_syntax', '') if hasattr(cmd_spec, 'unified_syntax') else '',
                    'format_type': cmd_spec.coordinate_format if hasattr(cmd_spec, 'coordinate_format') else getattr(cmd_spec, 'coordinate_format', 'unified')
                }
                suggestions.append(suggestion)
        
        return sorted(suggestions, key=lambda x: x['name'])