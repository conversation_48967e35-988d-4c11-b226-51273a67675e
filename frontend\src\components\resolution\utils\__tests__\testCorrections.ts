/**
 * Script de test pour vérifier les corrections des utilitaires de commandes
 */

import { parseCoordinates, parseUnifiedCommand } from '../commandGenerationUtils';
import { formatSpecialSelection } from '../specialSelectionUtils';

// Tests des coordonnées selon la documentation
console.log('=== Tests parseCoordinates ===');
console.log('Cellule simple:', parseCoordinates('2,2')); // Devrait retourner ['2,2']
console.log('Rectangle:', parseCoordinates('1,1 3,3')); // Devrait retourner ['1,1 3,3']
console.log('Avec crochets:', parseCoordinates('[2,2]')); // Devrait retourner ['2,2']
console.log('Rectangle avec crochets:', parseCoordinates('[1,1 3,3]')); // Devrait retourner ['1,1 3,3']

// Tests de formatage des commandes
console.log('\n=== Tests formatUnifiedCommand ===');
console.log('Commande simple:', formatUnifiedCommand('FILL', '5', ['2,2'])); // FILL 5 [2,2]
console.log('Commande multiple:', formatUnifiedCommand('FILL', '5', ['1,1 3,3', '5,5'])); // FILL 5 ([1,1 3,3] [5,5])
console.log('Sans paramètres:', formatUnifiedCommand('CLEAR', undefined, ['2,2'])); // CLEAR [2,2]

// Tests de parsing des commandes
console.log('\n=== Tests parseUnifiedCommand ===');
const testCommands = [
    'FILL 5 [2,2]',
    'FILL 5 ([1,1 3,3] [5,5])',
    'CLEAR ([1,2 8,8] [8,5 8,8])',
    'REPLACE 1,8,3 5 [1,1 3,3]',
    'SELECT (COLOR 1,2,3 [0,0 4,4])',
    'SELECT (INVERT [1,1 3,3])',
    'TRANSFERT {INIT 3x3; EDIT 7 [0,0]}',
    'INSERT 5 ROWS BELOW [0,1 5,4]',
    'FLIP HORIZONTAL [0,0 4,4]',
    'ROTATE LEFT [2,2 6,6]'
];

testCommands.forEach(cmd => {
    console.log(`\nCommande: ${cmd}`);
    const parsed = parseUnifiedCommand(cmd);
    console.log('Résultat:', {
        action: parsed.action,
        parameters: parsed.parameters,
        coordinates: parsed.coordinates,
        isSpecialSelection: parsed.isSpecialSelection
    });
});

// Tests des sélections spéciales
console.log('\n=== Tests formatSpecialSelection ===');
console.log('COLOR:', formatSpecialSelection('COLOR', '1,2,3', ['0,0 4,4', '5,5']));
console.log('INVERT:', formatSpecialSelection('INVERT', '', ['1,1 3,3', '5,5']));

console.log('\n=== Tests terminés ===');