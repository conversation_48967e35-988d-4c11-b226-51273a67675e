import { parseUnifiedCommand, UnifiedCommand } from './commandGenerationUtils';

export interface OptimizationRule {
    name: string;
    apply: (commands: string[]) => string[];
    priority: number;
}

/**
 * Regroupe des commandes consécutives identiques (hors groupement)
 */
function groupConsecutiveSimpleCommands(commands: string[], action: string): string | string[] | null {
    // DÉSACTIVER l'optimisation pour DELETE et INSERT car le backend ne supporte pas encore
    // le format avec coordonnées multiples groupées
    if (action === 'DELETE' || action === 'INSERT') {
        return commands; // Retourner les commandes telles quelles, sans optimisation
    }
    
    // Regroupe par paramètres
    const parsed = commands.map(cmd => parseUnifiedCommand(cmd));
    const byParams: Record<string, {params: string, cmds: string[]}> = {};
    for (let idx = 0; idx < parsed.length; idx++) {
        const p = parsed[idx];
        const key = p.parameters ?? '';
        if (!byParams[key]) byParams[key] = { params: key, cmds: [] };
        byParams[key].cmds.push(commands[idx]);
    }
    const regrouped: string[] = [];
    for (const key in byParams) {
        const group = byParams[key];
        let result = action;
        if (group.params) result += ` ${group.params}`;
        // On récupère chaque bloc de coordonnées tel quel
        const allBlocks: string[] = [];
        for (const cmd of group.cmds) {
            // On extrait la/les parties entre crochets (supporte plusieurs crochets par commande)
            const matches = [...cmd.matchAll(/\[([^\]]+)\]/g)];
            for (const m of matches) {
                allBlocks.push(`[${m[1]}]`);
            }
        }
        if (allBlocks.length > 0) {
            if (allBlocks.length === 1) result += ` ${allBlocks[0]}`;
            else result += ` (${allBlocks.join(' ')})`;
        }
        regrouped.push(result);
    }
    if (regrouped.length === 1) return regrouped[0];
    return regrouped;
}

/**
 * Optimise le contenu d'un groupement de commandes (ex: FLOODFILLS {...} ou TRANSFERT {...})
 */
function optimizeGroupedCommand(command: string): string {
    const groupRegex = /^([A-Z]+)S?\s*\{(.+)\}$/s;
    const match = command.match(groupRegex);
    if (!match) return command;

    const groupAction = match[1];
    const inner = match[2];
    const innerCommands = inner.split(';').map(cmd => cmd.trim()).filter(Boolean);

    // Regrouper toutes les commandes par action+paramètres
    const regroupMap: Record<string, string[]> = {};
    const order: string[] = [];
    for (const cmd of innerCommands) {
        const parsed = parseUnifiedCommand(cmd);
        const key = parsed.action + '||' + (parsed.parameters ?? '');
        if (!regroupMap[key]) {
            regroupMap[key] = [];
            order.push(key);
        }
        regroupMap[key].push(cmd);
    }

    const regrouped: string[] = [];
    for (const key of order) {
        const cmds = regroupMap[key];
        const parsed = parseUnifiedCommand(cmds[0]);
        if (
            cmds.length > 1 &&
            ['EDIT', 'FLOODFILL', 'REPLACE', 'FILL', 'DELETE', 'INSERT', 'CLEAR', 'SURROUND'].includes(parsed.action)
        ) {
            const grouped = groupConsecutiveSimpleCommands(cmds, parsed.action);
            if (Array.isArray(grouped)) regrouped.push(...grouped);
            else if (grouped) regrouped.push(grouped);
        } else {
            regrouped.push(...cmds);
        }
    }

    return `${groupAction} {${regrouped.join('; ')}}`;
}

/**
 * Optimise une liste de commandes selon les nouvelles règles
 * Remplace optimizeUnifiedCommandList
 */
export function optimizeCommandList(commands: string[]): { optimizedCommands: string[], rulesApplied: string[] } {
    console.log(`[DEBUG optimizeCommandList] Nombre de commandes d'origine: ${commands.length}`);
    let optimized = commands.filter(cmd => cmd && typeof cmd === 'string' && cmd.trim().length > 0);
    const rulesApplied: string[] = [];

    const sortedRules = [...optimizationRules].sort((a, b) => a.priority - b.priority);
    for (const rule of sortedRules) {
        const before = optimized;
        const after = rule.apply(before);
        if (JSON.stringify(before) !== JSON.stringify(after)) {
            rulesApplied.push(rule.name);
        }
        optimized = after;
    }
    return { optimizedCommands: optimized, rulesApplied };
}
/**
 * Vérifie si une action est une action de motif
 */
function isMotifAction(action: string): boolean {
    const motifActions = [
        'COPY', 'PASTE', 'CUT',
        'FLIP', 'ROTATE', 'MULTIPLY', 'DIVIDE'
    ];
    return motifActions.includes(action.toUpperCase());
}

/**
 * Vérifie si les coordonnées de deux commandes correspondent
 */
function coordinatesMatch(cmd1: UnifiedCommand, cmd2: UnifiedCommand): boolean {
    const coords1 = [...cmd1.coordinates, ...cmd1.additionalCoordinates].sort();
    const coords2 = [...cmd2.coordinates, ...cmd2.additionalCoordinates].sort();
    
    if (coords1.length !== coords2.length) {
        return false;
    }
    
    return coords1.every((coord, index) => coord === coords2[index]);
}

/**
 * Tente de regrouper des commandes consécutives identiques
 */
function tryGroupConsecutiveCommands(commands: string[], startIndex: number): { command: string | null, consumed: number } {
    if (startIndex >= commands.length) {
        return { command: null, consumed: 0 };
    }
    
    try {
        const first = parseUnifiedCommand(commands[startIndex]);
        const groupableActions = ['TRANSFERT', 'FILL', 'EDIT', 'REPLACE', 'CLEAR', 'SURROUND', 'FLOODFILL', 'DELETE', 'INSERT'];
        
        if (!groupableActions.includes(first.action)) {
            return { command: null, consumed: 0 };
        }
        
        const sameActionCommands = [commands[startIndex]];
        let i = startIndex + 1;
        
        // Collecter les commandes consécutives avec la même action
        while (i < commands.length) {
            try {
                const current = parseUnifiedCommand(commands[i]);
                if (current.action === first.action) {
                    sameActionCommands.push(commands[i]);
                    i++;
                } else {
                    break;
                }
            } catch {
                break;
            }
        }
        
        // Si on a plus d'une commande, essayer de les regrouper
        if (sameActionCommands.length > 1) {
            const grouped = groupSameActionCommands(sameActionCommands, first.action);
            if (grouped) {
                return { command: grouped, consumed: sameActionCommands.length };
            }
        }
        
        return { command: null, consumed: 0 };
        
    } catch {
        return { command: null, consumed: 0 };
    }
}

/**
 * Regroupe des commandes avec la même action, en préservant les blocs de coordonnées.
 */
function groupSameActionCommands(commands: string[], action: string): string | null {
    try {
        const parsedCommands = commands.map(cmd => parseUnifiedCommand(cmd));
        
        // Vérifier si tous les paramètres sont identiques (ex: la couleur pour FILL)
        const firstParams = parsedCommands[0].parameters;
        const allSameParams = parsedCommands.every(cmd => cmd.parameters === firstParams);
        
        if (allSameParams) {
            // Regrouper les blocs de coordonnées en préservant les rectangles
            const allCoordBlocks: string[] = [];
            for (const cmdStr of commands) {
                // Trouve tous les blocs de coordonnées comme `[1,2]` ou `[3,4 5,6]`
                const matches = cmdStr.match(/\[.*?\]/g);
                if (matches) {
                    allCoordBlocks.push(...matches);
                }
            }
            
            let result = action;
            if (firstParams) {
                result += ` ${firstParams}`;
            }
            
            if (allCoordBlocks.length > 0) {
                // Joindre les blocs avec des espaces, et mettre le tout entre parenthèses
                result += ` (${allCoordBlocks.join(' ')})`;
            }
            
            return result;
        } else {
            // Si les paramètres sont différents (ex: FILL 1 et FILL 2), on ne peut pas les regrouper simplement.
            // On utilise un groupement générique avec accolades.
            const groupName = action + 'S';
            const commandList = commands.join('; ');
            return `${groupName} {${commandList}}`;
        }
        
    } catch (e) {
        console.error("Error in groupSameActionCommands:", e);
        return null;
    }
}

/**
 * Regroupe les séquences COPY/CUT + (FLIP|ROTATE)* + PASTE en MOTIF { ... }
 */
function groupMotifActions(commands: string[]): string[] {
    //console.log(`[DEBUG groupMotifActions] Nombre de commandes d'origine: ${commands.length}, ${commands}`);
    const motifStart = /^\s*(COPY|CUT)\b/i;
    const motifTransform = /^\s*(FLIP|ROTATE|MULTIPLY|DIVIDE)\b/i;
    const motifEnd = /^\s*PASTE\b/i;
    const result: string[] = [];
    let i = 0;

    while (i < commands.length) {
        console.log(`[DEBUG groupMotifActions] Analyse de la commande ${commands[i]}, index ${i}, motifStart.test(commands[i]) = ${motifStart.test(commands[i])}`);
        if (motifStart.test(commands[i])) {
            const motifGroup = [commands[i]];
            let j = i + 1;
            while (j < commands.length && motifTransform.test(commands[j])) {
                motifGroup.push(commands[j]);
                j++;
            }
            console.log(`[DEBUG groupMotifActions] Test motifEnd sur ${commands[j]} à l'index ${j}: ${motifEnd.test(commands[j])}`);
            if (j < commands.length && motifEnd.test(commands[j])) {
                motifGroup.push(commands[j]);
                result.push(`MOTIF {${motifGroup.join('; ')}}`);
                console.log(`[DEBUG groupMotifActions] Ajout du MOTIF: MOTIF {${motifGroup.join('; ')}}`);
                i = j + 1;
                continue;
            }
        }
        result.push(commands[i]);
        console.log('[DEBUG groupMotifActions] Résultat:', result);
        i++;
    }
    return result;
}

/**
 * Applique une règle d'optimisation spécifique
 */
export function applyOptimizationRule(commands: string[], rule: OptimizationRule): string[] {
    try {
        return rule.apply(commands);
    } catch (error) {
        console.warn(`[Optimization] Erreur lors de l'application de la règle ${rule.name}: ${error}`);
        return commands;
    }
}

/**
 * Règles d'optimisation prédéfinies
 */
export const optimizationRules: OptimizationRule[] = [
    {
        name: 'groupMotifActions',
        priority: 0,
        apply: groupMotifActions
    },
    {
        name: 'removeRedundantSelect',
        priority: 1,
        apply: (commands: string[]) => {
            return commands.filter((cmd, index) => {
                if (!cmd.startsWith('SELECT ') || index === commands.length - 1) {
                    return true;
                }
                
                try {
                    const current = parseUnifiedCommand(cmd);
                    const next = parseUnifiedCommand(commands[index + 1]);
                    return !coordinatesMatch(current, next);
                } catch {
                    return true;
                }
            });
        }
    },
    {
        name: 'groupConsecutiveCommands',
        priority: 2,
        apply: (commands: string[]) => {
            const result: string[] = [];
            let i = 0;
            
            while (i < commands.length) {
                const grouped = tryGroupConsecutiveCommands(commands, i);
                if (grouped.command) {
                    result.push(grouped.command);
                    i += grouped.consumed;
                } else {
                    result.push(commands[i]);
                    i++;
                }
            }
            
            return result;
        }
    },
    {
        name: 'optimizeGroupedCommand',
        priority: 3,
        apply: (commands: string[]) => {
            const groupRegex = /^([A-Z]+)S?\s*\{(.+)\}$/s;
            return commands.map(cmd => {
                if (groupRegex.test(cmd)) {
                    return optimizeGroupedCommand(cmd);
                }
                return cmd;
            });
        }
    },
    {
        name: 'simplifySingletonGroups',
        priority: 4,
        apply: (commands: string[]) => {
            return commands.map(cmd => {
                const parenGroupRegex = /^(?<action>[A-Z]+S)\s*\((?<content>.*)\)$/s;
                const braceGroupRegex = /^(?<action>[A-Z]+S)\s*\{(?<content>.*)\}$/s;

                const parenMatch = cmd.match(parenGroupRegex);
                const braceMatch = cmd.match(braceGroupRegex);

                try {
                    if (braceMatch && braceMatch.groups) {
                        const { action, content } = braceMatch.groups;
                        const singularAction = action.slice(0, -1);
                        const innerCommands = content.split(';').map(c => c.trim()).filter(Boolean);
                        
                        if (innerCommands.length === 1) {
                            const innerParsed = parseUnifiedCommand(innerCommands[0]);
                            if (innerParsed && innerParsed.action === singularAction) {
                                return innerCommands[0];
                            }
                        }
                    } else if (parenMatch && parenMatch.groups) {
                        const { action, content } = parenMatch.groups;
                        const singularAction = action.slice(0, -1);
                        
                        const innerContent = content.trim();
                        const coordBlocks = innerContent.match(/\[.*?\]/g) || [];
                        const hasOtherParams = innerContent.replace(/\[.*?\]/g, '').trim().length > 0;
                        
                        if (coordBlocks.length === 1 && !hasOtherParams) {
                             const paramsContent = innerContent.replace(/[()]/g, '').trim();
                            return `${singularAction} ${paramsContent}`;
                        }
                    }
                } catch (e) {
                    return cmd;
                }
                
                return cmd;
            });
        }
    }
];

/**
 * Interface pour les statistiques d'optimisation
 */
export interface OptimizationStats {
    originalCount: number;
    optimizedCount: number;
    originalLines: number;
    optimizedLines: number;
    reduction: number;
    reductionPercentage: number;
    optimizationApplied: boolean;
    rulesApplied: string[];
}

function countAllCommands(commands: string[]): number {
    let count = 0;
    const groupRegex = /^([A-Z]+S?)\s*[\(\{](.+?)[\)\}]$/s;
    const parenGroupRegex = /^(?<action>[A-Z]+S)\s*\((?<content>.*)\)$/s;
    for (const cmd of commands) {
        const match = cmd.match(groupRegex);
        if (match) {
            const content = match[2];
            let innerCommands;
            if (parenGroupRegex.test(cmd)) {
                 innerCommands = content.match(/\[.*?\]/g) || [];
            } else {
                 innerCommands = content.split(';').map(c => c.trim()).filter(Boolean);
            }
           
            if (innerCommands.length > 1) {
                 count += countAllCommands(innerCommands);
            } else {
                 count +=1;
            }

        } else {
            count += 1;
        }
    }
    return count;
}


/**
 * Calcule les statistiques d'optimisation
 */
export function getOptimizationStats(originalCommands: string[], optimizedData: { optimizedCommands: string[], rulesApplied: string[] }): OptimizationStats {
    const { optimizedCommands, rulesApplied } = optimizedData;
    const originalCount = countAllCommands(originalCommands);
    const optimizedCount = countAllCommands(optimizedCommands);
    const originalLines = originalCommands.length;
    const optimizedLines = optimizedCommands.length;
    const reduction = originalCount - optimizedCount;
    const reductionPercentage = originalCount > 0 ? Math.round((reduction / originalCount) * 100) : 0;

    const optimizationApplied = (originalCommands.join('\n') !== optimizedCommands.join('\n'));

    return {
        originalCount,
        optimizedCount,
        originalLines,
        optimizedLines,
        reduction,
        reductionPercentage,
        optimizationApplied,
        rulesApplied
    };
}