#!/usr/bin/env python3
"""
Analyse et correction du comportement de MULTIPLY
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import numpy as np

def analyze_expected_multiply_behavior():
    """Analyse le comportement attendu de MULTIPLY 4 false"""
    
    print("🔍 Analyse du comportement attendu de MULTIPLY 4 false...")
    
    # Motif original coupé : 2x2
    original_pattern = np.array([[0, 0],
                                 [3, 0]])
    
    print("📋 Motif original (2x2):")
    print(original_pattern)
    
    # Sortie attendue après PASTE à [0,1]
    expected_output = np.array([
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    
    print("\n📋 Sortie attendue complète:")
    print(expected_output)
    
    # Extraire la zone collée (à partir de [0,1])
    pasted_region = expected_output[0:8, 1:9]  # 8x8 à partir de [0,1]
    
    print("\n📋 Zone collée extraite (8x8):")
    print(pasted_region)
    
    # Analyser le pattern
    print("\n🔍 Analyse du pattern multiplié:")
    
    # Le pattern semble être arrangé en 2x2 blocs de 4x4 chacun
    for block_row in range(2):
        for block_col in range(2):
            start_row = block_row * 4
            end_row = start_row + 4
            start_col = block_col * 4
            end_col = start_col + 4
            
            if end_row <= 8 and end_col <= 8:
                block = pasted_region[start_row:end_row, start_col:end_col]
                print(f"\nBloc [{block_row},{block_col}] (position {start_row}:{end_row}, {start_col}:{end_col}):")
                print(block)
                
                # Vérifier si c'est une répétition du motif original
                is_repetition = True
                for i in range(4):
                    for j in range(4):
                        orig_i, orig_j = i % 2, j % 2
                        if block[i, j] != original_pattern[orig_i, orig_j]:
                            is_repetition = False
                            break
                    if not is_repetition:
                        break
                
                print(f"Est une répétition 4x4 du motif original: {is_repetition}")

def test_correct_multiply_implementation():
    """Test d'une implémentation correcte de MULTIPLY"""
    
    print("\n" + "="*60)
    print("🧪 Test d'implémentation correcte de MULTIPLY...")
    
    # Motif original
    original_clipboard = np.array([[0, 0],
                                   [3, 0]])
    original_mask = np.ones((2, 2), dtype=bool)
    
    print("📋 Clipboard original:")
    print(original_clipboard)
    
    # Implémentation correcte de MULTIPLY 4 false
    factor = 4
    clip_height, clip_width = original_clipboard.shape
    
    # Calculer les nouvelles dimensions
    new_height = clip_height * factor
    new_width = clip_width * factor
    
    print(f"📏 Nouvelles dimensions: {new_height}x{new_width}")
    
    # Créer le nouveau clipboard
    new_clipboard = np.zeros((new_height, new_width), dtype=int)
    new_mask = np.zeros((new_height, new_width), dtype=bool)
    
    # Mode replace_content=False : répliquer chaque cellule factor x factor fois
    for i in range(clip_height):
        for j in range(clip_width):
            if original_mask[i, j]:
                # Cette cellule était sélectionnée, la répliquer factor x factor fois
                for fi in range(factor):
                    for fj in range(factor):
                        new_row = i * factor + fi
                        new_col = j * factor + fj
                        new_clipboard[new_row, new_col] = original_clipboard[i, j]
                        new_mask[new_row, new_col] = True
    
    print("\n📋 Clipboard après MULTIPLY 4 false (implémentation actuelle):")
    print(new_clipboard)
    print("Mask:")
    print(new_mask)
    
    # Test de collage à [0,1]
    grid = np.zeros((9, 9), dtype=int)
    x_dest, y_dest = 0, 1
    
    clip_height, clip_width = new_clipboard.shape
    for r in range(clip_height):
        for c in range(clip_width):
            if new_mask[r, c]:
                grid_r, grid_c = x_dest + r, y_dest + c
                if 0 <= grid_r < 9 and 0 <= grid_c < 9:
                    grid[grid_r, grid_c] = new_clipboard[r, c]
    
    print("\n📋 Grille après PASTE [0,1]:")
    print(grid)
    
    # Comparer avec l'attendu
    expected = np.array([
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    
    matches = np.sum(grid == expected)
    total = grid.size
    percentage = (matches / total) * 100
    
    print(f"\n📊 Comparaison avec l'attendu:")
    print(f"Cellules correspondantes: {matches}/{total} ({percentage:.1f}%)")
    
    if matches != total:
        print("\n❌ Différences détectées:")
        diff_positions = np.where(grid != expected)
        for i in range(min(10, len(diff_positions[0]))):
            row, col = diff_positions[0][i], diff_positions[1][i]
            print(f"   Position [{row},{col}]: généré={grid[row,col]}, attendu={expected[row,col]}")

def propose_multiply_fix():
    """Propose une correction pour MULTIPLY"""
    
    print("\n" + "="*60)
    print("🔧 Proposition de correction pour MULTIPLY...")
    
    # Le problème semble être que MULTIPLY doit créer un arrangement spécifique
    # Analysons la sortie attendue pour comprendre le pattern
    
    original_pattern = np.array([[0, 0],
                                 [3, 0]])
    
    # La sortie attendue suggère que MULTIPLY 4 false devrait créer
    # un arrangement 2x2 de copies du motif original, chaque copie étant 4x4
    
    # Essayons une approche différente : arrangement en grille 2x2
    factor = 4
    clip_height, clip_width = original_pattern.shape
    
    # Créer un arrangement 2x2 de motifs 4x4
    new_clipboard = np.zeros((8, 8), dtype=int)
    new_mask = np.ones((8, 8), dtype=bool)
    
    # Positions des 4 copies dans un arrangement 2x2
    positions = [(0, 0), (0, 4), (4, 0), (4, 4)]
    
    for pos_idx, (start_row, start_col) in enumerate(positions):
        print(f"\nCopie {pos_idx + 1} à position ({start_row}, {start_col}):")
        
        # Remplir chaque position avec le motif original étendu 4x4
        for i in range(4):
            for j in range(4):
                orig_i, orig_j = i % clip_height, j % clip_width
                new_clipboard[start_row + i, start_col + j] = original_pattern[orig_i, orig_j]
    
    print("\n📋 Nouveau clipboard (arrangement 2x2):")
    print(new_clipboard)
    
    # Test de collage
    grid = np.zeros((9, 9), dtype=int)
    x_dest, y_dest = 0, 1
    
    for r in range(8):
        for c in range(8):
            grid_r, grid_c = x_dest + r, y_dest + c
            if 0 <= grid_r < 9 and 0 <= grid_c < 9:
                grid[grid_r, grid_c] = new_clipboard[r, c]
    
    print("\n📋 Grille après PASTE [0,1] (nouvelle approche):")
    print(grid)
    
    # Comparer avec l'attendu
    expected = np.array([
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 0, 0, 0, 0, 3, 3, 3, 3],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 3, 3, 3, 3, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    
    matches = np.sum(grid == expected)
    total = grid.size
    percentage = (matches / total) * 100
    
    print(f"\n📊 Comparaison avec l'attendu (nouvelle approche):")
    print(f"Cellules correspondantes: {matches}/{total} ({percentage:.1f}%)")
    
    if matches == total:
        print("✅ SUCCÈS ! Cette approche fonctionne.")
    else:
        print("\n❌ Différences détectées:")
        diff_positions = np.where(grid != expected)
        for i in range(min(10, len(diff_positions[0]))):
            row, col = diff_positions[0][i], diff_positions[1][i]
            print(f"   Position [{row},{col}]: généré={grid[row,col]}, attendu={expected[row,col]}")

if __name__ == "__main__":
    analyze_expected_multiply_behavior()
    test_correct_multiply_implementation()
    propose_multiply_fix()