#!/usr/bin/env python3
"""
Script pour nettoyer les anciennes fonctions executeUnified qui utilisent coordinates/additionalCoordinates
"""

import re

# Lire le fichier
with open('frontend/src/components/resolution/hooks/useAutomation.ts', 'r', encoding='utf-8') as f:
    content = f.read()

# Fonctions à supprimer (remplacées par les versions Block)
functions_to_remove = [
    'executeUnifiedFill',
    'executeUnifiedClear', 
    'executeUnifiedSelect',
    'executeUnifiedExtract',
    'executeUnifiedMultiply',
    'executeUnifiedDivide',
    'executeUnifiedFlip',
    'executeUnifiedRotate',
    'executeUnifiedFloodFill',
    'executeUnifiedReplace',
    'executeUnifiedCopy',
    'executeUnifiedCut',
    'executeUnifiedPaste',
    'executeUnifiedSurround',
    'executeUnifiedEdit'
]

# Pattern pour matcher une fonction complète
def remove_function(content, func_name):
    # Pattern pour matcher la fonction complète avec ses accolades
    pattern = rf'async function {func_name}\([^{{]*\): Promise<boolean> \{{[^}}]*(?:\{{[^}}]*\}}[^}}]*)*\}}'
    
    # Chercher et remplacer
    match = re.search(pattern, content, re.DOTALL)
    if match:
        replacement = f'// SUPPRIMÉ - {func_name} remplacé par système unifié avec coordinateBlocks'
        content = content.replace(match.group(0), replacement)
        print(f"✅ Supprimé: {func_name}")
    else:
        print(f"❌ Non trouvé: {func_name}")
    
    return content

# Supprimer chaque fonction
for func_name in functions_to_remove:
    content = remove_function(content, func_name)

# Écrire le fichier nettoyé
with open('frontend/src/components/resolution/hooks/useAutomation.ts', 'w', encoding='utf-8') as f:
    f.write(content)

print("\n🎉 Nettoyage terminé!")