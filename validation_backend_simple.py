#!/usr/bin/env python3
"""
Script de validation et d'optimisation des scénarios via le backend.
"""

import sys
import os
import django
from pathlib import Path
import json
from datetime import datetime
import argparse
from collections import defaultdict, Counter
import re
from typing import List, Optional, TypedDict, Union

# Configuration Django
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import des services
from backend.myapp.services.scenario_backend_validation_service import ScenarioBackendValidationService
# ScenarioService n'est plus utilisé pour la sauvegarde dans ce script


# --- LOGIQUE D'OPTIMISATION INTÉGRÉE ---

class UnifiedCommand(TypedDict, total=False):
    action: str
    parameters: Optional[str]
    coordinates: List[str]
    additionalCoordinates: List[str]
    raw: str

def parse_unified_command(command: str) -> UnifiedCommand:
    """
    Portage de la fonction parseUnifiedCommand de commandGenerationUtils.ts.
    Analyse une chaîne de commande et la décompose en une structure UnifiedCommand.
    """
    clean_command = command.strip()
    grouped_pattern = re.compile(
        r'^(?P<action>[A-Z]+S?)\s*'
        r'(?P<enclosure>[\{\(])'
        r'(?P<content>.*)'
        r'[\}\)]$', re.DOTALL
    )
    match = grouped_pattern.match(clean_command)
    if match:
        action = match.group('action')
        content = match.group('content')
        enclosure = match.group('enclosure')
        if enclosure == '{':
            return UnifiedCommand(action=action, parameters=content, coordinates=[], additionalCoordinates=[], raw=clean_command)
        if enclosure == '(':
            params_part = ""
            coords_part = content
            coords_match = re.search(r'(\[.*)', content)
            if coords_match:
                params_part = content[:coords_match.start()].strip()
                coords_part = content[coords_match.start():].strip()
            coord_blocks = re.findall(r'\[([^\]]*)\]', coords_part)
            return UnifiedCommand(action=action, parameters=params_part or None, coordinates=coord_blocks, additionalCoordinates=[], raw=clean_command)
    
    simple_pattern = re.compile(
        r'^(?P<action>[A-Z_]+)\s*'
        r'(?P<params>.*?)?\s*'
        r'(?P<coords>(\[[^\]]*\]\s*)*)$'
    )
    match = simple_pattern.match(clean_command)
    if match:
        action = match.group('action')
        params = (match.group('params') or '').strip()
        coords_str = (match.group('coords') or '').strip()
        coord_blocks = re.findall(r'\[([^\]]*)\]', coords_str)
        return UnifiedCommand(action=action, parameters=params or None, coordinates=coord_blocks, additionalCoordinates=[], raw=clean_command)

    return UnifiedCommand(action=clean_command, parameters=None, coordinates=[], additionalCoordinates=[], raw=clean_command)


def group_sub_commands(commands: List[str]) -> List[str]:
    """Factorisation de la logique de regroupement de sous-commandes."""
    regroup_map = defaultdict(list)
    order_keys = []
    for cmd in commands:
        parsed = parse_unified_command(cmd)
        key = (parsed.get('action'), parsed.get('parameters'))
        if key not in order_keys: order_keys.append(key)
        regroup_map[key].append(parsed)

    optimized_cmds = []
    for key in order_keys:
        parsed_cmds = regroup_map[key]
        if len(parsed_cmds) > 1:
            action, params = key
            all_coords = [c for p in parsed_cmds for c in p.get('coordinates', [])]
            new_cmd = str(action) if action else ""
            if params: new_cmd += f" {params}"
            coord_str = " ".join([f"[{c}]" for c in all_coords])
            new_cmd += f" ({coord_str})"
            optimized_cmds.append(new_cmd)
        else:
            optimized_cmds.append(parsed_cmds[0]['raw'])
    return optimized_cmds

# def optimize_transfer_content(command: str) -> str:
#     """Logique spécifique pour optimiser le contenu d'un bloc TRANSFERT."""
#     match = re.match(r'^(TRANSFERT)\s*\{(?P<content>.*)\}$', command, re.DOTALL)
#     if not match: return command
#     content = match.group('content').strip()
#     inner_commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
#     init_command = inner_commands[0] if inner_commands and inner_commands[0].startswith("INIT") else None
#     commands_to_optimize = inner_commands[1:] if init_command else inner_commands
#     optimized_inner_cmds = group_sub_commands(commands_to_optimize)
#     final_cmds = ([init_command] if init_command else []) + optimized_inner_cmds
#     return f"TRANSFERT {{{'; '.join(filter(None, final_cmds))}}}"

def optimize_generic_grouped_command(command: str) -> str:
    """Optimise le contenu d'un bloc générique ACTION+S (ex: EDITS {...})."""
    match = re.match(r'^(?P<action_s>[A-Z]+S)\s*\{(?P<content>.*)\}$', command, re.DOTALL)
    if not match: return command
    action_s = match.group('action_s')
    content = match.group('content').strip()
    inner_commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
    return f"{action_s} {{{'; '.join(group_sub_commands(inner_commands))}}}"

def optimize_grouped_command_rule(commands: List[str]) -> List[str]:
    optimized_commands = []
    for cmd in commands:
        # if cmd.startswith('TRANSFERT'):
        #     optimized_commands.append(optimize_transfer_content(cmd))
        # el
        if re.match(r'^[A-Z]+S\s*\{.*\}$', cmd):
            optimized_commands.append(optimize_generic_grouped_command(cmd))
        else:
            optimized_commands.append(cmd)
    return optimized_commands

def simplify_singleton_groups(commands: List[str]) -> List[str]:
    new_commands = []
    for cmd in commands:
        match_s_brace = re.match(r'^[A-Z]+S\s*\{(?P<content>.*)\}$', cmd)
        if match_s_brace:
            inner_cmds = [c.strip() for c in match_s_brace.group('content').strip().split(';') if c.strip()]
            if len(inner_cmds) == 1: new_commands.append(inner_cmds[0]); continue
        match_s_paren = re.match(r'^(?P<action>[A-Z]+S)\s*\((?P<content>.*)\)$', cmd)
        if match_s_paren:
            new_commands.append(f"{match_s_paren.group('action')[:-1]} ({match_s_paren.group('content')})"); continue
        new_commands.append(cmd)
    return new_commands

def group_same_action_commands(commands: List[str], action: str) -> str:
    parsed_cmds = [parse_unified_command(cmd) for cmd in commands]
    first_params = parsed_cmds[0].get('parameters')
    if all(p.get('parameters') == first_params for p in parsed_cmds):
        all_coords = [c for p in parsed_cmds for c in p.get('coordinates', [])]
        result = action
        if first_params: result += f" {first_params}"
        coord_str = " ".join([f"[{c}]" for c in all_coords])
        return f"{result} ({coord_str})"
    else:
        return f"{action}S {{{'; '.join(commands)}}}"

def group_consecutive_commands(commands: List[str]) -> List[str]:
    result, i, n = [], 0, len(commands)
    groupable = ['FILL', 'EDIT', 'REPLACE', 'CLEAR', 'SURROUND', 'FLOODFILL']
    while i < n:
        first_parsed = parse_unified_command(commands[i])
        action = first_parsed.get('action')
        if not action or action not in groupable:
            result.append(commands[i]); i+=1; continue
        j = i + 1
        while j < n and parse_unified_command(commands[j]).get('action') == action: j+=1
        if (j - i) > 1: result.append(group_same_action_commands(commands[i:j], action)); i = j
        else: result.append(commands[i]); i+=1
    return result

def group_motif_actions(commands: List[str]) -> List[str]:
    motif_start, motif_end = re.compile(r'^\s*(COPY|CUT)\b'), re.compile(r'^\s*PASTE\b')
    motif_transform = re.compile(r'^\s*(FLIP|ROTATE|MULTIPLY|DIVIDE)\b')
    result, i, n = [], 0, len(commands)
    while i < n:
        if motif_start.match(commands[i]):
            j = i + 1
            while j < n and motif_transform.match(commands[j]): j += 1
            if j < n and motif_end.match(commands[j]):
                result.append(f"MOTIF {{{'; '.join(commands[i:j+1])}}}")
                i = j + 1; continue
        result.append(commands[i]); i += 1
    return result

def optimize_command_list(commands: List[str]) -> dict:
    optimized = [cmd.strip() for cmd in commands if cmd and cmd.strip()]
    rules = [
        group_consecutive_commands,
        group_motif_actions,
        optimize_grouped_command_rule,
        simplify_singleton_groups,
    ]
    for _ in range(5):
        snapshot = json.dumps(optimized)
        for rule_func in rules:
            optimized = rule_func(optimized)
        if json.dumps(optimized) == snapshot:
            break
    rules_applied = ['composite_optimization'] if json.dumps(commands) != json.dumps(optimized) else []
    return {"optimized_commands": optimized, "rules_applied": rules_applied}

# --- FIN DE LA LOGIQUE D'OPTIMISATION ---


def get_all_training_scenarios(max_scenarios=None, specific_task=None, subset='training'):
    """Récupère tous les fichiers .agi du répertoire spécifié."""
    training_dir = Path(f'arcdata/{subset}')
    if not training_dir.exists():
        print(f"❌ Répertoire {training_dir} non trouvé")
        return []
    
    pattern = f"{specific_task}_*.agi" if specific_task else "*.agi"
    agi_files = list(training_dir.glob(pattern))
    
    if max_scenarios:
        agi_files = agi_files[:max_scenarios]
    
    scenarios = []
    for agi_file in agi_files:
        filename = agi_file.stem
        parts = filename.split('_')
        if len(parts) >= 3 and parts[1].startswith('TEST'):
            try:
                scenarios.append({
                    'file_path': str(agi_file),
                    'task_id': parts[0],
                    'test_index': int(parts[1][4:]),
                    'filename': filename
                })
            except (ValueError, IndexError):
                print(f"⚠️ Impossible d'extraire les informations de {filename}")
    
    return scenarios

def process_scenario(scenario_info, validation_service, args):
    """Traite un seul scénario pour validation et/ou optimisation."""
    try:
        with open(scenario_info['file_path'], 'r', encoding='utf-8') as f:
            original_content = f.read().strip()
        
        original_commands = original_content.splitlines()
        content_to_process = original_content
        
        optimization_status = 'not_attempted'
        was_optimized = False
        optimized_commands = original_commands  # Initialisation
        optimization_result = optimize_command_list(original_commands)

        if args.optimize_and_save:
            optimized_commands = optimization_result['optimized_commands']
            if optimized_commands != original_commands:
                was_optimized = True
                content_to_process = "\n".join(optimized_commands)

        validation_result = validation_service.validate_scenario(
            subset=args.subnet,
            task_id=scenario_info['task_id'],
            content=content_to_process,
            test_index=scenario_info['test_index']
        )
        is_valid = validation_result.get('is_valid_by_backend', False)
        
        action_taken = was_optimized or not is_valid
        if action_taken or args.verbose:
             print(f"\n[{args.i}/{args.total_scenarios}] 🔍 {scenario_info['filename']}")
        
        if was_optimized and (action_taken or args.verbose):
             print(f"   ✨ Optimisation appliquée: {len(original_commands)} -> {len(optimized_commands)} lignes. Règles: {optimization_result['rules_applied']}")

        if is_valid:
            if args.optimize_and_save and was_optimized:
                try:
                    with open(scenario_info['file_path'], 'w', encoding='utf-8') as f:
                        f.write(content_to_process)
                    optimization_status = 'saved_by_script'
                    print(f"   💾 Fichier optimisé et sauvegardé.")
                except Exception as e:
                    optimization_status = 'error'
                    print(f"   ❌ Erreur de sauvegarde: {e}")
            else:
                optimization_status = 'no_change'
                if args.verbose:
                    print(f"   ✅ VALIDE")
        else:
            error_msg = validation_result.get('error_message', 'Erreur inconnue')
            print(f"   ❌ ÉCHEC - {error_msg}")
            optimization_status = 'validation_failed'

        result_entry = {
            'filename': scenario_info['filename'],
            'task_id': scenario_info['task_id'],
            'status': "SUCCESS" if is_valid else "FAILED",
            'optimization_status': optimization_status
        }
        return True, result_entry

    except Exception as e:
        print(f"Erreur critique lors du traitement {scenario_info.get('filename', 'N/A')}: {e}")
        return False, {'error_message': str(e), 'optimization_status': 'critical_error'}

def main():
    parser = argparse.ArgumentParser(description="Validation et optimisation des scénarios")
    parser.add_argument('--taskid', help='ID de la tâche à tester')
    parser.add_argument('--max', type=int, help='Nombre maximum de scénarios')
    parser.add_argument('--subnet', default='training', help='Sous-répertoire (training/evaluation)')
    parser.add_argument('--optimize-and-save', action='store_true', help='Active l\'optimisation et la sauvegarde')
    parser.add_argument('--verbose', action='store_true', help='Affichage détaillé')
    
    args = parser.parse_args()
    
    mode = "OPTIMISATION & SAUVEGARDE" if args.optimize_and_save else "VALIDATION SIMPLE"
    print(f"🚀 Démarrage du script en mode: {mode}")
    print(f"📁 Répertoire: arcdata/{args.subnet}")
    
    scenarios = get_all_training_scenarios(args.max, args.taskid, args.subnet)
    
    if not scenarios:
        print("❌ Aucun scénario trouvé")
        return
    
    print(f"📊 {len(scenarios)} scénario(s) à traiter")
    
    validation_service = ScenarioBackendValidationService(no_grids=not args.verbose)
    
    results = []
    optimization_stats = Counter()
    
    print("\n" + "="*60 + "\nDÉBUT DU TRAITEMENT\n" + "="*60)
    
    for i, scenario_info in enumerate(scenarios, 1):
        args.i = i
        args.total_scenarios = len(scenarios)
        success, result_entry = process_scenario(scenario_info, validation_service, args)
        
        results.append(result_entry)
        if success:
            optimization_stats[result_entry['optimization_status']] += 1

    total_scenarios = len(results)
    successful_validations = sum(1 for r in results if r.get('status') == "SUCCESS")
    failed_validations = total_scenarios - successful_validations
    success_rate = (successful_validations / total_scenarios * 100) if total_scenarios > 0 else 0
    
    print("\n" + "="*60 + "\nBILAN FINAL\n" + "="*60)
    print(f"📊 Total des scénarios traités: {total_scenarios}")
    print(f"✅ Validations réussies: {successful_validations}")
    print(f"❌ Validations échouées: {failed_validations}")
    print(f"📈 Taux de succès: {success_rate:.1f}%")
    
    if args.optimize_and_save:
        print("\n" + "-"*20 + " Bilan Optimisation " + "-"*20)
        print(f"💾 Scénarios optimisés et sauvegardés: {optimization_stats.get('saved_by_script', 0)}")
        print(f"👌 Scénarios valides sans optimisation: {optimization_stats.get('no_change', 0)}")
        print(f"❌ Scénarios optimisés mais invalides: {optimization_stats.get('validation_failed', 0)}")
        print(f"❓ Erreurs de sauvegarde: {optimization_stats.get('error', 0)}")
    
    print(f"\n⏱️ Traitement terminé")

if __name__ == "__main__":
    main()