// src/components/resolution/hooks/useAutomationStore.ts
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { Grid } from '../../../lib/grid';
import { optimizeCommandList, getOptimizationStats } from '../utils/commandOptimizationEngine';
import { formatCommand } from '../utils/commandFormatUtils';
// formatUnifiedCommand supprimé - utilisation d'une version locale compatible

export type PlaybackState = 'stopped' | 'playing' | 'paused';

export interface AutomationState {
  isOptimizationEnabled: boolean;
  setIsOptimizationEnabled: (enabled: boolean) => void;
  commands: string[][];
  currentTestIndex: number;
  _emptyCommandsArray: string[];
  addCommand: (type: string, params?: string) => void;
  replaceOrAddSelectCommand: (params: string) => void;
  setCommands: (commands: string[]) => void;
  setCurrentTestIndex: (index: number) => void;
  getCurrentCommands: () => string[];
  addUnifiedCommand: (action: string, params?: string, coordinates?: string[], additionalCoords?: string[]) => void;
  addCompleteCommand: (command: string) => void;
  formatUnifiedCommand: (action: string, params?: string, coordinates?: string[], additionalCoords?: string[]) => string;
  optimizeUnifiedCommands: (commands: string[]) => { optimizedCommands: string[], rulesApplied: string[] };
  optimizeAndSetCommands: () => void;
  isRecording: boolean;
  startRecording: () => void;
  stopRecording: () => void;
  undoneCommands: string[];
  removeLastCommand: () => void;
  restoreLastCommand: () => void;
  undoLastSelectIfExists: () => boolean;
  undo: () => Promise<boolean>;
  redo: () => Promise<boolean>;
  currentCommandIndex: number;
  setCurrentCommandIndex: (index: number | ((prevIndex: number) => number)) => void;
  playbackState: PlaybackState;
  setPlaybackState: (state: PlaybackState) => void;
  playbackSpeed: number;
  setPlaybackSpeed: (speed: number) => void;
  initialGridState: Grid | null;
  setInitialGridState: (grid: Grid | null) => void;
  hasLocalChanges: boolean;
  currentScenarioId: string | null;
  clearAllCommands: () => void;
  loadScenarioCommands: (commands: string[], scenarioId: string) => void;
  markAsLocallyModified: () => void;
  saveLocalScenario: () => Promise<void>;
  setCurrentScenario: (scenarioId: string | null) => void;
}

export const useAutomationStore = create(immer<AutomationState>((set, get) => ({
  isOptimizationEnabled: false,
  setIsOptimizationEnabled: (enabled) => set({ isOptimizationEnabled: enabled }),
  commands: [],
  currentTestIndex: 0,
  _emptyCommandsArray: [],

  getCurrentCommands: () => {
    const state = get();
    if (!state.commands[state.currentTestIndex]) {
      return state._emptyCommandsArray;
    }
    return state.commands[state.currentTestIndex].filter(cmd => cmd && typeof cmd === 'string' && cmd.trim().length > 0);
  },

  setCurrentTestIndex: (index) => set((state) => {
    state.currentTestIndex = index;
    if (!state.commands[index]) {
      state.commands[index] = [];
    }
  }),

  addCommand: (type, params = '') => set((state) => {
    if (!state.isRecording) return;
    if (!state.commands[state.currentTestIndex]) {
      state.commands[state.currentTestIndex] = [];
    }
    // ... (le reste de la logique de addCommand reste la même)
  }),

  replaceOrAddSelectCommand: (params) => set((state) => {
    if (!state.isRecording) return;
    if (!state.commands[state.currentTestIndex]) {
      state.commands[state.currentTestIndex] = [];
    }
    const currentCommands = state.commands[state.currentTestIndex];
    const newCommand = formatCommand('SELECT', params);
    if (currentCommands.length > 0 && currentCommands[currentCommands.length - 1].startsWith('SELECT')) {
      state.commands[state.currentTestIndex][currentCommands.length - 1] = newCommand;
    } else {
      state.commands[state.currentTestIndex].push(newCommand);
    }
    state.undoneCommands = [];
    if (state.currentScenarioId) {
      state.hasLocalChanges = true;
    }
  }),

  setCommands: (commands) => set((state) => {
    if (!state.commands[state.currentTestIndex]) {
      state.commands[state.currentTestIndex] = [];
    }
    const validCommands = commands.filter(cmd => cmd && typeof cmd === 'string' && cmd.trim().length > 0);
    const originalText = JSON.stringify(state.commands[state.currentTestIndex]);
    const newText = JSON.stringify(validCommands);

    if (originalText !== newText) {
      state.commands[state.currentTestIndex] = validCommands;
      state.undoneCommands = [];
      if (state.currentScenarioId) {
        state.hasLocalChanges = true;
      }
    }
  }),

  isRecording: false,
  startRecording: () => set((state) => { state.isRecording = true; }),
  stopRecording: () => set((state) => {
    if (state.isRecording) {
      if (state.commands[state.currentTestIndex]) {
        const currentCommands = state.commands[state.currentTestIndex];
        if (currentCommands.length === 0 || currentCommands[currentCommands.length - 1] !== 'END') {
          state.commands[state.currentTestIndex].push('END');
        }
      }
      state.isRecording = false;
    }
  }),

  undoneCommands: [],

  undoLastSelectIfExists: () => {
    const state = get();
    if (!state.commands[state.currentTestIndex]) return false;
    const currentCommands = state.commands[state.currentTestIndex];
    if (currentCommands.length > 0 && currentCommands[currentCommands.length - 1].startsWith('SELECT')) {
      set((draft) => {
        const removedCommand = draft.commands[draft.currentTestIndex].pop();
        if (removedCommand) {
          draft.undoneCommands.push(removedCommand);
        }
      });
      return true;
    }
    return false;
  },

  removeLastCommand: () => set((state) => {
    if (!state.commands[state.currentTestIndex] || state.commands[state.currentTestIndex].length === 0) return;
    const lastCommand = state.commands[state.currentTestIndex].pop();
    if (lastCommand) state.undoneCommands.push(lastCommand);
  }),

  restoreLastCommand: () => set((state) => {
    if (state.undoneCommands.length === 0) return;
    if (!state.commands[state.currentTestIndex]) {
      state.commands[state.currentTestIndex] = [];
    }
    const lastUndoneCommand = state.undoneCommands.pop();
    if (lastUndoneCommand) state.commands[state.currentTestIndex].push(lastUndoneCommand);
  }),

  undo: async () => {
    get().removeLastCommand();
    return true;
  },

  redo: async () => {
    get().restoreLastCommand();
    return true;
  },

  currentCommandIndex: -1,
  setCurrentCommandIndex: (index) => set((state) => {
    if (typeof index === 'function') {
      state.currentCommandIndex = index(state.currentCommandIndex);
    } else {
      state.currentCommandIndex = index;
    }
  }),
  playbackState: 'stopped' as PlaybackState,
  setPlaybackState: (state) => set({ playbackState: state }),
  playbackSpeed: 1,
  setPlaybackSpeed: (speed) => set({ playbackSpeed: speed }),

  initialGridState: null,
  setInitialGridState: (grid) => set({ initialGridState: grid }),

  hasLocalChanges: false,
  currentScenarioId: null,

  clearAllCommands: () => set((state) => {
    state.commands = [];
    state.currentTestIndex = 0;
    state.currentCommandIndex = -1;
    state.undoneCommands = [];
    state.hasLocalChanges = false;
    state.currentScenarioId = null;
  }),

  loadScenarioCommands: (commands, scenarioId) => set((state) => {
    if (!state.commands[state.currentTestIndex]) {
      state.commands[state.currentTestIndex] = [];
    }
    state.commands[state.currentTestIndex] = [...commands];
    state.currentScenarioId = scenarioId;
    state.hasLocalChanges = false;
    state.undoneCommands = [];
    state.currentCommandIndex = -1;
  }),

  markAsLocallyModified: () => set({ hasLocalChanges: true }),

  saveLocalScenario: async () => {
    const state = get();
    if (!state.currentScenarioId) return;
    try {
      const commands = state.getCurrentCommands();
      localStorage.setItem(`scenario_${state.currentScenarioId}_local`, JSON.stringify({ commands, timestamp: Date.now(), scenarioId: state.currentScenarioId }));
      set({ hasLocalChanges: false });
    } catch (error) {
      console.error('[AutomationStore] Error saving local scenario:', error);
      throw error;
    }
  },

  setCurrentScenario: (scenarioId) => set({ currentScenarioId: scenarioId, hasLocalChanges: !scenarioId ? false : get().hasLocalChanges }),

  addUnifiedCommand: (action, params, coordinates, additionalCoords) => set((state) => {
    if (!state.isRecording) return;
    if (!state.commands[state.currentTestIndex]) {
      state.commands[state.currentTestIndex] = [];
    }
    const unifiedCommand = get().formatUnifiedCommand(action, params, coordinates, additionalCoords);
    state.commands[state.currentTestIndex].push(unifiedCommand);
    state.undoneCommands = [];
    if (state.currentScenarioId) {
      state.hasLocalChanges = true;
    }
  }),

  addCompleteCommand: (command) => set((state) => {
    if (!state.isRecording) return;
    if (!state.commands[state.currentTestIndex]) {
      state.commands[state.currentTestIndex] = [];
    }
    state.commands[state.currentTestIndex].push(command);
    state.undoneCommands = [];
    if (state.currentScenarioId) {
      state.hasLocalChanges = true;
    }
  }),

  formatUnifiedCommand: (action, params, coordinates, additionalCoords) => {
    // Version locale compatible avec l'ancien format
    let command = action;
    
    if (params) {
      command += ` ${params}`;
    }
    
    // Formater les coordonnées principales
    if (coordinates && coordinates.length > 0) {
      const coordStr = coordinates.join(' ');
      command += ` [${coordStr}]`;
    }
    
    // Formater les coordonnées additionnelles
    if (additionalCoords && additionalCoords.length > 0) {
      const additionalCoordStr = additionalCoords.join(' ');
      command += ` [${additionalCoordStr}]`;
    }
    
    return command;
  },

  optimizeUnifiedCommands: (commands) => {
    return optimizeCommandList(commands);
  },

  optimizeAndSetCommands: () => set(state => {
    const currentCommands = get().getCurrentCommands();
    if (currentCommands.length < 2) return;

    const optimizedData = get().optimizeUnifiedCommands(currentCommands);

    const stats = getOptimizationStats(currentCommands, optimizedData);

    if (stats.optimizationApplied) {
      state.commands[state.currentTestIndex] = optimizedData.optimizedCommands;
      state.hasLocalChanges = true;
      state.undoneCommands = [];
      console.log(`[AutomationStore] Scenario optimized and marked as locally modified.`, stats);
    } else {
      console.log(`[AutomationStore] Optimization applied, but no changes were made.`, stats);
    }
  }),
})));
