#!/usr/bin/env python3
"""
Test pour comparer les résultats de MULTIPLY entre frontend et backend
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from command_system.command_executor import CommandExecutor
import numpy as np

def test_multiply_detailed():
    """Test détaillé de MULTIPLY 4 false"""
    
    print("🧪 Test détaillé de MULTIPLY 4 false...")
    
    # Créer une grille de test simple
    executor = CommandExecutor()
    
    # Initialiser une grille 9x9
    executor.grid = np.zeros((9, 9), dtype=int)
    executor.height = 9
    executor.width = 9
    
    # Ajouter quelques valeurs pour le test
    executor.grid[0, 1] = 0  # EDIT 0 [0,1]
    executor.grid[1, 2] = 0  # EDIT 0 [1,2] 
    executor.grid[1, 1] = 3  # EDIT 3 [1,1]
    
    print("📋 Grille initiale:")
    print(executor.grid[:3, :3])  # Afficher seulement la partie intéressante
    
    # Exécuter CUT [0,1 1,2]
    print("\n🔄 Exécution de CUT [0,1 1,2]...")
    success = executor._execute_command("CUT [0,1 1,2]")
    print(f"   Succès: {success}")
    
    if executor.clipboard is not None:
        print("📋 Contenu du clipboard après CUT:")
        print(executor.clipboard)
        print(f"   Dimensions: {executor.clipboard.shape}")
        
        if hasattr(executor, 'clipboard_mask') and executor.clipboard_mask is not None:
            print("🎭 Masque du clipboard:")
            print(executor.clipboard_mask)
    
    print("\n📋 Grille après CUT:")
    print(executor.grid[:3, :3])
    
    # Exécuter MULTIPLY 4 false
    print("\n🔄 Exécution de MULTIPLY 4 false...")
    success = executor._execute_command("MULTIPLY 4 false")
    print(f"   Succès: {success}")
    
    if executor.clipboard is not None:
        print("📋 Contenu du clipboard après MULTIPLY:")
        print(executor.clipboard)
        print(f"   Dimensions: {executor.clipboard.shape}")
        
        if hasattr(executor, 'clipboard_mask') and executor.clipboard_mask is not None:
            print("🎭 Masque du clipboard après MULTIPLY:")
            print(executor.clipboard_mask)
    
    # Exécuter PASTE [0,1 1,2]
    print("\n🔄 Exécution de PASTE [0,1 1,2]...")
    success = executor._execute_command("PASTE [0,1 1,2]")
    print(f"   Succès: {success}")
    
    print("\n📋 Grille finale:")
    print(executor.grid)
    
    return executor.grid

def test_with_real_task():
    """Test avec les vraies données de la tâche"""
    
    print("\n" + "="*60)
    print("🧪 Test avec données réelles de la tâche 4522001f...")
    
    # Simuler les données de la tâche (vous devrez ajuster selon la vraie tâche)
    task_data = {
        'test': [{
            'input': [
                [0, 0, 0],
                [0, 0, 0], 
                [0, 0, 0]
            ],
            'output': [
                [0, 0, 0, 0, 0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0, 0, 0, 0]
            ]
        }]
    }
    
    commands = [
        'INPUT',
        'EDIT 0 [0,1]',
        'EDIT 0 [1,2]', 
        'EDIT 3 [1,1]',
        'RESIZE 9x9',
        'CUT [0,1 1,2]',
        'MULTIPLY 4 false',
        'PASTE [0,1 1,2]',
        'END'
    ]
    
    executor = CommandExecutor(task_data, 0)
    result = executor.execute_commands(commands)
    
    print(f"📊 Résultat: {result['success']}")
    if not result['success']:
        print(f"   Erreur: {result['error']}")
    
    if result['grid']:
        print("📋 Grille générée:")
        grid = np.array(result['grid'])
        print(grid)
        
        # Comparer avec la sortie attendue
        expected = np.array(task_data['test'][0]['output'])
        matches = np.sum(grid == expected)
        total = grid.size
        percentage = (matches / total) * 100
        
        print(f"\n📊 Comparaison:")
        print(f"   Cellules correspondantes: {matches}/{total} ({percentage:.1f}%)")
        
        if matches != total:
            print("❌ Différences détectées:")
            diff_positions = np.where(grid != expected)
            for i in range(min(10, len(diff_positions[0]))):  # Afficher max 10 différences
                row, col = diff_positions[0][i], diff_positions[1][i]
                print(f"   Position [{row},{col}]: généré={grid[row,col]}, attendu={expected[row,col]}")
    
    return result

if __name__ == "__main__":
    # Test détaillé
    grid = test_multiply_detailed()
    
    # Test avec vraies données
    result = test_with_real_task()