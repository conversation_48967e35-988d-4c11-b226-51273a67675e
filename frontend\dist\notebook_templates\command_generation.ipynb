{"metadata": {"language_info": {"codemirror_mode": {"name": "python", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8"}, "kernelspec": {"display_name": "Python (Pyodide)", "language": "python", "name": "python"}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "markdown", "source": ["# Generation de commandes\n", "\n", "Ce notebook se concentre sur la génération de commandes d'automatisation pour résoudre une tâche ARC."], "metadata": {}}, {"cell_type": "code", "source": ["# Importer le module arc_puzzle et les bibliothèques nécessaires\n", "import arc_puzzle as arc\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Vérifier que le module est correctement chargé\n", "print(\"Module arc_puzzle chargé avec succès!\")"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {}, "source": ["# Importer le module arc_utils et les bibliothèques nécessaires", "import arc_utils", "import numpy as np", "import matplotlib.pyplot as plt", "import json", "", "# Vérifier que le module est correctement chargé", "print(\"Module arc_utils chargé avec succès!\")"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Récupération des données de la tâche\n", "\nCommençons par récupérer les données de la tâche courante."]}, {"cell_type": "code", "metadata": {}, "source": ["# Variables pour stocker les données de la tâche", "task_id = None", "train_input = []", "train_output = []", "test_input = []", "training_examples = []", "test_example = None", "", "# Essayer de récupérer les données depuis le frontend", "try:", "    display(Javascript(\"\"\"", "    try {", "        // Récupérer les données de la tâche depuis le frontend", "        const taskData = window.parent.currentTask;", "        if (taskData) {", "            // Extraire l'ID de la tâche", "            const taskId = window.parent.taskName || 'unknown';", "            ", "            // Extraire les données d'entrée et de sortie d'entraînement", "            const trainInput = taskData.train.map(pair => pair.input);", "            const trainOutput = taskData.train.map(pair => pair.output);", "            ", "            // Extraire les données d'entrée de test", "            const testInput = taskData.test.map(pair => pair.input);", "            ", "            // Assigner les variables Python", "            IPython.notebook.kernel.execute(`task_id = \"${taskId}\"`);", "            IPython.notebook.kernel.execute(`train_input = ${JSON.stringify(trainInput)}`);", "            IPython.notebook.kernel.execute(`train_output = ${JSON.stringify(trainOutput)}`);", "            IPython.notebook.kernel.execute(`test_input = ${JSON.stringify(testInput)}`);", "            ", "            console.log('Donn<PERSON> de la tâche récupérées avec succès');", "        } else {", "            console.error('Aucune tâche chargée dans le frontend');", "        }", "    } catch (e) {", "        console.error('Erreur lors de la récupération des données de la tâche:', e);", "    }", "    \"\"\"))", "    ", "    # Attendre que les données soient chargées", "    import time", "    time.sleep(1)", "except Exception as e:", "    print(f\"Erreur lors de la récupération des données depuis le frontend: {e}\")", "", "# Si les données n'ont pas été récupérées depuis le frontend, essayer de les charger depuis arcdata", "if not train_input and task_id:", "    print(f\"Tentative de chargement de la tâche {task_id} depuis arcdata...\")", "    try:", "        task_data = arc_utils.load_task(task_id)", "        if task_data:", "            train_input = [pair[\"input\"] for pair in task_data[\"train\"]]", "            train_output = [pair[\"output\"] for pair in task_data[\"train\"]]", "            test_input = [pair[\"input\"] for pair in task_data[\"test\"]]", "            print(f\"Tâche {task_id} chargée avec succès depuis arcdata\")", "    except Exception as e:", "        print(f\"Erreur lors du chargement de la tâche depuis arcdata: {e}\")", "", "# C<PERSON>er les exemples d'entraînement et de test", "if train_input and train_output:", "    training_examples = [{'input': inp, 'output': out} for inp, out in zip(train_input, train_output)]", "    if test_input and len(test_input) > 0:", "        test_example = {'input': test_input[0]}", "", "# Afficher un résumé", "print(f\"Nombre d'exemples d'entraînement: {len(training_examples)}\")", "print(f\"Exemple de test disponible: {test_example is not None}\")"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonctions d'analyse avancées\n", "\nCes fonctions permettent d'analyser en détail les grilles et les transformations."]}, {"cell_type": "code", "metadata": {}, "source": ["# Fonctions d'analyse avancées", "", "def analyze_patterns(grid):", "    \"\"\"Analyse les patterns dans une grille\"\"\"", "    if isinstance(grid, list):", "        grid = np.array(grid)", "", "    # Dimensions de la grille", "    height, width = grid.shape", "    print(f\"Dimensions: {height}x{width}\")", "", "    # Valeurs uniques et leur fréquence", "    values, counts = np.unique(grid, return_counts=True)", "    print(\"\\nValeurs uniques et leur fréquence:\")", "    for val, count in zip(values, counts):", "        print(f\"  Valeur {val}: {count} occurrences ({count/(height*width)*100:.1f}%)\")", "", "    # Recherche de symétries", "    print(\"\\nAnalyse des symétries:\")", "", "    # Symétrie horizontale", "    is_h_symmetric = np.array_equal(grid, np.flipud(grid))", "    print(f\"  Symétrie horizontale: {'Oui' if is_h_symmetric else 'Non'}\")", "", "    # Symétrie verticale", "    is_v_symmetric = np.array_equal(grid, np.fliplr(grid))", "    print(f\"  Symétrie verticale: {'Oui' if is_v_symmetric else 'Non'}\")", "", "    # <PERSON><PERSON><PERSON><PERSON><PERSON> diagonale (si la grille est carrée)", "    if height == width:", "        is_diag_symmetric = np.array_equal(grid, grid.T)", "        print(f\"  Symétrie diagonale: {'Oui' if is_diag_symmetric else 'Non'}\")", "", "    # Recherche de motifs répétitifs", "    print(\"\\nRecherche de motifs répétitifs:\")", "", "    # Motifs 2x2", "    if height >= 2 and width >= 2:", "        patterns_2x2 = {}", "        for y in range(height-1):", "            for x in range(width-1):", "                pattern = tuple(grid[y:y+2, x:x+2].flatten())", "                patterns_2x2[pattern] = patterns_2x2.get(pattern, 0) + 1", "", "        # Afficher les motifs les plus fréquents", "        if patterns_2x2:", "            most_common = sorted(patterns_2x2.items(), key=lambda x: x[1], reverse=True)[:3]", "            print(\"  Motifs 2x2 les plus fréquents:\")", "            for pattern, count in most_common:", "                print(f\"    {pattern}: {count} occurrences\")", "", "    return {", "        \"dimensions\": (height, width),", "        \"unique_values\": list(zip(values.tolist(), counts.tolist())),", "        \"symmetries\": {", "            \"horizontal\": is_h_symmetric,", "            \"vertical\": is_v_symmetric,", "            \"diagonal\": is_diag_symmetric if height == width else None", "        }", "    }", "", "def analyze_transformations(input_grid, output_grid):", "    \"\"\"Analyse les transformations entre deux grilles\"\"\"", "    if isinstance(input_grid, list):", "        input_grid = np.array(input_grid)", "    if isinstance(output_grid, list):", "        output_grid = np.array(output_grid)", "", "    # Vérifier si les dimensions sont les mêmes", "    if input_grid.shape != output_grid.shape:", "        print(f\"Les dimensions sont différentes: {input_grid.shape} -> {output_grid.shape}\")", "        return None", "", "    height, width = input_grid.shape", "", "    # Compter les cellules modifiées", "    diff = (input_grid != output_grid)", "    num_diff = np.sum(diff)", "    print(f\"Nombre de cellules modifiées: {num_diff} sur {input_grid.size} ({num_diff/input_grid.size*100:.1f}%)\")", "", "    # Analyser les valeurs", "    input_values = np.unique(input_grid)", "    output_values = np.unique(output_grid)", "    print(f\"Valeurs dans l'entrée: {input_values}\")", "    print(f\"Valeurs dans la sortie: {output_values}\")", "", "    # Analyser les transformations par valeur", "    print(\"\\nTransformations par valeur:\")", "    transformations = {}", "    for val in input_values:", "        mask = (input_grid == val)", "        if np.sum(mask) > 0:", "            output_vals, counts = np.unique(output_grid[mask], return_counts=True)", "            transformations[int(val)] = {int(o_val): int(count) for o_val, count in zip(output_vals, counts)}", "", "            print(f\"  Valeur {val} -> \", end=\"\")", "            for o_val, count in zip(output_vals, counts):", "                print(f\"{o_val}: {count} ({count/np.sum(mask)*100:.1f}%), \", end=\"\")", "            print()", "", "    # Vérifier les transformations courantes", "    print(\"\\nVérification des transformations courantes:\")", "", "    # Rotation de 90°", "    rot90 = np.rot90(input_grid)", "    is_rot90 = np.array_equal(rot90, output_grid)", "    print(f\"  Rotation 90°: {'Oui' if is_rot90 else 'Non'}\")", "", "    # Rotation de 180°", "    rot180 = np.rot90(input_grid, 2)", "    is_rot180 = np.array_equal(rot180, output_grid)", "    print(f\"  Rotation 180°: {'Oui' if is_rot180 else 'Non'}\")", "", "    # Rotation de 270°", "    rot270 = np.rot90(input_grid, 3)", "    is_rot270 = np.array_equal(rot270, output_grid)", "    print(f\"  Rotation 270°: {'Oui' if is_rot270 else 'Non'}\")", "", "    # Miroir horizontal", "    flip_h = np.flipud(input_grid)", "    is_flip_h = np.array_equal(flip_h, output_grid)", "    print(f\"  Miroir horizontal: {'Oui' if is_flip_h else 'Non'}\")", "", "    # Miroir vertical", "    flip_v = np.fliplr(input_grid)", "    is_flip_v = np.array_equal(flip_v, output_grid)", "    print(f\"  Miroir vertical: {'Oui' if is_flip_v else 'Non'}\")", "", "    # Transposition (si la grille est carrée)", "    if height == width:", "        transpose = input_grid.T", "        is_transpose = np.array_equal(transpose, output_grid)", "        print(f\"  Transposition: {'<PERSON><PERSON>' if is_transpose else 'Non'}\")", "", "    # Afficher une grille des différences", "    print(\"\\nGrille des différences (cellules modifiées):\")", "    diff_grid = np.zeros_like(input_grid)", "    diff_grid[diff] = output_grid[diff]", "    display_grid(diff_grid, \"Différences\")", "", "    return {", "        \"num_diff\": int(num_diff),", "        \"diff_percentage\": float(num_diff/input_grid.size*100),", "        \"transformations\": transformations,", "        \"common_transformations\": {", "            \"rotation_90\": is_rot90,", "            \"rotation_180\": is_rot180,", "            \"rotation_270\": is_rot270,", "            \"flip_horizontal\": is_flip_h,", "            \"flip_vertical\": is_flip_v,", "            \"transpose\": is_transpose if height == width else None", "        }", "    }", "", "def detect_objects(grid, background=0):", "    \"\"\"Détecte les objets dans une grille\"\"\"", "    if isinstance(grid, list):", "        grid = np.array(grid)", "", "    height, width = grid.shape", "", "    # C<PERSON>er une grille de labels pour les objets", "    labels = np.zeros_like(grid)", "    next_label = 1", "", "    # Fonction pour étiqueter un objet de manière récursive", "    def label_object(y, x, label):", "        if y < 0 or y >= height or x < 0 or x >= width:", "            return 0  # Hors limites", "", "        if grid[y, x] == background or labels[y, x] != 0:", "            return 0  # <PERSON><PERSON> ou d<PERSON><PERSON><PERSON>", "", "        # Étiqueter la cellule", "        labels[y, x] = label", "        size = 1", "", "        # Éti<PERSON>er les voisins (4-connexité)", "        size += label_object(y-1, x, label)  # Haut", "        size += label_object(y+1, x, label)  # Bas", "        size += label_object(y, x-1, label)  # Gauche", "        size += label_object(y, x+1, label)  # Droite", "", "        return size", "", "    # Parcourir la grille et étiqueter les objets", "    objects = {}", "    for y in range(height):", "        for x in range(width):", "            if grid[y, x] != background and labels[y, x] == 0:", "                size = label_object(y, x, next_label)", "                objects[next_label] = {", "                    \"value\": int(grid[y, x]),", "                    \"size\": size,", "                    \"label\": next_label", "                }", "                next_label += 1", "", "    print(f\"Nombre d'objets détectés: {len(objects)}\")", "", "    # Afficher les informations sur les objets", "    if objects:", "        print(\"\\nInformations sur les objets:\")", "        for label, obj in objects.items():", "            print(f\"  Objet {label}: valeur {obj['value']}, taille {obj['size']}\")", "", "    # C<PERSON>er une grille colorée pour visualiser les objets", "    if objects:", "        print(\"\\nVisualisation des objets:\")", "        # Utiliser une palette de couleurs différente pour chaque objet", "        object_grid = np.zeros_like(grid)", "        for y in range(height):", "            for x in range(width):", "                if labels[y, x] > 0:", "                    object_grid[y, x] = labels[y, x]", "", "        display_grid(object_grid, \"Objets détectés\")", "", "    return objects"], "outputs": []}, {"cell_type": "code", "metadata": {}, "source": ["# Exemple d'utilisation des fonctions d'analyse avancées\n", "if training_examples:\n", "    example = training_examples[0]\n", "    \n", "    print(\"Analyse des patterns dans la grille d'entrée:\")\n", "    analyze_patterns(example['input'])\n", "    \n", "    print(\"\\nAnalyse des transformations entre l'entrée et la sortie:\")\n", "    analyze_transformations(example['input'], example['output'])\n", "    \n", "    print(\"\\nDétection des objets dans la grille d'entrée:\")\n", "    detect_objects(example['input'])\n"], "outputs": []}, {"cell_type": "markdown", "source": ["## 1. Récupération des données de la tâche\n", "\n", "Récupérons les données de la tâche courante."], "metadata": {}}, {"cell_type": "code", "source": ["# Récupérer la tâche courante\n", "task = arc.get_current_task()\n", "training_examples = arc.get_training_examples()\n", "test_example = arc.get_test_example()\n", "\n", "# Afficher un résumé\n", "print(f\"Nombre d'exemples d'entraînement: {len(training_examples)}\")\n", "print(f\"Exemple de test disponible: {test_example is not None}\")"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 2. Visualisation des exemples\n", "\n", "Affichons rapidement les exemples pour comprendre la tâche."], "metadata": {}}, {"cell_type": "code", "source": ["# Afficher les exemples d'entraînement\n", "for i, example in enumerate(training_examples):\n", "    print(f\"\\nExemple d'entraînement {i+1}:\")\n", "    print(\"Input:\")\n", "    arc.display_grid(example[\"input\"])\n", "    print(\"Output:\")\n", "    arc.display_grid(example[\"output\"])\n", "\n", "# Afficher l'exemple de test\n", "if test_example:\n", "    print(\"\\nExemple de test:\")\n", "    print(\"Input:\")\n", "    arc.display_grid(test_example[\"input\"])"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 3. Génération de commandes manuelles\n", "\n", "Créons manuellement une séquence de commandes pour résoudre la tâche."], "metadata": {}}, {"cell_type": "code", "source": ["# C<PERSON>er un constructeur de commandes\n", "cmd_builder = arc.CommandBuilder()\n", "\n", "# Exemple: si l'exemple de test est une grille 3x3\n", "if test_example:\n", "    test_input = np.array(test_example[\"input\"])\n", "    height, width = test_input.shape\n", "    \n", "    # Initialiser la grille avec les dimensions de l'entrée\n", "    cmd_builder.init(width, height)\n", "    \n", "    # Ajouter des commandes manuellement\n", "    # Exemple: remplir toute la grille avec la valeur 1\n", "    cmd_builder.fill(1, 0, 0, width-1, height-1)\n", "    \n", "    # Exemple: éditer quelques cellules\n", "    cmd_builder.edit(1, 1, 2)\n", "    \n", "    # Ajouter la commande PROPOSE\n", "    cmd_builder.propose()\n", "    \n", "    # Construire la liste de commandes\n", "    commands = cmd_builder.build()\n", "    \n", "    print(\"Commandes générées manuellement:\")\n", "    for cmd in commands:\n", "        print(f\"  {cmd}\")\n", "else:\n", "    print(\"Aucun exemple de test disponible.\")"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 4. Simulation de l'exécution des commandes\n", "\n", "Simulons l'exécution des commandes pour vérifier le résultat."], "metadata": {}}, {"cell_type": "code", "source": ["# Fonction pour simuler l'exécution des commandes\n", "def simulate_commands(commands):\n", "    grid = None\n", "    \n", "    for cmd in commands:\n", "        parts = cmd.split()\n", "        command = parts[0]\n", "        \n", "        if command == \"INIT\":\n", "            width = int(parts[1])\n", "            height = int(parts[2])\n", "            grid = np.zeros((height, width), dtype=int)\n", "        \n", "        elif command == \"FILL\" and grid is not None:\n", "            value = int(parts[1])\n", "            x1 = int(parts[2])\n", "            y1 = int(parts[3])\n", "            x2 = int(parts[4])\n", "            y2 = int(parts[5])\n", "            grid[y1:y2+1, x1:x2+1] = value\n", "        \n", "        elif command == \"EDIT\" and grid is not None:\n", "            x = int(parts[1])\n", "            y = int(parts[2])\n", "            value = int(parts[3])\n", "            grid[y, x] = value\n", "    \n", "    return grid\n", "\n", "# Simuler l'exécution des commandes\n", "if 'commands' in locals():\n", "    result_grid = simulate_commands(commands)\n", "    \n", "    print(\"Résultat de l'exécution des commandes:\")\n", "    arc.display_grid(result_grid)\n", "else:\n", "    print(\"Aucune commande à simuler.\")"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 5. Génération automatique de commandes\n", "\n", "Développons une fonction pour générer automatiquement des commandes à partir d'une grille."], "metadata": {}}, {"cell_type": "code", "source": ["# Fonction pour générer des commandes à partir d'une grille\n", "def generate_commands_from_grid(grid):\n", "    if isinstance(grid, list):\n", "        grid = np.array(grid)\n", "    \n", "    height, width = grid.shape\n", "    cmd_builder = arc.CommandBuilder()\n", "    \n", "    # Initialiser la grille\n", "    cmd_builder.init(width, height)\n", "    \n", "    # Stratégie 1: Utiliser EDIT pour chaque cellule non nulle\n", "    for y in range(height):\n", "        for x in range(width):\n", "            if grid[y, x] > 0:\n", "                cmd_builder.edit(x, y, int(grid[y, x]))\n", "    \n", "    # Stratégie 2 (alternative): Rechercher des rectangles de même valeur\n", "    # Cette stratégie est plus complexe et n'est pas implémentée ici\n", "    \n", "    # Ajouter la commande PROPOSE\n", "    cmd_builder.propose()\n", "    \n", "    return cmd_builder.build()\n", "\n", "# Tester la fonction sur un exemple d'entraînement\n", "if training_examples:\n", "    example_output = training_examples[0][\"output\"]\n", "    auto_commands = generate_commands_from_grid(example_output)\n", "    \n", "    print(\"Commandes générées automatiquement pour l'exemple d'entraînement:\")\n", "    for cmd in auto_commands:\n", "        print(f\"  {cmd}\")\n", "    \n", "    # Vérifier que les commandes reproduisent bien la grille\n", "    result = simulate_commands(auto_commands)\n", "    \n", "    print(\"\\nGrille originale:\")\n", "    arc.display_grid(example_output)\n", "    \n", "    print(\"Grille reconstruite:\")\n", "    arc.display_grid(result)\n", "    \n", "    # Vérifier l'égalité\n", "    is_equal = np.array_equal(result, np.array(example_output))\n", "    print(f\"Les grilles sont identiques: {is_equal}\")\n", "else:\n", "    print(\"Aucun exemple d'entraînement disponible.\")"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 6. Application à l'exemple de test\n", "\n", "<PERSON><PERSON><PERSON>, r<PERSON><PERSON>vons l'exemple de test et générons les commandes correspondantes."], "metadata": {}}, {"cell_type": "code", "source": ["# Fonction pour résoudre la tâche (à adapter selon la tâche)\n", "def solve_task(input_grid):\n", "    if isinstance(input_grid, list):\n", "        input_grid = np.array(input_grid)\n", "    \n", "    # TODO: Implémenter la logique de résolution ici\n", "    # Exemple simple: créer une croix autour des cellules non nulles\n", "    height, width = input_grid.shape\n", "    output_grid = np.zeros((height, width), dtype=int)\n", "    \n", "    for y in range(height):\n", "        for x in range(width):\n", "            if input_grid[y, x] > 0:\n", "                # Cellule centrale\n", "                output_grid[y, x] = input_grid[y, x]\n", "                \n", "                # Cellules adjacentes (croix)\n", "                if x > 0:\n", "                    output_grid[y, x-1] = input_grid[y, x]\n", "                if x < width-1:\n", "                    output_grid[y, x+1] = input_grid[y, x]\n", "                if y > 0:\n", "                    output_grid[y-1, x] = input_grid[y, x]\n", "                if y < height-1:\n", "                    output_grid[y+1, x] = input_grid[y, x]\n", "    \n", "    return output_grid\n", "\n", "# Résoudre l'exemple de test et générer les commandes\n", "if test_example:\n", "    # R<PERSON><PERSON><PERSON> la tâche\n", "    test_solution = solve_task(test_example[\"input\"])\n", "    \n", "    print(\"Solution proposée pour l'exemple de test:\")\n", "    arc.display_grid(test_solution)\n", "    \n", "    # G<PERSON><PERSON><PERSON> les commandes\n", "    test_commands = generate_commands_from_grid(test_solution)\n", "    \n", "    print(\"\\nCommandes générées pour la solution:\")\n", "    for cmd in test_commands:\n", "        print(f\"  {cmd}\")\n", "    \n", "    # Envoyer les commandes à l'éditeur d'automatisation\n", "    arc.send_to_automation_editor(test_commands)\n", "else:\n", "    print(\"Aucun exemple de test disponible.\")"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 7. Optimisation des commandes\n", "\n", "Optimisons les commandes pour réduire leur nombre."], "metadata": {}}, {"cell_type": "code", "source": ["# Fonction pour optimiser les commandes\n", "def optimize_commands(commands):\n", "    # Filtrer les commandes INIT et PROPOSE\n", "    init_cmd = next((cmd for cmd in commands if cmd.startswith(\"INIT\")), None)\n", "    other_cmds = [cmd for cmd in commands if not cmd.startswith(\"INIT\") and not cmd == \"PROPOSE\"]\n", "    \n", "    # TODO: Implémenter des stratégies d'optimisation\n", "    # Par exemple, regrouper les EDIT adjacents en FILL\n", "    \n", "    # Reconstruire la liste de commandes\n", "    optimized = []\n", "    if init_cmd:\n", "        optimized.append(init_cmd)\n", "    optimized.extend(other_cmds)\n", "    optimized.append(\"PROPOSE\")\n", "    \n", "    return optimized\n", "\n", "# Optimiser les commandes générées\n", "if 'test_commands' in locals():\n", "    optimized_commands = optimize_commands(test_commands)\n", "    \n", "    print(f\"Nombre de commandes avant optimisation: {len(test_commands)}\")\n", "    print(f\"Nombre de commandes après optimisation: {len(optimized_commands)}\")\n", "    \n", "    print(\"\\nCommandes optimisées:\")\n", "    for cmd in optimized_commands:\n", "        print(f\"  {cmd}\")\n", "    \n", "    # Vérifier que les commandes optimisées produisent le même résultat\n", "    original_result = simulate_commands(test_commands)\n", "    optimized_result = simulate_commands(optimized_commands)\n", "    \n", "    is_equal = np.array_equal(original_result, optimized_result)\n", "    print(f\"\\nLes résultats sont identiques: {is_equal}\")\n", "else:\n", "    print(\"Aucune commande à optimiser.\")"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## 8. Envo<PERSON> des commandes finales\n", "\n", "Envoyons les commandes optimisées à l'éditeur d'automatisation."], "metadata": {}}, {"cell_type": "code", "source": ["# Envoyer les commandes optimisées\n", "if 'optimized_commands' in locals():\n", "    print(\"Envoi des commandes optimisées à l'éditeur d'automatisation...\")\n", "    arc.send_to_automation_editor(optimized_commands)\n", "    print(\"Commandes envoyées avec succès!\")\n", "else:\n", "    print(\"Aucune commande optimisée à envoyer.\")"], "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Guide d'utilisation du notebook", "", "Ce notebook vous permet d'analyser et de résoudre des tâches ARC (Abstraction and Reasoning Corpus). Voici comment l'utiliser efficacement :", "", "## Structure du notebook", "", "Le notebook est organisé en sections :", "1. **Configuration et importation** : Importe les bibliothèques nécessaires", "2. **Récupération des données** : Charge les données de la tâche courante", "3. **Visualisation des exemples** : Affiche les grilles d'entrée et de sortie", "4. **Analyse des données** : Fournit des outils pour analyser les patterns et transformations", "5. **Développement de la solution** : Espace pour implémenter votre solution", "6. **Génération de commandes** : Convertit votre solution en commandes pour l'éditeur d'automatisation", "", "## Fonctions disponibles", "", "### Fonctions de base", "- `display_grid(grid, title=None)` : Affiche une grille avec un titre optionnel", "- `arc_utils.load_task(task_id)` : Charge une tâche ARC depuis le répertoire arcdata", "- `arc_utils.validate_commands(commands)` : <PERSON><PERSON> une liste de commandes", "- `arc_utils.execute_commands(commands)` : Exécute une liste de commandes et retourne la grille résultante", "- `arc_utils.generate_commands_from_grid(grid)` : <PERSON>énère des commandes à partir d'une grille", "", "### Fonctions d'analyse avancées", "- `analyze_patterns(grid)` : Analyse les patterns dans une grille (symétries, motifs répétitifs)", "- `analyze_transformations(input_grid, output_grid)` : Analyse les transformations entre deux grilles", "- `detect_objects(grid, background=0)` : Détecte les objets dans une grille", "", "## Commandes d'automatisation", "", "Les commandes d'automatisation permettent de construire une solution. Voici les commandes disponibles :", "", "- `INIT width height` : Initialise une grille vide de dimensions width × height", "- `EDIT x y value` : Définit la valeur de la cellule aux coordonnées (x, y)", "- `COPY x1 y1 x2 y2` : Co<PERSON> la valeur de la cellule (x1, y1) vers la cellule (x2, y2)", "- `FILL x y width height value` : Remplit un rectangle avec une valeur", "- `PROPOSE` : Propose la solution actuelle", "", "## Envoi des commandes à l'éditeur d'automatisation", "", "Pour envoyer des commandes à l'éditeur d'automatisation, utilisez le code suivant :", "", "```python", "# G<PERSON><PERSON><PERSON> les commandes", "commands = arc_utils.generate_commands_from_grid(output_grid)", "", "# Affiche<PERSON> les commandes", "for cmd in commands:", "    print(f\"  {cmd}\")", "", "# Envoyer les commandes à l'éditeur d'automatisation", "from IPython.display import display, Javascript", "display(Javascript(\"\"\"", "try {", "    // Récupérer les commandes générées", "    const commands = %s;", "    ", "    // Envoyer les commandes au frontend", "    if (window.parent.notebookCommandsCallback) {", "        window.parent.notebookCommandsCallback(commands);", "        console.log('Commandes envoyées au frontend:', commands);", "    } else {", "        console.error('Callback pour les commandes non disponible dans le frontend');", "    }", "} catch (e) {", "    console.error('<PERSON><PERSON>ur lors de l\\'envoi des commandes au frontend:', e);", "}", "\"\"\" % json.dumps(commands)))", "```", "", "## Conseils pour résoudre les tâches ARC", "", "1. **Examinez attentivement les exemples** : Comprenez la transformation entre l'entrée et la sortie", "2. **Recherchez des patterns** : Utilisez les fonctions d'analyse pour détecter des patterns", "3. **Testez différentes hypothèses** : Implémentez et testez plusieurs approches", "4. **Validez votre solution** : Vérifiez que votre solution fonctionne sur tous les exemples d'entraînement", "5. **<PERSON><PERSON><PERSON><PERSON> des commandes** : Convertissez votre solution en commandes pour l'éditeur d'automatisation"]}]}