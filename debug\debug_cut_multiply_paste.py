#!/usr/bin/env python3
"""
Debug spécifique pour la séquence CUT/MULTIPLY/PASTE
Reproduit le problème de génération de grille incorrecte
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from command_system.command_executor import CommandExecutor
import numpy as np

def test_cut_multiply_paste_sequence():
    """Test de la séquence complète CUT/MULTIPLY/PASTE"""
    
    print("🧪 Test de la séquence CUT/MULTIPLY/PASTE...")
    
    # Données de test basées sur les logs
    task_data = {
        'test': [{
            'input': [
                [0, 0, 0],
                [0, 0, 0], 
                [0, 0, 0]
            ],
            'output': [
                [0, 0, 0, 0, 0, 3, 3, 3, 3],
                [0, 0, 0, 0, 0, 3, 3, 3, 3],
                [0, 0, 0, 0, 0, 3, 3, 3, 3],
                [0, 0, 0, 0, 0, 3, 3, 3, 3],
                [0, 3, 3, 3, 3, 0, 0, 0, 0],
                [0, 3, 3, 3, 3, 0, 0, 0, 0],
                [0, 3, 3, 3, 3, 0, 0, 0, 0],
                [0, 3, 3, 3, 3, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0, 0, 0, 0]
            ]
        }]
    }
    
    commands = [
        'INPUT',
        'EDIT 0 [0,1]',
        'EDIT 0 [1,2]', 
        'EDIT 3 [1,1]',
        'RESIZE 9x9',
        'CUT [0,1 1,2]',
        'MULTIPLY 4 false',
        'PASTE [0,1 1,2]',
        'END'
    ]
    
    executor = CommandExecutor(task_data, 0)
    
    print("📋 Exécution étape par étape...")
    
    # Exécuter chaque commande individuellement pour debug
    for i, command in enumerate(commands):
        print(f"\n--- Étape {i+1}: {command} ---")
        
        success = executor._execute_command(command)
        print(f"Succès: {success}")
        
        if executor.error:
            print(f"Erreur: {executor.error}")
            break
            
        if executor.grid is not None:
            print(f"Grille actuelle ({executor.grid.shape}):")
            print(executor.grid)
            
        if command.startswith('CUT'):
            print("📋 Contenu du clipboard après CUT:")
            if executor.clipboard is not None:
                print(f"Clipboard shape: {executor.clipboard.shape}")
                print(executor.clipboard)
                if executor.clipboard_mask is not None:
                    print("Clipboard mask:")
                    print(executor.clipboard_mask)
            else:
                print("Clipboard vide!")
                
        elif command.startswith('MULTIPLY'):
            print("📋 Contenu du clipboard après MULTIPLY:")
            if executor.clipboard is not None:
                print(f"Clipboard shape: {executor.clipboard.shape}")
                print(executor.clipboard)
                if executor.clipboard_mask is not None:
                    print("Clipboard mask:")
                    print(executor.clipboard_mask)
            else:
                print("Clipboard vide!")
    
    # Comparer avec la sortie attendue
    if executor.grid is not None:
        expected = np.array(task_data['test'][0]['output'])
        generated = executor.grid
        
        print(f"\n📊 Comparaison finale:")
        print(f"Grille générée shape: {generated.shape}")
        print(f"Grille attendue shape: {expected.shape}")
        
        if generated.shape == expected.shape:
            matches = np.sum(generated == expected)
            total = generated.size
            percentage = (matches / total) * 100
            
            print(f"Cellules correspondantes: {matches}/{total} ({percentage:.1f}%)")
            
            if matches != total:
                print("\n❌ Différences détectées:")
                diff_positions = np.where(generated != expected)
                for i in range(min(20, len(diff_positions[0]))):
                    row, col = diff_positions[0][i], diff_positions[1][i]
                    print(f"   Position [{row},{col}]: généré={generated[row,col]}, attendu={expected[row,col]}")
        else:
            print("❌ Les dimensions ne correspondent pas!")
    
    return executor

def test_multiply_behavior():
    """Test spécifique du comportement de MULTIPLY"""
    
    print("\n" + "="*60)
    print("🧪 Test spécifique de MULTIPLY...")
    
    executor = CommandExecutor()
    
    # Créer une grille simple pour test
    executor.grid = np.zeros((9, 9), dtype=int)
    executor.height = 9
    executor.width = 9
    
    # Ajouter les valeurs comme dans la vraie séquence
    executor.grid[0, 1] = 0  # EDIT 0 [0,1]
    executor.grid[1, 2] = 0  # EDIT 0 [1,2] 
    executor.grid[1, 1] = 3  # EDIT 3 [1,1]
    
    print("📋 Grille initiale:")
    print(executor.grid[:3, :3])
    
    # Simuler CUT [0,1 1,2]
    print("\n🔄 Simulation de CUT [0,1 1,2]...")
    
    # Copier la zone [0,1 1,2] dans le clipboard
    executor.clipboard = np.array([[executor.grid[0, 1], executor.grid[0, 2]],
                                   [executor.grid[1, 1], executor.grid[1, 2]]])
    executor.clipboard_mask = np.ones((2, 2), dtype=bool)
    
    # Effacer la zone
    executor.grid[0:2, 1:3] = 0
    
    print("Clipboard après CUT:")
    print(executor.clipboard)
    print("Clipboard mask:")
    print(executor.clipboard_mask)
    print("Grille après CUT:")
    print(executor.grid[:3, :3])
    
    # Test MULTIPLY 4 false
    print("\n🔄 Test de MULTIPLY 4 false...")
    
    from command_system.unified_command import UnifiedCommand
    multiply_cmd = UnifiedCommand.parse("MULTIPLY 4 false")
    
    success = executor._cmd_multiply(multiply_cmd)
    print(f"Succès MULTIPLY: {success}")
    
    if executor.clipboard is not None:
        print(f"Clipboard après MULTIPLY shape: {executor.clipboard.shape}")
        print("Clipboard après MULTIPLY:")
        print(executor.clipboard)
        print("Clipboard mask après MULTIPLY:")
        print(executor.clipboard_mask)
    
    # Test PASTE [0,1 1,2]
    print("\n🔄 Test de PASTE [0,1 1,2]...")
    
    paste_cmd = UnifiedCommand.parse("PASTE [0,1 1,2]")
    success = executor._cmd_paste(paste_cmd)
    print(f"Succès PASTE: {success}")
    
    if executor.error:
        print(f"Erreur PASTE: {executor.error}")
    
    print("Grille finale:")
    print(executor.grid)
    
    return executor

if __name__ == "__main__":
    # Test de la séquence complète
    executor1 = test_cut_multiply_paste_sequence()
    
    # Test spécifique de MULTIPLY
    executor2 = test_multiply_behavior()