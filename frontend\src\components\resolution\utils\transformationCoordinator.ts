// frontend/src/components/resolution/utils/transformationCoordinator.ts
import { Grid } from '../../../lib/grid';
import { useSelection } from '../hooks/useSelection';
import { useGridStateStore } from '../hooks/useGridStateStore'; // Remplacer useResolutionStore
import { useAutomationStore } from '../hooks/useAutomationStore';
import { SelectionSnapshot } from '../types/syncTypes';

// Types pour le coordinateur de transformation
export interface SelectionBounds {
  startRow: number;
  startCol: number;
  endRow: number;
  endCol: number;
}

export type TransformationType = 'rotateRight' | 'rotateLeft' | 'flipHorizontal' | 'flipVertical';

export interface TransformationContext {
  type: TransformationType;
  bounds: SelectionBounds;
  selectedCells: Set<string>;
  grid: Grid;
  timestamp: number;
}

export interface TransformationResult {
  success: boolean;
  newGrid: Grid;
  newSelectedCells: Set<string>;
  newBounds: SelectionBounds;
  automationCommand: string;
  error?: string;
}

export interface TransformationSnapshot {
  originalSelection: SelectionSnapshot;
  originalGrid: Grid;
  originalCommands: string[];
  timestamp: number;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface TransformationOptions {
  skipValidation?: boolean;
  preserveHistory?: boolean;
  generateCommand?: boolean;
}

/**
 * Coordinateur principal pour la synchronisation des transformations
 * Garantit que la sélection est mise à jour AVANT la transformation de la grille
 */
export class TransformationCoordinator {
  private static instance: TransformationCoordinator;

  public static getInstance(): TransformationCoordinator {
    if (!TransformationCoordinator.instance) {
      TransformationCoordinator.instance = new TransformationCoordinator();
    }
    return TransformationCoordinator.instance;
  }

  /**
   * Méthode principale de synchronisation des transformations
   */
  public async executeTransformation(
    type: TransformationType,
    bounds: SelectionBounds,
    options: TransformationOptions = {}
  ): Promise<TransformationResult> {
    const {
      skipValidation = false,
      preserveHistory = true,
      generateCommand = true
    } = options;

    let snapshot: TransformationSnapshot | null = null;

    try {
      console.log('[TransformationCoordinator] Starting synchronized transformation:', {
        type,
        bounds,
        options
      });

      // Phase 1: Capture de l'état initial
      const context = this.captureInitialState(type, bounds);

      // Créer un snapshot pour rollback potentiel
      snapshot = await this.createSnapshot(context);

      // Phase 2: Validation pré-transformation
      if (!skipValidation) {
        const validation = this.validatePreTransformState(context);
        if (!validation.isValid) {
          return {
            success: false,
            newGrid: context.grid,
            newSelectedCells: context.selectedCells,
            newBounds: bounds,
            automationCommand: '',
            error: `Validation failed: ${validation.errors.join(', ')}`
          };
        }
      }

      // Phase 3: Calcul des nouvelles coordonnées
      const newCoordinates = this.calculateNewCoordinates(context);
      const newBounds = this.calculateNewBounds(context);

      // Phase 4: Mise à jour de la sélection AVANT transformation
      this.updateSelectionBeforeTransform(newCoordinates, newBounds, type);

      // Phase 5: Application de la transformation à la grille
      const transformedGrid = this.applyGridTransformation(context, newCoordinates);

      // Phase 6: Validation de cohérence
      const result: TransformationResult = {
        success: true,
        newGrid: transformedGrid,
        newSelectedCells: newCoordinates,
        newBounds,
        automationCommand: this.generateAutomationCommand(type, `${bounds.startRow},${bounds.startCol} ${bounds.endRow},${bounds.endCol}`)
      };

      if (!skipValidation && !this.validateConsistency(result, context)) {
        throw new Error('Consistency validation failed after transformation');
      }

      // Phase 7: Sauvegarde synchronisée
      if (preserveHistory) {
        this.saveToSynchronizedHistory(result); // context retiré de l'appel
      }

      // Phase 8: Génération de la commande d'automation
      if (generateCommand) {
        this.addAutomationCommand(result.automationCommand);
      }

      console.log('[TransformationCoordinator] Transformation completed successfully:', {
        type,
        cellsTransformed: newCoordinates.size,
        newBounds
      });

      return result;

    } catch (error) {
      console.error('[TransformationCoordinator] Transformation failed:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        type,
        bounds,
        options,
        hasSnapshot: !!snapshot,
        timestamp: Date.now()
      });

      // Rollback en cas d'erreur avec logs détaillés
      if (snapshot) {
        console.log('[TransformationCoordinator] Initiating rollback due to error...');
        await this.rollbackTransformation(snapshot);
      } else {
        console.warn('[TransformationCoordinator] No snapshot available for rollback');
      }

      return {
        success: false,
        newGrid: snapshot?.originalGrid || new Grid(3, 3),
        newSelectedCells: new Set(),
        newBounds: bounds,
        automationCommand: '',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Capture l'état initial complet avant transformation
   */
  private captureInitialState(type: TransformationType, bounds: SelectionBounds): TransformationContext {
    const selectionStore = useSelection.getState();
    const resolutionStore = useGridStateStore.getState(); // Utiliser useGridStateStore

    const currentGrid = resolutionStore.getCurrentGrid();
    if (!currentGrid) {
      throw new Error('No current grid available for transformation');
    }

    return {
      type,
      bounds,
      selectedCells: new Set(selectionStore.selectedCells),
      grid: currentGrid.clone(),
      timestamp: Date.now()
    };
  }

  /**
   * Crée un snapshot pour rollback potentiel
   */
  private async createSnapshot(context: TransformationContext): Promise<TransformationSnapshot> {
    const selectionStore = useSelection.getState();
    const automationStore = useAutomationStore.getState();

    return {
      originalSelection: selectionStore.captureSnapshot(),
      originalGrid: context.grid.clone(),
      originalCommands: [...(automationStore.commands[automationStore.currentTestIndex] || [])],
      timestamp: context.timestamp
    };
  }

  /**
   * Calcule les nouvelles coordonnées après transformation
   */
  private calculateNewCoordinates(context: TransformationContext): Set<string> {
    const { type, bounds, selectedCells } = context;
    const { startRow, startCol, endRow, endCol } = bounds;
    const height = endRow - startRow + 1;
    const width = endCol - startCol + 1;

    const newSelectedCells = new Set<string>();

    // Convertir les cellules sélectionnées en coordonnées
    const selectedCoords = Array.from(selectedCells).map(cellKey => {
      const [row, col] = cellKey.split(',').map(Number);
      return [row, col];
    });

    // Appliquer la transformation à chaque cellule sélectionnée
    selectedCoords.forEach(([row, col]) => {
      // Vérifier si la cellule est dans les limites de la sélection
      if (row >= startRow && row <= endRow && col >= startCol && col <= endCol) {
        // Calculer les coordonnées relatives à l'origine de la sélection
        const relRow = row - startRow;
        const relCol = col - startCol;

        // Appliquer la transformation
        let newRelRow: number, newRelCol: number;

        switch (type) {
          case 'rotateRight':
            // Rotation à droite (90° sens horaire)
            newRelRow = relCol;
            newRelCol = height - 1 - relRow;
            break;
          case 'rotateLeft':
            // Rotation à gauche (90° sens antihoraire)
            newRelRow = width - 1 - relCol;
            newRelCol = relRow;
            break;
          case 'flipHorizontal':
            // Symétrie horizontale
            newRelRow = relRow;
            newRelCol = width - 1 - relCol;
            break;
          case 'flipVertical':
            // Symétrie verticale
            newRelRow = height - 1 - relRow;
            newRelCol = relCol;
            break;
          default:
            // Par défaut, ne pas transformer
            newRelRow = relRow;
            newRelCol = relCol;
        }

        // Calculer les nouvelles coordonnées absolues
        const newRow = startRow + newRelRow;
        const newCol = startCol + newRelCol;

        // Ajouter la cellule transformée à la nouvelle sélection
        newSelectedCells.add(`${newRow},${newCol}`);
      } else {
        // Si la cellule n'est pas dans les limites de la sélection, la conserver telle quelle
        newSelectedCells.add(`${row},${col}`);
      }
    });

    return newSelectedCells;
  }

  /**
   * Calcule les nouvelles limites après transformation
   */
  private calculateNewBounds(context: TransformationContext): SelectionBounds {
    const { type, bounds } = context;
    const { startRow, startCol, endRow, endCol } = bounds;
    const height = endRow - startRow + 1;
    const width = endCol - startCol + 1;

    if (type === 'rotateRight' || type === 'rotateLeft') {
      // Pour les rotations, les dimensions sont inversées
      return {
        startRow,
        startCol,
        endRow: startRow + width - 1,
        endCol: startCol + height - 1
      };
    } else {
      // Pour les symétries, les dimensions restent les mêmes
      return { ...bounds };
    }
  }

  /**
   * Met à jour la sélection AVANT la transformation de la grille
   */
  private updateSelectionBeforeTransform(
    newCoordinates: Set<string>,
    newBounds: SelectionBounds,
    type: TransformationType
  ): void {
    const selectionStore = useSelection.getState();

    // Désactiver temporairement l'enregistrement des commandes
    const wasRecording = selectionStore.isRecordingCommands;
    if (wasRecording) {
      selectionStore.disableCommandRecording();
    }

    try {
      // Utiliser la nouvelle méthode de mise à jour synchronisée
      if (selectionStore.updateSelectionBeforeTransform) {
        selectionStore.updateSelectionBeforeTransform(newCoordinates, newBounds, type);
      } else {
        // Fallback : mise à jour manuelle
        selectionStore.clearSelection();
        newCoordinates.forEach(coord => {
          const [row, col] = coord.split(',').map(Number);
          selectionStore.selectCell(row, col, 'add');
        });
      }

      console.log('[TransformationCoordinator] Selection updated before transform:', {
        newCells: newCoordinates.size,
        newBounds,
        type
      });
    } finally {
      // Restaurer l'état d'enregistrement
      if (wasRecording) {
        selectionStore.enableCommandRecording();
      }
    }
  }

  /**
   * Applique la transformation à la grille avec les nouvelles coordonnées
   * CORRECTION BUG 1: Ne transformer que les cellules sélectionnées
   */
  private applyGridTransformation(
    context: TransformationContext,
    newCoordinates: Set<string>
  ): Grid {
    const { type, bounds, grid, selectedCells } = context;
    const { startRow, startCol, endRow, endCol } = bounds;

    // Créer une nouvelle grille en clonant l'originale
    const newGrid = grid.clone();

    // Créer un mapping des valeurs des cellules sélectionnées UNIQUEMENT
    const selectedCellsData = new Map<string, number>();

    // Extraire SEULEMENT les valeurs des cellules sélectionnées
    selectedCells.forEach(cellKey => {
      const [row, col] = cellKey.split(',').map(Number);
      // Vérifier que la cellule est dans les bounds de transformation
      if (row >= startRow && row <= endRow && col >= startCol && col <= endCol) {
        const value = grid.getCell(row, col) ?? 0;
        selectedCellsData.set(cellKey, value);
      }
    });

    // Appliquer la transformation SEULEMENT aux cellules sélectionnées
    selectedCellsData.forEach((value, originalCellKey) => {
      const [originalRow, originalCol] = originalCellKey.split(',').map(Number);

      // Calculer les coordonnées relatives à l'origine de la sélection
      const relRow = originalRow - startRow;
      const relCol = originalCol - startCol;
      const height = endRow - startRow + 1;
      const width = endCol - startCol + 1;

      // Appliquer la transformation aux coordonnées relatives
      let newRelRow: number, newRelCol: number;

      switch (type) {
        case 'rotateRight':
          newRelRow = relCol;
          newRelCol = height - 1 - relRow;
          break;
        case 'rotateLeft':
          newRelRow = width - 1 - relCol;
          newRelCol = relRow;
          break;
        case 'flipHorizontal':
          newRelRow = relRow;
          newRelCol = width - 1 - relCol;
          break;
        case 'flipVertical':
          newRelRow = height - 1 - relRow;
          newRelCol = relCol;
          break;
        default:
          newRelRow = relRow;
          newRelCol = relCol;
      }

      // Calculer les nouvelles coordonnées absolues
      const newRow = startRow + newRelRow;
      const newCol = startCol + newRelCol;
      const newCellKey = `${newRow},${newCol}`;

      // Vérifier que la nouvelle position est dans les limites de la grille
      if (newRow >= 0 && newRow < newGrid.height && newCol >= 0 && newCol < newGrid.width) {
        // Vérifier que cette cellule fait partie des nouvelles coordonnées calculées
        if (newCoordinates.has(newCellKey)) {
          newGrid.setCell(newRow, newCol, value);
        }
      }
    });

    console.log('[TransformationCoordinator] Grid transformation applied to selected cells only:', {
      selectedCellsCount: selectedCells.size,
      transformedCellsCount: selectedCellsData.size,
      type
    });

    return newGrid;
  }

  // La méthode transformSubgrid n'est pas utilisée actuellement.
  // Elle pourrait être utile pour une approche alternative de transformation,
  // mais pour l'instant, elle est supprimée pour éviter un avertissement de code mort.
  /*
  private transformSubgrid(data: number[][], type: TransformationType): number[][] {
    const height = data.length;
    const width = data[0]?.length || 0;

    switch (type) {
      case 'rotateRight': {
        const rotated: number[][] = [];
        for (let col = 0; col < width; col++) {
          rotated[col] = [];
          for (let row = 0; row < height; row++) {
            rotated[col][height - 1 - row] = data[row][col];
          }
        }
        return rotated;
      }

      case 'rotateLeft': {
        // Rotation LEFT (antihoraire) : transposer puis inverser les lignes
        const rotated: number[][] = [];
        for (let row = 0; row < height; row++) {
          rotated[height - 1 - row] = [];
          for (let col = 0; col < width; col++) {
            rotated[height - 1 - row][col] = data[row][col];
          }
        }
        return rotated;
      }

      case 'flipHorizontal': {
        const flipped: number[][] = [];
        for (let row = 0; row < height; row++) {
          flipped[row] = [];
          for (let col = 0; col < width; col++) {
            flipped[row][width - 1 - col] = data[row][col];
          }
        }
        return flipped;
      }

      case 'flipVertical': {
        const flipped: number[][] = [];
        for (let row = 0; row < height; row++) {
          flipped[height - 1 - row] = [];
          for (let col = 0; col < width; col++) {
            flipped[height - 1 - row][col] = data[row][col];
          }
        }
        return flipped;
      }

      default:
        return data;
    }
  }
  */

  /**
   * Valide l'état avant transformation
   */
  private validatePreTransformState(context: TransformationContext): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Vérifier que la grille existe
    if (!context.grid) {
      errors.push('No grid available for transformation');
    }

    // Vérifier que les bounds sont valides
    const { bounds } = context;
    if (bounds.startRow < 0 || bounds.startCol < 0) {
      errors.push('Invalid bounds: negative coordinates');
    }

    if (bounds.endRow < bounds.startRow || bounds.endCol < bounds.startCol) {
      errors.push('Invalid bounds: end coordinates before start coordinates');
    }

    // Vérifier que la sélection n'est pas vide
    if (context.selectedCells.size === 0) {
      warnings.push('No cells selected for transformation');
    }

    // Vérifier que les bounds sont dans les limites de la grille
    if (context.grid) {
      if (bounds.endRow >= context.grid.height || bounds.endCol >= context.grid.width) {
        errors.push('Bounds exceed grid dimensions');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Valide la cohérence après transformation
   */
  private validateConsistency(result: TransformationResult, context: TransformationContext): boolean {
    try {
      // Vérifier que la nouvelle grille existe
      if (!result.newGrid) {
        console.error('[TransformationCoordinator] Validation failed: no new grid');
        return false;
      }

      // Vérifier que les nouvelles coordonnées ne sont pas vides
      if (result.newSelectedCells.size === 0 && context.selectedCells.size > 0) {
        console.error('[TransformationCoordinator] Validation failed: lost selection');
        return false;
      }

      // Vérifier que les nouvelles bounds sont cohérentes
      const { newBounds } = result;
      if (newBounds.endRow < newBounds.startRow || newBounds.endCol < newBounds.startCol) {
        console.error('[TransformationCoordinator] Validation failed: invalid new bounds');
        return false;
      }

      // Vérifier la cohérence avec le store de sélection
      const selectionStore = useSelection.getState();
      if (selectionStore.validateTransformationConsistency) {
        return selectionStore.validateTransformationConsistency(result.newSelectedCells, newBounds);
      }

      return true;
    } catch (error) {
      console.error('[TransformationCoordinator] Validation error:', error);
      return false;
    }
  }

  /**
   * Sauvegarde synchronisée dans l'historique
   */
  // private saveToSynchronizedHistory(result: TransformationResult, context: TransformationContext): void { // context n'est plus utilisé
  private saveToSynchronizedHistory(result: TransformationResult): void { // context retiré des paramètres
    try {
      const resolutionStore = useGridStateStore.getState(); // Utiliser useGridStateStore
      // const selectionStore = useSelection.getState(); // selectionStore n'est plus utilisé

      // Mettre à jour la grille dans le store
      resolutionStore.replaceCurrentGrid(result.newGrid); // Utiliser replaceCurrentGrid

      // Capturer un nouveau snapshot de sélection
      // const selectionSnapshot = selectionStore.captureSnapshot(); // Potentiellement utile pour useUnifiedHistory

      // La logique de sauvegarde d'historique spécifique à la transformation (saveTransformationToHistory, etc.)
      // a été déplacée ou est gérée par replaceCurrentGrid (pour la grille) et potentiellement
      // useUnifiedHistory ou useSelectionHistoryStore pour la sélection.
      // Pour l'instant, on se fie à replaceCurrentGrid pour l'historique de la grille.
      // Les lignes suivantes sont commentées car elles ne s'appliquent plus directement à useGridStateStore.
      /*
      if (resolutionStore.saveTransformationToHistory) { // N'existe plus sur useGridStateStore
        resolutionStore.saveTransformationToHistory(
          result.newGrid,
          selectionSnapshot,
          {
            type: context.type,
            bounds: context.bounds,
            timestamp: context.timestamp
          }
        );
      } else {
        // Fallback : sauvegarde standard avec synchronisation complète
        if (resolutionStore.saveToHistoryWithFullSync) { // N'existe plus sur useGridStateStore
          resolutionStore.saveToHistoryWithFullSync();
        } else {
          resolutionStore.saveToHistory(); // N'existe plus sur useGridStateStore
        }
      }
      */

      console.log('[TransformationCoordinator] State (grid) saved to history via replaceCurrentGrid');
    } catch (error) {
      console.error('[TransformationCoordinator] Failed to save to history:', error);
      // Propager l'erreur pour que la transformation échoue
      throw error;
    }
  }

  /**
   * Génère la commande d'automation pour la transformation
   */
  private generateAutomationCommand(type: TransformationType, boundsStr: string): string {
    switch (type) {
      case 'rotateRight':
        return `ROTATE RIGHT ${boundsStr}`; // Format attendu par les tests
      case 'rotateLeft':
        return `ROTATE LEFT ${boundsStr}`; // Format attendu par les tests
      case 'flipHorizontal':
        return `FLIP HORIZONTAL ${boundsStr}`; // Format attendu par les tests
      case 'flipVertical':
        return `FLIP VERTICAL ${boundsStr}`; // Format attendu par les tests
      default:
        return `TRANSFORM ${type} ([${boundsStr}])`;
    }
  }

  /**
   * Ajoute la commande d'automation
   */
  private addAutomationCommand(command: string): void {
    try {
      const automationStore = useAutomationStore.getState();

      // Pour les commandes avec deux mots, traiter différemment
      if (command.startsWith('FLIP HORIZONTAL') || command.startsWith('FLIP VERTICAL') ||
        command.startsWith('ROTATE LEFT') || command.startsWith('ROTATE RIGHT')) {
        const parts = command.split(' ');
        const type = parts[0] + '_' + parts[1]; // FLIP_HORIZONTAL, FLIP_VERTICAL, ROTATE_LEFT, ROTATE_RIGHT
        const params = parts.slice(2).join(' ');
        automationStore.addCommand(type, params);
      } else {
        // Traitement standard pour autres commandes
        const firstSpaceIndex = command.indexOf(' ');
        if (firstSpaceIndex === -1) {
          automationStore.addCommand(command);
        } else {
          const type = command.substring(0, firstSpaceIndex);
          const params = command.substring(firstSpaceIndex + 1);
          automationStore.addCommand(type, params);
        }
      }

      console.log('[TransformationCoordinator] Automation command added:', command);
    } catch (error) {
      console.error('[TransformationCoordinator] Failed to add automation command:', error);
    }
  }

  /**
   * Rollback en cas d'erreur
   * CORRECTION BUG 2: Amélioration de la gestion d'erreurs et du rollback
   */
  private async rollbackTransformation(snapshot: TransformationSnapshot): Promise<void> {
    const rollbackSteps: string[] = [];
    const rollbackErrors: string[] = [];

    try {
      console.log('[TransformationCoordinator] Starting rollback transformation...', {
        timestamp: snapshot.timestamp,
        hasSelection: !!snapshot.originalSelection,
        hasGrid: !!snapshot.originalGrid,
        hasCommands: snapshot.originalCommands.length > 0
      });

      const selectionStore = useSelection.getState();
      const resolutionStore = useGridStateStore.getState(); // Utiliser useGridStateStore
      const automationStore = useAutomationStore.getState();

      // Étape 1: Restaurer la sélection
      try {
        if (selectionStore.rollbackSelection) {
          selectionStore.rollbackSelection(snapshot.originalSelection);
        } else {
          selectionStore.restoreSnapshot(snapshot.originalSelection);
        }
        rollbackSteps.push('selection');
        console.log('[TransformationCoordinator] Selection rollback completed');
      } catch (selectionError) {
        rollbackErrors.push(`Selection rollback failed: ${selectionError}`);
        console.error('[TransformationCoordinator] Selection rollback failed:', selectionError);

        // Fallback: vider la sélection
        try {
          selectionStore.clearSelection();
          rollbackSteps.push('selection-cleared');
        } catch (clearError) {
          rollbackErrors.push(`Selection clear failed: ${clearError}`);
        }
      }

      // Étape 2: Restaurer la grille
      try {
        if (snapshot.originalGrid) {
          resolutionStore.replaceCurrentGrid(snapshot.originalGrid); // Utiliser replaceCurrentGrid
          rollbackSteps.push('grid');
          console.log('[TransformationCoordinator] Grid rollback completed');
        }
      } catch (gridError) {
        rollbackErrors.push(`Grid rollback failed: ${gridError}`);
        console.error('[TransformationCoordinator] Grid rollback failed:', gridError);
      }

      // Étape 3: Restaurer les commandes d'automation
      try {
        const currentTestIndex = automationStore.currentTestIndex;
        if (snapshot.originalCommands && Array.isArray(snapshot.originalCommands)) {
          // Utiliser setCommands pour une restauration propre
          if (automationStore.setCommands) {
            automationStore.setCommands([...snapshot.originalCommands]);
          } else {
            // Fallback: restauration directe (non recommandée mais nécessaire)
            if (automationStore.commands[currentTestIndex]) {
              automationStore.commands[currentTestIndex].length = 0;
              automationStore.commands[currentTestIndex].push(...snapshot.originalCommands);
            }
          }
          rollbackSteps.push('commands');
          console.log('[TransformationCoordinator] Commands rollback completed:', {
            restoredCount: snapshot.originalCommands.length
          });
        }
      } catch (commandsError) {
        rollbackErrors.push(`Commands rollback failed: ${commandsError}`);
        console.error('[TransformationCoordinator] Commands rollback failed:', commandsError);
      }

      // Étape 4: Validation post-rollback
      try {
        const currentGrid = resolutionStore.getCurrentGrid();
        const currentSelection = selectionStore.selectedCells;

        console.log('[TransformationCoordinator] Post-rollback validation:', {
          gridRestored: !!currentGrid,
          selectionSize: currentSelection.size,
          rollbackSteps,
          errors: rollbackErrors
        });
      } catch (validationError) {
        rollbackErrors.push(`Post-rollback validation failed: ${validationError}`);
      }

      if (rollbackErrors.length === 0) {
        console.log('[TransformationCoordinator] Rollback completed successfully:', rollbackSteps);
      } else {
        console.warn('[TransformationCoordinator] Rollback completed with errors:', {
          completedSteps: rollbackSteps,
          errors: rollbackErrors
        });
      }

    } catch (error) {
      console.error('[TransformationCoordinator] Critical rollback failure:', error);
      rollbackErrors.push(`Critical failure: ${error}`);

      // Rollback d'urgence: nettoyer l'état pour éviter la corruption
      try {
        const selectionStore = useSelection.getState();
        selectionStore.clearSelection();
        console.log('[TransformationCoordinator] Emergency selection clear completed');
      } catch (emergencyError) {
        console.error('[TransformationCoordinator] Emergency rollback failed:', emergencyError);
      }
    }
  }
}

// Instance singleton exportée
export const transformationCoordinator = TransformationCoordinator.getInstance();