from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import Allow<PERSON>ny, IsAdminUser
from rest_framework.response import Response
from .decorators import cache_response, invalidate_cache
from .models.audit import AuditLog
from myapp.utils.api_responses import success_response, error_response, standardize_response

from ai.models import AIModel, ARCTask, AutomationScenario, NotebookTemplate, UserNotebook
from ai.services import ModelService, PromptService
from ai.task_service import TaskService
from ai.jupyter_service import JupyterService
from ai.stats_service import StatsService
from .serializers import (
    AIModelSerializer, PromptExecutionRequestSerializer, PromptExecutionSerializer,
    ARCTaskSerializer, ARCTaskDetailSerializer, AutomationScenarioSerializer, CreateScenarioSerializer,
    NotebookTemplateSerializer, UserNotebookSerializer, CreateNotebookSerializer, JupyterServerInfoSerializer,
    TestCommandsSerializer, AuditLogSerializer, CommandSerializer, CommandCategorySerializer
)

@api_view(['GET'])
@cache_response(timeout=3600, key_prefix='models_list')  # Cache pour 1 heure
@standardize_response
def models_list(request):
    """
    Liste des modèles d'IA disponibles.

    Retourne la liste de tous les modèles d'IA disponibles dans le système.
    Chaque modèle contient un identifiant, un nom, une description et des métadonnées.
    """
    model_service = ModelService()
    models = model_service.get_models()
    serializer = AIModelSerializer(models, many=True)
    return serializer.data


@api_view(['POST'])
@standardize_response
def execute_prompt(request):
    """
    Exécute un prompt avec un modèle d'IA.

    Envoie un prompt à un modèle d'IA spécifié et retourne la réponse générée.

    Paramètres:
    - prompt: Texte du prompt à envoyer au modèle
    - taskId: Identifiant de la tâche ARC associée
    - modelId: Identifiant du modèle d'IA à utiliser

    Retourne:
    - La réponse générée par le modèle
    """
    serializer = PromptExecutionRequestSerializer(data=request.data)

    if not serializer.is_valid():
        return error_response(
            message='Données invalides',
            code='INVALID_DATA',
            details=serializer.errors,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    prompt_service = PromptService()

    try:
        result = prompt_service.execute_prompt(
            prompt=serializer.validated_data['prompt'],
            task_id=serializer.validated_data['taskId'],
            model_id=serializer.validated_data['modelId']
        )

        return success_response(result)
    except ValueError as e:
        return error_response(
            message=str(e),
            code='INVALID_INPUT',
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        return error_response(
            message=str(e),
            code='EXECUTION_ERROR',
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@cache_response(timeout=3600, key_prefix='tasks_list', params=['subset'])  # Cache pour 1 heure
@standardize_response
def tasks_list(request):
    """
    Liste des tâches ARC disponibles.

    Retourne la liste de toutes les tâches ARC disponibles dans le système.
    Les tâches peuvent être filtrées par sous-ensemble (training ou evaluation).

    Paramètres:
    - subset: (Optionnel) Sous-ensemble de tâches à récupérer (défaut: training)

    Retourne:
    - Liste des tâches ARC avec leurs métadonnées
    """
    subset = request.query_params.get('subset', 'training')

    task_service = TaskService()
    tasks = task_service.list_tasks(subset)

    serializer = ARCTaskSerializer(tasks, many=True)
    return serializer.data


@api_view(['GET'])
@cache_response(timeout=3600, key_prefix='task_detail')  # Cache pour 1 heure
@standardize_response
def task_detail(request, task_id):
    """
    Détails d'une tâche ARC.

    Retourne les détails d'une tâche ARC spécifique, y compris les exemples d'entrée/sortie.

    Paramètres:
    - task_id: Identifiant de la tâche ARC

    Retourne:
    - Détails de la tâche ARC avec ses métadonnées et exemples
    """
    task_service = TaskService()
    task_data = task_service.get_task(task_id)

    if not task_data:
        return error_response(
            message='Tâche non trouvée',
            code='TASK_NOT_FOUND',
            status_code=status.HTTP_404_NOT_FOUND
        )

    serializer = ARCTaskDetailSerializer(task_data)
    return success_response(serializer.data)


@api_view(['GET'])
@cache_response(timeout=300, key_prefix='task_scenarios')  # Cache pour 5 minutes
@standardize_response
def task_scenarios(request, task_id):
    """
    Liste des scénarios d'automatisation pour une tâche.

    Retourne la liste de tous les scénarios d'automatisation associés à une tâche ARC spécifique.

    Paramètres:
    - task_id: Identifiant de la tâche ARC

    Retourne:
    - Liste des scénarios d'automatisation pour la tâche
    """
    task_service = TaskService()
    scenarios = task_service.get_scenarios(task_id)

    serializer = AutomationScenarioSerializer(scenarios, many=True)
    return success_response(serializer.data)


@api_view(['POST'])
@invalidate_cache(key_prefix='task_scenarios')  # Invalider le cache des scénarios
@standardize_response
def create_scenario(request):
    """
    Crée un nouveau scénario d'automatisation.

    Permet de créer un nouveau scénario d'automatisation pour une tâche ARC.

    Paramètres:
    - task_id: Identifiant de la tâche ARC
    - name: Nom du scénario
    - commands: Chaîne de caractères contenant les commandes d'automatisation
    - description: (Optionnel) Description du scénario
    - created_by_ai: (Optionnel) Indique si le scénario a été créé par l'IA

    Retourne:
    - Le scénario créé avec ses métadonnées
    """
    serializer = CreateScenarioSerializer(data=request.data)

    if not serializer.is_valid():
        return error_response(
            message='Données invalides',
            code='INVALID_DATA',
            details=serializer.errors,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    task_service = TaskService()

    # Créer le scénario avec validation
    scenario, validation_result = task_service.create_scenario(
        task_id=serializer.validated_data['task_id'],
        name=serializer.validated_data['name'],
        commands=serializer.validated_data['commands'],
        description=serializer.validated_data.get('description', ''),
        created_by_ai=serializer.validated_data.get('created_by_ai', False),
        validate=True
    )

    if not scenario:
        return error_response(
            message='Tâche non trouvée',
            code='TASK_NOT_FOUND',
            status_code=status.HTTP_404_NOT_FOUND
        )

    # Préparer la réponse
    result_serializer = AutomationScenarioSerializer(scenario)
    response_data = result_serializer.data

    # Ajouter les résultats de validation si disponibles
    if validation_result:
        response_data['validation'] = {
            'valid': validation_result['valid'],
            'errors': validation_result['errors'],
            'execution_result': validation_result.get('execution_result'),
            'validation_result': validation_result.get('validation_result')
        }

    return success_response(response_data, status_code=status.HTTP_201_CREATED)


@api_view(['POST'])
@invalidate_cache(key_prefix='task_scenarios')  # Invalider le cache des scénarios
@standardize_response
def validate_scenario(request, scenario_id):
    """
    Valide un scénario d'automatisation.

    Valide un scénario d'automatisation existant en exécutant les commandes
    et en vérifiant qu'elles produisent le résultat attendu.

    Paramètres:
    - scenario_id: Identifiant du scénario à valider

    Retourne:
    - Résultat de la validation avec les erreurs éventuelles
    """
    task_service = TaskService()

    scenario, validation_result = task_service.validate_scenario(scenario_id)

    if not scenario:
        return error_response(
            message='Scénario non trouvé',
            code='SCENARIO_NOT_FOUND',
            status_code=status.HTTP_404_NOT_FOUND
        )

    # Préparer la réponse
    response_data = {
        'id': str(scenario.id),
        'status': scenario.status
    }

    # Ajouter les résultats de validation si disponibles
    if validation_result:
        response_data['validation'] = {
            'valid': validation_result['valid'],
            'errors': validation_result['errors'],
            'execution_result': validation_result.get('execution_result'),
            'validation_result': validation_result.get('validation_result')
        }

    return success_response(response_data)


@api_view(['POST'])
@standardize_response
def test_commands(request):
    """
    Teste des commandes d'automatisation sans créer de scénario.

    Permet de valider et d'exécuter des commandes d'automatisation pour une tâche ARC
    sans créer de scénario persistant. Utile pour tester des commandes avant de les sauvegarder.

    Paramètres:
    - task_id: Identifiant de la tâche ARC
    - commands: Chaîne de caractères contenant les commandes à tester (séparées par des sauts de ligne)

    Retourne:
    - valid: Booléen indiquant si les commandes sont valides
    - valid_commands: Liste des commandes valides
    - errors: Liste des erreurs rencontrées
    - execution_result: Résultat de l'exécution des commandes
    - validation_result: Résultat de la validation par rapport à la sortie attendue
    """
    serializer = TestCommandsSerializer(data=request.data)

    if not serializer.is_valid():
        return error_response(
            message='Données invalides',
            code='INVALID_DATA',
            details=serializer.errors,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    task_service = TaskService()

    try:
        task = ARCTask.objects.get(task_id=serializer.validated_data['task_id'])
    except ARCTask.DoesNotExist:
        return error_response(
            message='Tâche non trouvée',
            code='TASK_NOT_FOUND',
            status_code=status.HTTP_404_NOT_FOUND
        )

    # Convertir les commandes en liste
    commands = serializer.validated_data['commands']
    commands_list = commands.split('\n') if isinstance(commands, str) else commands

    # Récupérer l'index de test (par défaut 0)
    test_index = serializer.validated_data.get('test_index', 0)

    # Valider les commandes
    validation_result = task_service.validator.validate_commands(commands_list, task, test_index)

    return success_response(validation_result)


# Vues pour les journaux d'audit
@api_view(['GET'])
@permission_classes([IsAdminUser])
@standardize_response
def audit_logs_list(request):
    """
    Liste des journaux d'audit.

    Retourne la liste des journaux d'audit, avec possibilité de filtrage.
    Cette API est réservée aux administrateurs.

    Paramètres:
    - action_type: (Optionnel) Type d'action (create, update, delete, etc.)
    - resource_type: (Optionnel) Type de ressource (task, scenario, prompt, etc.)
    - resource_id: (Optionnel) Identifiant de la ressource
    - user_id: (Optionnel) Identifiant de l'utilisateur
    - start_date: (Optionnel) Date de début (format: YYYY-MM-DD)
    - end_date: (Optionnel) Date de fin (format: YYYY-MM-DD)
    - limit: (Optionnel) Nombre maximum de résultats à retourner

    Retourne:
    - Liste des journaux d'audit correspondant aux critères de filtrage
    """
    # Récupérer les paramètres de filtrage
    action_type = request.query_params.get('action_type')
    resource_type = request.query_params.get('resource_type')
    resource_id = request.query_params.get('resource_id')
    user_id = request.query_params.get('user_id')
    start_date = request.query_params.get('start_date')
    end_date = request.query_params.get('end_date')
    limit = request.query_params.get('limit', 100)

    # Filtrer les journaux d'audit
    logs = AuditLog.objects.all()

    if action_type:
        logs = logs.filter(action_type=action_type)

    if resource_type:
        logs = logs.filter(resource_type=resource_type)

    if resource_id:
        logs = logs.filter(resource_id=resource_id)

    if user_id:
        logs = logs.filter(user_id=user_id)

    if start_date:
        logs = logs.filter(created_at__gte=start_date)

    if end_date:
        logs = logs.filter(created_at__lte=end_date)

    # Limiter le nombre de résultats
    logs = logs[:int(limit)]

    # Sérialiser les résultats
    serializer = AuditLogSerializer(logs, many=True)

    return success_response(serializer.data)


# Vues pour les statistiques
@api_view(['GET'])
@permission_classes([IsAdminUser])
@standardize_response
def model_usage_stats(request):
    """
    Statistiques d'utilisation des modèles d'IA.

    Retourne les statistiques d'utilisation des modèles d'IA.
    Cette API est réservée aux administrateurs.

    Paramètres:
    - days: (Optionnel) Nombre de jours à considérer (défaut: 30)

    Retourne:
    - Statistiques d'utilisation des modèles d'IA
    """
    days = int(request.query_params.get('days', 30))

    stats_service = StatsService()
    stats = stats_service.get_model_usage_stats(days=days)

    return stats


@api_view(['GET'])
@permission_classes([IsAdminUser])
@standardize_response
def task_stats(request):
    """
    Statistiques par tâche.

    Retourne les statistiques d'utilisation par tâche.
    Cette API est réservée aux administrateurs.

    Paramètres:
    - days: (Optionnel) Nombre de jours à considérer (défaut: 30)

    Retourne:
    - Statistiques par tâche
    """
    days = int(request.query_params.get('days', 30))

    stats_service = StatsService()
    stats = stats_service.get_task_stats(days=days)

    return stats


@api_view(['GET'])
@permission_classes([IsAdminUser])
@standardize_response
def time_series_stats(request):
    """
    Statistiques sous forme de série temporelle.

    Retourne les statistiques d'utilisation sous forme de série temporelle.
    Cette API est réservée aux administrateurs.

    Paramètres:
    - days: (Optionnel) Nombre de jours à considérer (défaut: 30)
    - interval: (Optionnel) Intervalle de temps ('day', 'week', 'month') (défaut: 'day')

    Retourne:
    - Statistiques sous forme de série temporelle
    """
    days = int(request.query_params.get('days', 30))
    interval = request.query_params.get('interval', 'day')

    stats_service = StatsService()
    stats = stats_service.get_time_series_stats(days=days, interval=interval)

    return stats


# Note: Cette vue a été déplacée vers views_statistics.py
# @api_view(['GET'])
# @permission_classes([IsAuthenticated])
# @standardize_response
# def user_statistics(request):
#     """
#     Statistiques de l'utilisateur actuel.
#
#     Retourne les statistiques de l'utilisateur connecté concernant
#     la résolution des tâches ARC.
#
#     Paramètres:
#     - subset: (Optionnel) Sous-ensemble à considérer (défaut: all)
#
#     Retourne:
#     - Statistiques détaillées de l'utilisateur
#     """
#     subset = request.query_params.get('subset', 'all')
#
#     stats_service = StatsService()
#     stats = stats_service.get_user_statistics(request.user, subset)
#
#     return stats


# Note: Cette vue a été déplacée vers views_statistics.py
# @api_view(['GET'])
# @permission_classes([IsAuthenticated])
# @standardize_response
# def average_statistics(request):
#     """
#     Statistiques moyennes de tous les utilisateurs.
#
#     Retourne les statistiques moyennes de tous les utilisateurs
#     pour permettre la comparaison.
#
#     Retourne:
#     - Statistiques moyennes
#     """
#     stats_service = StatsService()
#     stats = stats_service.get_average_statistics()


@api_view(['GET'])
@cache_response(timeout=3600, key_prefix='commands_list')  # Cache pour 1 heure
@standardize_response
def commands_list(request):
    """
    Liste des commandes d'automatisation disponibles.

    Retourne la liste de toutes les commandes d'automatisation disponibles dans le système.
    Chaque commande contient un nom, une description, une syntaxe, un exemple et des métadonnées.

    Paramètres:
    - active_only: (Optionnel) Si True, ne retourne que les commandes actives (défaut: True)
    - frontend_only: (Optionnel) Si True, ne retourne que les commandes exécutées côté client (défaut: False)
    - backend_only: (Optionnel) Si True, ne retourne que les commandes exécutées côté serveur (défaut: False)

    Retourne:
    - Liste des commandes d'automatisation avec leurs détails
    """
    from myapp.models import Command

    # Récupérer les paramètres de filtrage
    active_only = request.query_params.get('active_only', 'true').lower() == 'true'
    frontend_only = request.query_params.get('frontend_only', 'false').lower() == 'true'
    backend_only = request.query_params.get('backend_only', 'false').lower() == 'true'

    # Filtrer les commandes
    commands = Command.objects.all()

    if active_only:
        commands = commands.filter(is_active=True)

    if frontend_only:
        commands = commands.filter(is_frontend_only=True)

    if backend_only:
        commands = commands.filter(is_backend_only=True)

    # Ordonner les commandes
    commands = commands.order_by('category__order', 'order', 'name')

    # Sérialiser les résultats
    serializer = CommandSerializer(commands, many=True)

    return success_response(serializer.data)


@api_view(['GET'])
@cache_response(timeout=3600, key_prefix='command_categories')  # Cache pour 1 heure
@standardize_response
def command_categories(request):
    """
    Liste des catégories de commandes d'automatisation.

    Retourne la liste de toutes les catégories de commandes d'automatisation disponibles dans le système.

    Retourne:
    - Liste des catégories de commandes avec leurs détails
    """
    from myapp.models import CommandCategory

    # Récupérer toutes les catégories
    categories = CommandCategory.objects.all().order_by('order', 'name')

    # Sérialiser les résultats
    serializer = CommandCategorySerializer(categories, many=True)

    return success_response(serializer.data)


@api_view(['HEAD', 'GET'])
def health_check(request):
    """
    Endpoint simple pour vérifier si le backend est accessible.
    """
    return Response(status=status.HTTP_200_OK)
#
#     return stats


# Note: Cette vue a été déplacée vers views_statistics.py
# @api_view(['GET'])
# @permission_classes([IsAuthenticated])
# @standardize_response
# def statistics_health(request):
#     """
#     Vérifie la disponibilité de l'API de statistiques.
#
#     Retourne un statut indiquant si l'API de statistiques est disponible.
#
#     Retourne:
#     - Statut de l'API
#     """
#     return {
#         'status': 'ok',
#         'message': 'L\'API de statistiques est disponible',
#         'version': '1.0.0'
#     }


# Vues pour les API Jupyter

@api_view(['GET'])
@standardize_response
def jupyter_server_info(request):
    """Récupère les informations du serveur Jupyter"""
    jupyter_service = JupyterService()

    server_info = jupyter_service.get_server_info()

    if not server_info:
        return error_response(
            message='Serveur Jupyter non démarré',
            code='JUPYTER_NOT_RUNNING',
            status_code=status.HTTP_404_NOT_FOUND
        )

    serializer = JupyterServerInfoSerializer(server_info)
    return success_response(serializer.data)


@api_view(['POST'])
@standardize_response
def start_jupyter_server(request):
    """Démarre le serveur Jupyter"""
    jupyter_service = JupyterService()

    server_info = jupyter_service.start_server()

    if not server_info:
        return error_response(
            message='Impossible de démarrer le serveur Jupyter',
            code='JUPYTER_START_ERROR',
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    serializer = JupyterServerInfoSerializer(server_info)
    return success_response(serializer.data)


@api_view(['POST'])
@standardize_response
def stop_jupyter_server(request):
    """Arrête le serveur Jupyter"""
    jupyter_service = JupyterService()

    success = jupyter_service.stop_server()

    if not success:
        return error_response(
            message='Serveur Jupyter non démarré',
            code='JUPYTER_NOT_RUNNING',
            status_code=status.HTTP_404_NOT_FOUND
        )

    return success_response({'status': 'stopped'})


@api_view(['GET'])
@standardize_response
def list_notebook_templates(request):
    """Liste les templates de notebooks disponibles"""
    templates = NotebookTemplate.objects.all()

    serializer = NotebookTemplateSerializer(templates, many=True)
    return success_response(serializer.data)


@api_view(['GET'])
@standardize_response
def list_user_notebooks(request, task_id=None):
    """Liste les notebooks utilisateur"""
    if task_id:
        try:
            task = ARCTask.objects.get(task_id=task_id)
            notebooks = UserNotebook.objects.filter(task=task)
        except ARCTask.DoesNotExist:
            return error_response(
                message='Tâche non trouvée',
                code='TASK_NOT_FOUND',
                status_code=status.HTTP_404_NOT_FOUND
            )
    else:
        notebooks = UserNotebook.objects.all()

    serializer = UserNotebookSerializer(notebooks, many=True)
    return success_response(serializer.data)


@api_view(['POST'])
@standardize_response
def create_notebook(request):
    """Crée un nouveau notebook à partir d'un template"""
    serializer = CreateNotebookSerializer(data=request.data)

    if not serializer.is_valid():
        return error_response(
            message='Données invalides',
            code='INVALID_DATA',
            details=serializer.errors,
            status_code=status.HTTP_400_BAD_REQUEST
        )

    try:
        template = NotebookTemplate.objects.get(id=serializer.validated_data['template_id'])
        task = ARCTask.objects.get(task_id=serializer.validated_data['task_id'])
    except NotebookTemplate.DoesNotExist:
        return error_response(
            message='Template non trouvé',
            code='TEMPLATE_NOT_FOUND',
            status_code=status.HTTP_404_NOT_FOUND
        )
    except ARCTask.DoesNotExist:
        return error_response(
            message='Tâche non trouvée',
            code='TASK_NOT_FOUND',
            status_code=status.HTTP_404_NOT_FOUND
        )

    # Créer le notebook
    jupyter_service = JupyterService()

    # Vérifier si le serveur est démarré
    if not jupyter_service.is_server_running():
        jupyter_service.start_server()

    # Générer un nom pour le notebook si non fourni
    name = serializer.validated_data.get('name')
    if not name:
        name = f"{template.name}_{task.task_id}"

    # Créer le notebook à partir du template
    notebook_info = jupyter_service.create_notebook_from_template(
        template_name=template.name,
        notebook_name=name
    )

    if not notebook_info:
        return error_response(
            message='Impossible de créer le notebook',
            code='NOTEBOOK_CREATION_ERROR',
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    # Enregistrer le notebook dans la base de données
    notebook = UserNotebook.objects.create(
        name=name,
        file_path=notebook_info['path'],
        template=template,
        task=task,
        metadata={
            'url': notebook_info['url'],
            'created_from_template': template.name
        }
    )

    serializer = UserNotebookSerializer(notebook)
    return success_response(serializer.data, status_code=status.HTTP_201_CREATED)