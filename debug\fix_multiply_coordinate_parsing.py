#!/usr/bin/env python3
"""
Script pour corriger l'implémentation de MULTIPLY afin qu'elle utilise 
le système de parsing générique des coordonnées du backend
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

def generate_fixed_multiply_implementation():
    """Génère l'implémentation corrigée de _cmd_multiply"""
    
    fixed_implementation = '''
    def _cmd_multiply(self, cmd: UnifiedCommand) -> bool:
        """
        Multiplie le contenu du presse-papier par un facteur
        Format: MULTIPLY factor [replace_content] ([coordonnées])
        Utilise le système de parsing générique des coordonnées
        """
        if not cmd.parameters or len(cmd.parameters) < 1:
            self.error = "MULTIPLY nécessite un facteur de multiplication"
            return False

        if self.clipboard is None:
            self.error = "MULTIPLY nécessite une commande COPY préalable"
            return False

        try:
            factor = int(cmd.parameters[0])
            if factor < 2:
                self.error = "Le facteur de multiplication doit être >= 2"
                return False
        except ValueError:
            self.error = "Le facteur de multiplication doit être un entier"
            return False

        # Vérifier si le paramètre 'replace_content' est présent
        replace_content = False  # Par défaut false selon la documentation
        if len(cmd.parameters) >= 2:
            try:
                replace_param = str(cmd.parameters[1]).lower()
                if replace_param in ['true', '1', 'yes']:
                    replace_content = True
                elif replace_param in ['false', '0', 'no']:
                    replace_content = False
                else:
                    self.error = f"Paramètre replace_content invalide: {replace_param}. Utilisez true/false"
                    return False
            except (ValueError, AttributeError):
                self.error = "Le paramètre replace_content doit être true ou false"
                return False

        # Obtenir les dimensions actuelles du clipboard
        clip_height, clip_width = self.clipboard.shape

        # Calculer les nouvelles dimensions
        new_height = clip_height * factor
        new_width = clip_width * factor

        # Créer le nouveau clipboard multiplié
        new_clipboard = np.zeros((new_height, new_width), dtype=int)
        new_clipboard_mask = np.zeros((new_height, new_width), dtype=bool)

        # NOUVEAU: Utiliser le système de parsing générique des coordonnées
        # Si des coordonnées sont spécifiées, elles définissent quelles cellules du clipboard multiplier
        if hasattr(cmd, 'raw_command') and cmd.raw_command and ('[' in cmd.raw_command):
            coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            
            if coordinate_blocks:
                # Créer un masque pour les cellules à multiplier
                multiply_mask = np.zeros((clip_height, clip_width), dtype=bool)
                
                # Fonction pour marquer les cellules à multiplier
                def mark_multiply_cells(x1, y1, x2, y2):
                    # Vérifier les limites du clipboard
                    if (0 <= x1 < clip_height and 0 <= x2 < clip_height and
                        0 <= y1 < clip_width and 0 <= y2 < clip_width):
                        multiply_mask[x1:x2+1, y1:y2+1] = True
                
                # Traiter les blocs de coordonnées
                self._process_coordinate_blocks(coordinate_blocks, mark_multiply_cells)
                
                # Utiliser le masque créé au lieu du clipboard_mask original
                effective_mask = multiply_mask
            else:
                # Pas de coordonnées spécifiées, utiliser le masque original
                effective_mask = self.clipboard_mask if self.clipboard_mask is not None else np.ones((clip_height, clip_width), dtype=bool)
        else:
            # Pas de coordonnées spécifiées, utiliser le masque original
            effective_mask = self.clipboard_mask if self.clipboard_mask is not None else np.ones((clip_height, clip_width), dtype=bool)

        if replace_content:
            # Mode replace_content=True : remplacer le contenu dans chaque rectangle correspondant aux cellules sélectionnées
            # Chaque cellule sélectionnée devient un bloc factor x factor avec le motif entier
            for i in range(clip_height):
                for j in range(clip_width):
                    if effective_mask[i, j]:
                        # Cette cellule était sélectionnée, la remplacer par le motif entier factor x factor fois
                        for fi in range(factor):
                            for fj in range(factor):
                                new_row = i * factor + fi
                                new_col = j * factor + fj

                                # Utiliser le motif original pour remplir ce bloc
                                motif_row = fi % clip_height
                                motif_col = fj % clip_width
                                new_clipboard[new_row, new_col] = self.clipboard[motif_row, motif_col]
                                new_clipboard_mask[new_row, new_col] = True
        else:
            # Mode replace_content=False : multiplier seulement les cellules du masque
            for i in range(clip_height):
                for j in range(clip_width):
                    if effective_mask[i, j]:
                        # Cette cellule était sélectionnée, la répliquer factor x factor fois
                        for fi in range(factor):
                            for fj in range(factor):
                                new_row = i * factor + fi
                                new_col = j * factor + fj
                                new_clipboard[new_row, new_col] = self.clipboard[i, j]
                                new_clipboard_mask[new_row, new_col] = True

        # Remplacer le clipboard par la version multipliée
        self.clipboard = new_clipboard
        self.clipboard_mask = new_clipboard_mask

        return True
    '''
    
    return fixed_implementation

def test_multiply_with_coordinates():
    """Test de MULTIPLY avec coordonnées"""
    
    print("🧪 Test de MULTIPLY avec coordonnées...")
    
    # Simuler une commande MULTIPLY avec coordonnées
    test_commands = [
        "MULTIPLY 4 false",  # Sans coordonnées (comportement actuel)
        "MULTIPLY 4 false [0,0 1,1]",  # Avec coordonnées spécifiques
        "MULTIPLY 2 true [1,0]",  # Avec une cellule spécifique
        "MULTIPLY 3 false (COLOR 3 [0,0 1,1])",  # Avec sélection COLOR
    ]
    
    for cmd_str in test_commands:
        print(f"\n--- Test: {cmd_str} ---")
        
        # Analyser la commande
        if '[' in cmd_str:
            print("✅ Commande avec coordonnées détectée")
            # Extraire les coordonnées
            import re
            coord_pattern = r'\[([^\]]+)\]'
            coords = re.findall(coord_pattern, cmd_str)
            print(f"   Coordonnées trouvées: {coords}")
        else:
            print("❌ Commande sans coordonnées")
        
        # Vérifier les modificateurs
        if 'COLOR' in cmd_str:
            print("✅ Modificateur COLOR détecté")
        if 'INVERT' in cmd_str:
            print("✅ Modificateur INVERT détecté")

def analyze_current_problem():
    """Analyser le problème actuel avec MULTIPLY"""
    
    print("🔍 Analyse du problème actuel avec MULTIPLY...")
    
    print("\n📋 Problème identifié:")
    print("1. MULTIPLY n'utilise pas _parse_coordinate_blocks()")
    print("2. MULTIPLY n'utilise pas _process_coordinate_blocks()")
    print("3. MULTIPLY ne peut pas traiter les sélections spéciales (COLOR, INVERT)")
    print("4. MULTIPLY ne peut pas traiter les coordonnées multiples")
    
    print("\n📋 Solution proposée:")
    print("1. Ajouter le parsing générique des coordonnées")
    print("2. Créer un masque basé sur les coordonnées spécifiées")
    print("3. Utiliser ce masque au lieu du clipboard_mask original")
    print("4. Maintenir la compatibilité avec l'ancien comportement")
    
    print("\n📋 Avantages de la correction:")
    print("✅ Compatibilité avec le système unifié")
    print("✅ Support des sélections COLOR et INVERT")
    print("✅ Support des coordonnées multiples")
    print("✅ Rétrocompatibilité maintenue")

if __name__ == "__main__":
    analyze_current_problem()
    test_multiply_with_coordinates()
    
    print("\n" + "="*60)
    print("🔧 Implémentation corrigée générée:")
    print("="*60)
    
    fixed_code = generate_fixed_multiply_implementation()
    print(fixed_code)