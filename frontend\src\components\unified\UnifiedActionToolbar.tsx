import React, { useState, useCallback } from 'react';
import { Grid } from '../../lib/grid';
import { UnifiedSelection } from '../../types/unifiedCommands';
import { UnifiedCommandParser } from '../../services/unifiedCommandParser';
import { UnifiedCommandExecutor } from '../../services/unifiedCommandExecutor';
import { ActionButton } from './ActionButton';
import { ParameterDialog } from './ParameterDialog';
import { useInputGrid } from '../../hooks/useInputGrid';
import './UnifiedActionToolbar.css';

interface UnifiedActionToolbarProps {
  grid: Grid;
  selection: UnifiedSelection;
  onGridChange: (newGrid: Grid) => void;
  onCommandExecuted?: (command: string) => void;
}

const unifiedCommandParser = new UnifiedCommandParser();
const unifiedCommandExecutor = new UnifiedCommandExecutor();

export const UnifiedActionToolbar: React.FC<UnifiedActionToolbarProps> = ({
  grid,
  selection,
  onGridChange,
  onCommandExecuted
}) => {
  const [showParameterDialog, setShowParameterDialog] = useState<string | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);

  // Accès à la grille d'input pour la commande INPUT
  const { currentInputGrid } = useInputGrid();

  // === EXÉCUTION DE COMMANDES ===

  const executeCommand = useCallback(async (commandString: string) => {
    try {
      setIsExecuting(true);

      // Parser la commande
      const parsed = unifiedCommandParser.parseCommand(commandString);

      // Valider
      const validation = unifiedCommandParser.validateParsedCommand(parsed);
      if (!validation.isValid) {
        throw new Error(`Erreur de validation: ${validation.errors[0]?.message}`);
      }

      // Gestion spéciale pour la commande INPUT
      if (parsed.action === 'INPUT') {
        if (!currentInputGrid) {
          throw new Error('Aucune grille d\'input disponible pour la commande INPUT');
        }

        // Copier la grille d'input vers la grille de résolution
        const newGrid = currentInputGrid.clone();
        onGridChange(newGrid);
        onCommandExecuted?.(commandString);
        return;
      }

      // Exécuter les autres commandes normalement
      const result = await unifiedCommandExecutor.executeCommand(parsed, grid);

      if (result.success && result.newGrid) {
        onGridChange(result.newGrid);
        onCommandExecuted?.(commandString);
      } else {
        throw new Error(result.error || 'Erreur d\'exécution');
      }
      
    } catch (error) {
      console.error('Erreur lors de l\'exécution:', error);
      alert(`Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    } finally {
      setIsExecuting(false);
    }
  }, [grid, onGridChange, onCommandExecuted]);

  // === GÉNÉRATION DE COMMANDES ===

  const generateSelectionString = useCallback((): string => {
    if (selection.rectangles.length === 0) return '';
    
    let selectionStr = '';
    
    selection.rectangles.forEach((rect, index) => {
      const prefix = rect.isAdditional ? '+' : '';
      const rectStr = rect.startRow === rect.endRow && rect.startCol === rect.endCol
        ? `[${prefix}${rect.startRow},${rect.startCol}]`
        : `[${prefix}${rect.startRow},${rect.startCol} ${rect.endRow},${rect.endCol}]`;
      
      selectionStr += (index > 0 ? ' ' : '') + rectStr;
    });
    
    if (selection.isInverted) {
      selectionStr = `INVERSE(${selectionStr})`;
    }
    
    if (selection.colorFilter && selection.colorFilter.length > 0) {
      selectionStr = `COLOR(${selectionStr})`;
    }
    
    return selectionStr;
  }, [selection]);

  // === ACTIONS SIMPLES ===

  const handleClear = useCallback(() => {
    const selectionStr = generateSelectionString();
    if (!selectionStr) return;
    
    executeCommand(`CLEAR ${selectionStr}`);
  }, [generateSelectionString, executeCommand]);

  const handleFill = useCallback((color: number) => {
    const selectionStr = generateSelectionString();
    if (!selectionStr) return;
    
    executeCommand(`FILL ${color} ${selectionStr}`);
  }, [generateSelectionString, executeCommand]);

  const handleSurround = useCallback((color: number) => {
    const selectionStr = generateSelectionString();
    if (!selectionStr) return;
    
    executeCommand(`SURROUND ${color} ${selectionStr}`);
  }, [generateSelectionString, executeCommand]);

  const handleReplace = useCallback((sourceColors: number[], targetColor: number) => {
    const selectionStr = generateSelectionString();
    if (!selectionStr) return;
    
    const sourceStr = sourceColors.join(',');
    executeCommand(`REPLACE ${sourceStr} ${targetColor} ${selectionStr}`);
  }, [generateSelectionString, executeCommand]);

  // === TRANSFORMATIONS ===

  const handleFlip = useCallback((direction: 'H' | 'V') => {
    const selectionStr = generateSelectionString();
    if (!selectionStr) return;
    
    executeCommand(`FLIP ${direction} ${selectionStr}`);
  }, [generateSelectionString, executeCommand]);

  const handleRotate = useCallback((direction: 'L' | 'R') => {
    const selectionStr = generateSelectionString();
    if (!selectionStr) return;
    
    executeCommand(`ROTATE ${direction} ${selectionStr}`);
  }, [generateSelectionString, executeCommand]);

  // === MODIFICATIONS DE GRILLE ===

  const handleInsert = useCallback((type: 'ROWS' | 'COLUMNS', count: number, direction: string) => {
    const selectionStr = generateSelectionString();
    if (!selectionStr) return;
    
    // Format unifié : INSERT nombre ROWS/COLUMNS POSITION [coordonnées] ou ([coordonnées] [coordonnées]) pour multiples
    executeCommand(`INSERT ${count} ${type} ${direction.toUpperCase()} ${selectionStr}`);
  }, [generateSelectionString, executeCommand]);

  const handleDelete = useCallback((type: 'ROWS' | 'COLUMNS') => {
    const selectionStr = generateSelectionString();
    if (!selectionStr) return;
    
    // Format unifié : DELETE ROWS/COLUMNS [coordonnées] ou ([coordonnées] [coordonnées]) pour multiples
    executeCommand(`DELETE ${type} ${selectionStr}`);
  }, [generateSelectionString, executeCommand]);

  // === RENDU ===

  const hasSelection = selection.rectangles.length > 0;

  return (
    <div className="unified-action-toolbar">
      {/* Actions simples */}
      <div className="action-group">
        <h3>Actions</h3>
        <ActionButton
          onClick={handleClear}
          disabled={!hasSelection || isExecuting}
          title="Effacer la sélection"
          variant="danger"
        >
          CLEAR
        </ActionButton>
        
        <ActionButton
          onClick={() => setShowParameterDialog('FILL')}
          disabled={!hasSelection || isExecuting}
          title="Remplir avec une couleur"
        >
          FILL
        </ActionButton>
        
        <ActionButton
          onClick={() => setShowParameterDialog('SURROUND')}
          disabled={!hasSelection || isExecuting}
          title="Entourer la sélection"
        >
          SURROUND
        </ActionButton>
        
        <ActionButton
          onClick={() => setShowParameterDialog('REPLACE')}
          disabled={!hasSelection || isExecuting}
          title="Remplacer des couleurs"
        >
          REPLACE
        </ActionButton>
      </div>

      {/* Transformations */}
      <div className="action-group">
        <h3>Transformations</h3>
        <ActionButton
          onClick={() => handleFlip('H')}
          disabled={!hasSelection || isExecuting}
          title="Retournement horizontal"
          variant="secondary"
        >
          FLIP H
        </ActionButton>
        
        <ActionButton
          onClick={() => handleFlip('V')}
          disabled={!hasSelection || isExecuting}
          title="Retournement vertical"
          variant="secondary"
        >
          FLIP V
        </ActionButton>
        
        <ActionButton
          onClick={() => handleRotate('L')}
          disabled={!hasSelection || isExecuting}
          title="Rotation gauche 90°"
          variant="secondary"
        >
          ROTATE L
        </ActionButton>
        
        <ActionButton
          onClick={() => handleRotate('R')}
          disabled={!hasSelection || isExecuting}
          title="Rotation droite 90°"
          variant="secondary"
        >
          ROTATE R
        </ActionButton>
      </div>

      {/* Modifications de grille */}
      <div className="action-group">
        <h3>Modifications</h3>
        <ActionButton
          onClick={() => setShowParameterDialog('INSERT')}
          disabled={!hasSelection || isExecuting}
          title="Insérer lignes/colonnes"
          variant="warning"
        >
          INSERT
        </ActionButton>
        
        <ActionButton
          onClick={() => setShowParameterDialog('DELETE')}
          disabled={!hasSelection || isExecuting}
          title="Supprimer lignes/colonnes"
          variant="danger"
        >
          DELETE
        </ActionButton>
      </div>

      {/* Statut */}
      <div className="toolbar-status">
        {isExecuting && <div className="executing-indicator">Exécution...</div>}
        {hasSelection && (
          <div className="selection-summary">
            {selection.rectangles.length} zone(s) sélectionnée(s)
            {selection.isInverted && ' (INVERSE)'}
            {selection.colorFilter && ` (COULEURS: ${selection.colorFilter.join(',')})`}
          </div>
        )}
      </div>

      {/* Dialogs de paramètres */}
      {showParameterDialog && (
        <ParameterDialog
          action={showParameterDialog}
          onConfirm={(params) => {
            // Gérer les paramètres selon l'action
            switch (showParameterDialog) {
              case 'FILL':
                handleFill(params.color);
                break;
              case 'SURROUND':
                handleSurround(params.color);
                break;
              case 'REPLACE':
                handleReplace(params.sourceColors, params.targetColor);
                break;
              case 'INSERT':
                handleInsert(params.type, params.count, params.direction);
                break;
              case 'DELETE':
                handleDelete(params.type);
                break;
            }
            setShowParameterDialog(null);
          }}
          onCancel={() => setShowParameterDialog(null)}
        />
      )}
    </div>
  );
};
