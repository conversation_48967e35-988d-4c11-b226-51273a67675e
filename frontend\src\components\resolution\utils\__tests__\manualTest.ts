/**
 * Test manuel pour vérifier le scénario problématique
 */

import { parseUnifiedCommand } from '../commandGenerationUtils';

console.log('🧪 Test du scénario problématique');

// Test de la commande qui ne fonctionnait pas
const command = 'DELETE COLUMNS ([0,1 3,1] [0,3 3,5])';
console.log(`\n📝 Commande: ${command}`);

const parsed = parseUnifiedCommand(command);
console.log('\n✅ Résultat du parsing:');
console.log('- Action:', parsed.action);
console.log('- Paramètres:', parsed.parameters);
console.log('- Blocs de coordonnées:', JSON.stringify(parsed.coordinateBlocks, null, 2));

// Vérification que les blocs sont préservés
if (parsed.coordinateBlocks && parsed.coordinateBlocks.length === 2) {
    console.log('\n🎯 Structure préservée:');
    console.log('- Bloc 1:', parsed.coordinateBlocks[0], '→ Rectangle de (0,1) à (3,1)');
    console.log('- Bloc 2:', parsed.coordinateBlocks[1], '→ Rectangle de (0,3) à (3,5)');
    console.log('\n✅ SUCCESS: Les blocs sont correctement séparés !');
} else {
    console.log('\n❌ ERREUR: Les blocs ne sont pas correctement préservés');
}

// Test de la commande qui fonctionnait
console.log('\n\n🧪 Test des commandes séparées (qui fonctionnaient)');

const command1 = 'DELETE COLUMNS [0,1 3,1]';
const command2 = 'DELETE COLUMNS [0,3 3,5]';

console.log(`\n📝 Commande 1: ${command1}`);
const parsed1 = parseUnifiedCommand(command1);
console.log('- Blocs:', JSON.stringify(parsed1.coordinateBlocks));

console.log(`\n📝 Commande 2: ${command2}`);
const parsed2 = parseUnifiedCommand(command2);
console.log('- Blocs:', JSON.stringify(parsed2.coordinateBlocks));

console.log('\n🎉 Test terminé !');