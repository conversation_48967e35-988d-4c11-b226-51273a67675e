const j={sendMessageStream:(o,e)=>{if(!localStorage.getItem("token"))return e.onError&&e.onError("Non authentifié"),()=>{};let E="",c="",d=!1,y=!1;const S=new AbortController,b=S.signal,C=()=>{S.abort(),y=!0},x="http://localhost:11434/api/generate";if(o.model_id&&!o.model_id.startsWith("175e")&&!o.model_id.startsWith("4550")){const a="http://localhost:8000",s=localStorage.getItem("token");if(!s)return e.onError&&e.onError("Non authentifié"),()=>{};const g=new AbortController,t=g.signal;return fetch(`${a}/api/chat/message/`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Token ${s}`},body:JSON.stringify(o),signal:t}).then(async n=>{if(!n.ok){const l=await n.text();throw new Error(`Erreur HTTP: ${n.status} - ${l}`)}return n.json()}).then(n=>{var l;if(!n.success)throw new Error(((l=n.error)==null?void 0:l.message)||"Erreur lors de l'appel au modèle externe");if(e.onThinkingStart&&n.data.thinking&&(e.onThinkingStart("Analyse en cours..."),setTimeout(()=>{e.onThinkingUpdate&&e.onThinkingUpdate(n.data.thinking),e.onThinkingEnd&&e.onThinkingEnd(n.data.thinking)},500)),e.onContentUpdate){const i=n.data.response.split(" ");let r=0;const T=setInterval(()=>{var h;if(r<i.length){const u=i[r]+" ";(h=e.onContentUpdate)==null||h.call(e,u),r++}else clearInterval(T),e.onComplete&&e.onComplete(n.data.response,n.data.thinking||null,n.data.commands||[])},50)}}).catch(n=>{e.onError&&e.onError(n.message||"Erreur lors de l'appel au modèle externe")}),()=>{g.abort()}}let w="qwen3:14b";const p={"175e3142-0000-4000-8000-000000000000":"qwen3:14b","175e253f-4521-4200-a000-000000000000":"qwen2.5-coder:14b","4550355b-2132-4000-9000-000000000000":"deepseek-r1:32b"};o.model_id&&p[o.model_id]&&(w=p[o.model_id]);const U={model:w,prompt:o.messages[o.messages.length-1].content,stream:!0,system:o.system_prompt||"Vous êtes un assistant spécialisé dans la résolution de puzzles ARC (Abstraction and Reasoning Corpus). Vous aidez à analyser et résoudre ces puzzles en générant des commandes d'automatisation."};fetch(x,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(U),signal:b}).then(a=>{var t;if(!a.ok)throw new Error(`Erreur HTTP: ${a.status}`);const s=(t=a.body)==null?void 0:t.getReader();if(!s)throw new Error("Impossible de lire la réponse");(async()=>{var n;try{for(e.onThinkingStart&&(c="Analyse de la demande...",d=!0,e.onThinkingStart(c));;){const{done:i,value:r}=await s.read();if(i||y)break;const h=new TextDecoder().decode(r).split(`
`).filter(u=>u.trim()!=="");for(const u of h)try{const k=JSON.parse(u);if(k.done&&d&&e.onThinkingEnd&&(d=!1,e.onThinkingEnd(c)),k.response){const f=k.response;if(f.includes("<think>")){!d&&e.onThinkingStart&&(d=!0,c="",e.onThinkingStart("Analyse en cours..."));const m=f.replace("<think>","");m&&e.onThinkingUpdate&&(c+=m,e.onThinkingUpdate(m))}else if(f.includes("</think>")){if(d&&e.onThinkingEnd){const m=f.replace("</think>","");m&&(c+=m,(n=e.onThinkingUpdate)==null||n.call(e,m)),d=!1,e.onThinkingEnd(c)}}else d?(c+=f,e.onThinkingUpdate&&e.onThinkingUpdate(f)):(E+=f,e.onContentUpdate&&e.onContentUpdate(f))}}catch(k){console.error(k)}}d&&e.onThinkingEnd&&(d=!1,e.onThinkingEnd(c));const l=A(E);e.onComplete&&e.onComplete(E,c,l)}catch(l){e.onError&&e.onError(l instanceof Error?l.message:"Erreur lors du traitement du stream")}})()}).catch(a=>{e.onError&&e.onError(a instanceof Error?a.message:"Erreur lors de l'initialisation de la requête")});const A=a=>{const s=[],g=/```commands\s*([\s\S]*?)```/g;let t;for(;(t=g.exec(a))!==null;)if(t[1]){const i=t[1].trim().split(`
`);s.push(...i.filter(r=>r.trim()!==""))}const n=/```python\s*([\s\S]*?)```/g;for(;(t=n.exec(a))!==null;)if(t[1]){const r=t[1].trim().split(`
`).filter(T=>{const h=T.trim();return h.startsWith("set_cell")||h.startsWith("fill_region")||h.startsWith("draw_line")||h.startsWith("copy_region")});s.push(...r)}const l=/```json\s*([\s\S]*?)```/g;for(;(t=l.exec(a))!==null;)if(t[1])try{const i=JSON.parse(t[1]);Array.isArray(i)&&i.forEach(r=>{typeof r=="string"?s.push(r):typeof r=="object"&&s.push(JSON.stringify(r))})}catch(i){console.error(i)}return s};return C}};export{j as default};
