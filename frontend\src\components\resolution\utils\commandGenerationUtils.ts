export interface UnifiedCommand {
    action: string;
    parameters?: string;
    coordinateBlocks: string[][]; // SEUL système de coordonnées - structure sémantique préservée
    isMotif: boolean;
    isSpecialSelection: boolean;
    executionType?: string;
    subcommands?: UnifiedCommand[];
    specialSelection?: {
        type: string;
        parameters?: string;
    };
    raw?: string;
}

/**
 * Alias pour UnifiedCommand pour compatibilité
 */
export type UnifiedPlaybackCommand = UnifiedCommand;

/**
 * Détermine le type d'exécution d'une commande
 */
function getExecutionType(_action: string, isMotif: boolean, isSpecialSelection: boolean): string {
    if (isMotif) return 'motif';
    if (isSpecialSelection) return 'special_selection';
    return 'standard';
}

/**
 * Parse les coordonnées d'une chaîne selon le format unifié
 * Format: "ligne,col" pour cellule ou "ligne1,col1 ligne2,col2" pour rectangle
 */
export function parseCoordinates(coordStr: string): string[] {
    if (!coordStr || coordStr.trim() === '') return [];

    // Nettoyer la chaîne en supprimant seulement les crochets
    const cleaned = coordStr.trim().replace(/[[\]]/g, '');

    // Si la chaîne contient un espace, séparer les coordonnées
    if (cleaned.includes(' ')) {
        return cleaned.split(' ').filter(coord => coord.trim() !== '');
    } else {
        return [cleaned]; // Cellule: "ligne,col"
    }
}

/**
 * Parse les blocs de coordonnées en préservant leur structure sémantique
 * Fonction générique unique pour toutes les commandes
 * 
 * @param coordContent - Contenu des coordonnées: "([0,1 3,1] [0,3 3,5])" ou "[1,2 3,4]"
 * @returns Array de blocs, chaque bloc étant un array de coordonnées
 */
export function parseCoordinateBlocks(coordContent: string): string[][] {
    if (!coordContent || coordContent.trim() === '') return [];

    const cleaned = coordContent.trim();

    // Cas 1: Coordonnées multiples avec parenthèses: ([coord1] [coord2])
    if (cleaned.startsWith('(') && cleaned.endsWith(')')) {
        const inner = cleaned.slice(1, -1); // Enlever les parenthèses
        const blockRegex = /\[([^\]]+)\]/g;
        const blocks: string[][] = [];
        let match;

        while ((match = blockRegex.exec(inner)) !== null) {
            blocks.push(parseCoordinates(match[1]));
        }

        return blocks;
    }

    // Cas 2: Coordonnées simples avec crochets: [coord1 coord2]
    if (cleaned.startsWith('[') && cleaned.endsWith(']')) {
        const inner = cleaned.slice(1, -1); // Enlever les crochets
        return [parseCoordinates(inner)];
    }

    // Cas 3: Coordonnées sans délimiteurs (fallback)
    return [parseCoordinates(cleaned)];
}

// SUPPRIMÉ - formatUnifiedCommand obsolète (utilisait l'ancien format aplati)

// SUPPRIMÉ - formatMotifCommand obsolète (utilisait l'ancien format aplati)

/**
 * Parse une commande unifiée selon les formats de la documentation
 */
export function parseUnifiedCommand(command: string): UnifiedCommand {
    console.log(`[DEBUG parseUnifiedCommand] Commande à parser: ${command}`);
    const cleanCommand = command.trim().replace(/\r/g, '');

    // Pattern pour TRANSFERT MOTIF avec accolades
    const transfertPattern = /^(?:TRANSFERT)\s*\{([^}]+)\}$/;
    const motifPattern = /^(?:MOTIF)\s*\{([^}]+)\}$/;

    // Pattern pour commandes avec plusieurs blocs de coordonnées: ACTION params [coord1] [coord2] ...
    //const multipleBlocksPattern = /^([A-Z_]+)\s+([^[\]]+?)\s*((?:\[[^\]]+\]\s*)+)$/;

    // Pattern pour commandes avec sélections spéciales intégrées: ACTION params (SELECT (COLOR|INVERT ...)
    //const integratedSpecialPattern = /^(\w+)\s+([^(]*?)\s*\((SELECT (?:COLOR|INVERT))\s+([^)]+)\)$/;

    // Pattern pour commandes avec sélections multiples: ACTION params ([coord1] [coord2])
    const multiplePattern = /^(\w+)\s+([^(]+?)\s*\(([^)]+)\)$/;

    // Pattern pour commandes simples: ACTION params [coord]
    const simplePattern = /^([A-Z_]+)\s+(.*?)\s+\[([^\]]+)\]$/;

    // Pattern pour commandes simples SANS paramètres: ACTION [coord]
    const simpleNoParamsPattern = /^([A-Z_]+)\s+\[([^\]]+)\]$/;

    // Pattern pour commandes sans coordonnées: ACTION params
    const noCoordPattern = /^(\w+)(?:\s+(.+))?$/;



    // Tenter le format TRANSFERT avec accolades
    const transfertMatch = cleanCommand.match(transfertPattern);
    if (transfertMatch) {
        const isMotif = false;
        const isSpecialSelection = false;

        return {
            action: 'TRANSFERT',
            parameters: transfertMatch[1],
            coordinateBlocks: [],
            isMotif,
            isSpecialSelection,
            executionType: getExecutionType('TRANSFERT', isMotif, isSpecialSelection),
            raw: cleanCommand
        };
    }

    // Tenter le format MOTIF avec accolades
    const motifMatch = cleanCommand.match(motifPattern);
    if (motifMatch) {
        const isMotif = true;
        const isSpecialSelection = false;

        return {
            action: 'MOTIF',
            parameters: motifMatch[1],
            coordinateBlocks: [],
            isMotif,
            isSpecialSelection,
            executionType: getExecutionType('MOTIF', isMotif, isSpecialSelection),
            raw: cleanCommand
        };
    }

    // Tenter le nouveau format COLOR: (COLOR paramètres ([coord1] [coord2]))
    const colorPattern = /^\(COLOR\s+([^(]+)\s*\(([^)]+)\)\)$/;
    const colorMatch = cleanCommand.match(colorPattern);
    if (colorMatch) {
        const params = colorMatch[1].trim();
        const coordsContent = colorMatch[2];
        const blockRegex = /\[([^\]]+)\]/g;
        const blocks = [];
        let match;
        while ((match = blockRegex.exec(coordsContent)) !== null) {
            blocks.push(match[1]);
        }

        const coordinateBlocks: string[][] = [];
        for (const block of blocks) {
            coordinateBlocks.push(parseCoordinates(block));
        }

        return {
            action: 'COLOR',
            parameters: params,
            coordinateBlocks,
            isMotif: false,
            isSpecialSelection: true,
            raw: cleanCommand
        };
    }

    // Tenter le nouveau format INVERT: (INVERT ([coord1] [coord2]))
    const invertPattern = /^\(INVERT\s*\(([^)]+)\)\)$/;
    const invertMatch = cleanCommand.match(invertPattern);
    if (invertMatch) {
        console.log('invertMatch:', invertMatch);
        const coordsContent = invertMatch[1];
        const blockRegex = /\[([^\]]+)\]/g;
        const blocks = [];
        let match;
        while ((match = blockRegex.exec(coordsContent)) !== null) {
            blocks.push(match[1]);
        }

        const coordinateBlocks: string[][] = [];
        for (const block of blocks) {
            coordinateBlocks.push(parseCoordinates(block));
        }

        return {
            action: 'INVERT',
            parameters: undefined,
            coordinateBlocks,
            isMotif: false,
            isSpecialSelection: true,
            raw: cleanCommand
        };
    }

    // Tenter le nouveau format avec modificateurs intégrés: ACTION params (INVERT (...)) ou ACTION params (COLOR ...) ou ACTION params (INVERT [...]) ou ACTION params (COLOR [...])
    const newIntegratedPattern = /^(\w+)\s+([^(]*?)\s*\(((?:INVERT|COLOR).*)\)$/;
    const newIntegratedMatch = cleanCommand.match(newIntegratedPattern);
    if (newIntegratedMatch) {
        const action = newIntegratedMatch[1].trim();
        const params = newIntegratedMatch[2].trim();
        const modifierContent = newIntegratedMatch[3];

        // Parser les coordonnées selon le modificateur
        const coordinateBlocks: string[][] = [];

        // Parser INVERT: INVERT ([coords]) ou INVERT [coords]
        const invertMatchDouble = modifierContent.match(/^INVERT\s*\(([^)]+)\)$/);
        const invertMatchSingle = modifierContent.match(/^INVERT\s*\[([^\]]+)\]$/);

        if (invertMatchDouble) {
            const coordsContent = invertMatchDouble[1];
            const blockRegex = /\[([^\]]+)\]/g;
            const blocks = [];
            let match;
            while ((match = blockRegex.exec(coordsContent)) !== null) {
                blocks.push(match[1]);
            }

            for (const block of blocks) {
                coordinateBlocks.push(parseCoordinates(block));
            }
        } else if (invertMatchSingle) {
            coordinateBlocks.push(parseCoordinates(invertMatchSingle[1]));
        }

        // Parser COLOR: COLOR params ([coords]) ou COLOR params [coords]
        const colorMatchDouble = modifierContent.match(/^COLOR\s+([^(]+)\s*\(([^)]+)\)$/);
        const colorMatchSingle = modifierContent.match(/^COLOR\s+(.+?)\s+\[([^\]]+)\]$/);

        if (colorMatchDouble) {
            const coordsContent = colorMatchDouble[2];
            const blockRegex = /\[([^\]]+)\]/g;
            const blocks = [];
            let match;
            while ((match = blockRegex.exec(coordsContent)) !== null) {
                blocks.push(match[1]);
            }

            for (const block of blocks) {
                coordinateBlocks.push(parseCoordinates(block));
            }
        } else if (colorMatchSingle) {
            coordinateBlocks.push(parseCoordinates(colorMatchSingle[2]));
        }

        const isMotif = false;
        const isSpecialSelection = false;

        return {
            action,
            parameters: params || undefined,
            coordinateBlocks,
            isMotif,
            isSpecialSelection,
            executionType: getExecutionType(action, isMotif, isSpecialSelection),
            raw: cleanCommand
        };
    }

    // Tenter le format avec sélections multiples: ACTION params ([coord1] [coord2])
    const multipleMatch = cleanCommand.match(multiplePattern);
    if (multipleMatch) {
        const action = multipleMatch[1].trim();
        const params = multipleMatch[2].trim();
        const coordContent = `(${multipleMatch[3]})`;

        // Utiliser la fonction générique de parsing des blocs
        const coordinateBlocks = parseCoordinateBlocks(coordContent);

        // Déterminer si c'est une commande motif (opère sur le presse-papier sans coordonnées)
        const isMotif = ['FLIP', 'ROTATE', 'MULTIPLY', 'DIVIDE'].includes(action);

        const isSpecialSelection = false;

        return {
            action,
            parameters: params || undefined,
            coordinateBlocks,
            isMotif,
            isSpecialSelection,
            executionType: getExecutionType(action, isMotif, isSpecialSelection),
            raw: cleanCommand
        };
    }

    // Tenter le format simple: ACTION params [coord]
    const simpleMatch = cleanCommand.match(simplePattern);
    if (simpleMatch) {
        const action = simpleMatch[1].trim();
        const params = simpleMatch[2].trim();
        const coordContent = `[${simpleMatch[3]}]`;

        // Utiliser la fonction générique de parsing des blocs
        const coordinateBlocks = parseCoordinateBlocks(coordContent);

        const isMotif = false;
        const isSpecialSelection = false;

        return {
            action,
            parameters: params || undefined,
            coordinateBlocks,
            isMotif,
            isSpecialSelection,
            executionType: getExecutionType(action, isMotif, isSpecialSelection),
            raw: cleanCommand
        };
    }

    // Tenter le format simple SANS paramètres: ACTION [coord]
    const simpleNoParamsMatch = cleanCommand.match(simpleNoParamsPattern);
    if (simpleNoParamsMatch) {
        const action = simpleNoParamsMatch[1].trim();
        const coordContent = `[${simpleNoParamsMatch[2]}]`;

        // Utiliser la fonction générique de parsing des blocs
        const coordinateBlocks = parseCoordinateBlocks(coordContent);

        const isMotif = false;
        const isSpecialSelection = false;

        return {
            action,
            parameters: undefined,
            coordinateBlocks,
            isMotif,
            isSpecialSelection,
            executionType: getExecutionType(action, isMotif, isSpecialSelection),
            raw: cleanCommand
        };
    }

    // Format sans coordonnées: ACTION params
    const noCoordMatch = cleanCommand.match(noCoordPattern);
    if (noCoordMatch) {
        const action = noCoordMatch[1].trim();
        const params = noCoordMatch[2] ? noCoordMatch[2].trim() : '';

        const isMotif = false;
        const isSpecialSelection = false;

        return {
            action,
            parameters: params || undefined,
            coordinateBlocks: [],
            isMotif,
            isSpecialSelection,
            executionType: getExecutionType(action, isMotif, isSpecialSelection),
            raw: cleanCommand
        };
    }

    // Format par défaut
    return {
        action: cleanCommand,
        parameters: undefined,
        coordinateBlocks: [],
        isMotif: false,
        isSpecialSelection: false,
        executionType: getExecutionType(cleanCommand, false, false),
        raw: cleanCommand
    };
}

/**
 * Parse une commande pour la lecture (playback)
 */
export function parseForPlayback(command: string): UnifiedCommand {
    return parseUnifiedCommand(command);
}

/**
 * Alias pour parseForPlayback pour compatibilité
 */
export async function parseUnifiedCommandForPlayback(command: string): Promise<UnifiedCommand> {
    // Importer parseGroupedCommand dynamiquement pour éviter les dépendances circulaires
    const { parseGroupedCommand } = await import('./commandGroupingUtils');

    // Vérifier si c'est une commande groupée
    const groupedResult = parseGroupedCommand(command);
    if (groupedResult) {
        // Créer des sous-commandes avec la propriété raw
        const subcommands: UnifiedCommand[] = groupedResult.commands.map((cmd: string) => {
            const parsed = parseUnifiedCommand(cmd);
            parsed.raw = cmd;
            return parsed;
        });

        return {
            action: groupedResult.groupType,
            parameters: groupedResult.commands.join('; '),
            coordinateBlocks: [],
            isMotif: false,
            isSpecialSelection: false,
            executionType: 'grouped',
            subcommands,
            raw: command
        };
    }

    // Commande normale
    const result = parseUnifiedCommand(command);
    result.raw = command;
    return result;
}

// SUPPRIMÉ - groupCoordinates obsolète (utilisait l'ancien format aplati)